{"ast": null, "code": "import React,{useState,useEffect}from'react';import axios from'axios';import{Container,Row,Col,Card,Tabs,Tab,Form,Button}from'react-bootstrap';import Plot from'react-plotly.js';import ChartContainer from'../components/ChartContainer';import DefectMap from'../components/DefectMap';import'./dashboard.css';import jsPDF from'jspdf';import autoTable from'jspdf-autotable';import*as XLSX from'xlsx';import{saveAs}from'file-saver';/**\r\n * Comprehensive Image URL Resolution Logic\r\n * Handles both S3 URLs (new data) and GridFS IDs (legacy data)\r\n *\r\n * Priority order:\r\n * 1. S3 Full URL (direct HTTPS link)\r\n * 2. S3 Key (generate URL via API)\r\n * 3. GridFS ID (legacy endpoint)\r\n * 4. Fallback to \"No image available\"\r\n */import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const getImageUrlForDisplay=function(imageData){let imageType=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'original';console.log('getImageUrlForDisplay called:',{imageData,imageType});if(!imageData){console.log('No imageData provided');return null;}// Try S3 full URL first (new images with pre-generated URLs) - proxy through backend\nconst fullUrlField=`${imageType}_image_full_url`;if(imageData[fullUrlField]){console.log('Using full URL field:',fullUrlField,imageData[fullUrlField]);// Extract S3 key from full URL and use proxy endpoint\nconst urlParts=imageData[fullUrlField].split('/');const bucketIndex=urlParts.findIndex(part=>part.includes('.s3.'));if(bucketIndex!==-1&&bucketIndex+1<urlParts.length){const s3Key=urlParts.slice(bucketIndex+1).join('/');const proxyUrl=`/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;console.log('Generated proxy URL from full URL:',proxyUrl);return proxyUrl;}}// Try S3 key with proxy endpoint (new images without full URL)\nconst s3KeyField=`${imageType}_image_s3_url`;if(imageData[s3KeyField]){console.log('Using S3 key field:',s3KeyField,imageData[s3KeyField]);// Properly encode the S3 key for URL path\nconst s3Key=imageData[s3KeyField];const encodedKey=s3Key.split('/').map(part=>encodeURIComponent(part)).join('/');const url=`/api/pavement/get-s3-image/${encodedKey}`;console.log('Generated proxy URL from S3 key:',url);console.log('Original S3 key:',s3Key);console.log('Encoded S3 key:',encodedKey);return url;}// Fall back to GridFS endpoint (legacy images)\nconst gridfsIdField=`${imageType}_image_id`;if(imageData[gridfsIdField]){console.log('Using GridFS field:',gridfsIdField,imageData[gridfsIdField]);const url=`/api/pavement/get-image/${imageData[gridfsIdField]}`;console.log('Generated GridFS URL:',url);return url;}// No image URL available\nconsole.log('No image URL available for:',imageType,imageData);return null;};/**\r\n * Enhanced Image Component with comprehensive error handling\r\n * Supports S3 URLs, GridFS fallback, and graceful error handling\r\n */const EnhancedImageDisplay=_ref=>{let{imageData,imageType='original',alt,className,style,onError}=_ref;const[currentImageUrl,setCurrentImageUrl]=useState(null);const[hasError,setHasError]=useState(false);const[fallbackAttempts,setFallbackAttempts]=useState(0);useEffect(()=>{// Reset state when imageData changes\nsetHasError(false);setFallbackAttempts(0);// Get initial image URL\nconst imageUrl=getImageUrlForDisplay(imageData,imageType);// Debug logging\nconsole.log('EnhancedImageDisplay Debug:',{imageType,imageData,generatedUrl:imageUrl,s3KeyField:`${imageType}_image_s3_url`,s3KeyValue:imageData===null||imageData===void 0?void 0:imageData[`${imageType}_image_s3_url`],fullUrlField:`${imageType}_image_full_url`,fullUrlValue:imageData===null||imageData===void 0?void 0:imageData[`${imageType}_image_full_url`]});setCurrentImageUrl(imageUrl);},[imageData,imageType]);const handleImageError=event=>{var _event$target,_event$target2,_event$target3,_event$target4,_event$target5;console.error('🚨 Image load error:',{imageType,currentImageUrl,fallbackAttempts,error:event===null||event===void 0?void 0:(_event$target=event.target)===null||_event$target===void 0?void 0:_event$target.error,src:event===null||event===void 0?void 0:(_event$target2=event.target)===null||_event$target2===void 0?void 0:_event$target2.src,naturalWidth:event===null||event===void 0?void 0:(_event$target3=event.target)===null||_event$target3===void 0?void 0:_event$target3.naturalWidth,naturalHeight:event===null||event===void 0?void 0:(_event$target4=event.target)===null||_event$target4===void 0?void 0:_event$target4.naturalHeight,complete:event===null||event===void 0?void 0:(_event$target5=event.target)===null||_event$target5===void 0?void 0:_event$target5.complete});// Test if the URL is reachable\nif(currentImageUrl){fetch(currentImageUrl,{method:'HEAD'}).then(response=>{console.log('🔍 URL HEAD check:',{url:currentImageUrl,status:response.status,statusText:response.statusText,headers:Object.fromEntries(response.headers.entries())});}).catch(fetchError=>{console.error('🚨 URL HEAD check failed:',{url:currentImageUrl,error:fetchError.message});});}if(fallbackAttempts===0){// First error: try alternative image type or fallback\nconst fallbackUrl=getFallbackImageUrl(imageData,imageType);console.log('🔄 Trying fallback URL:',fallbackUrl);if(fallbackUrl&&fallbackUrl!==currentImageUrl){setCurrentImageUrl(fallbackUrl);setFallbackAttempts(1);return;}}// All fallbacks failed\nconsole.error('❌ All image loading attempts failed for:',imageType);setHasError(true);if(onError)onError();};const getFallbackImageUrl=(imageData,imageType)=>{console.log('🔄 Getting fallback URL for:',imageType,imageData);// Try direct S3 URL if we have the full URL field\nconst fullUrlField=`${imageType}_image_full_url`;if(imageData[fullUrlField]){console.log('🔄 Trying direct S3 URL:',imageData[fullUrlField]);return imageData[fullUrlField];}// Try GridFS if S3 failed\nconst gridfsIdField=`${imageType}_image_id`;if(imageData[gridfsIdField]){console.log('🔄 Trying GridFS URL:',imageData[gridfsIdField]);return`/api/pavement/get-image/${imageData[gridfsIdField]}`;}// Try alternative S3 proxy with different encoding\nconst s3KeyField=`${imageType}_image_s3_url`;if(imageData[s3KeyField]){console.log('🔄 Trying alternative S3 proxy encoding');const s3Key=imageData[s3KeyField];const alternativeUrl=`/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;console.log('🔄 Alternative proxy URL:',alternativeUrl);return alternativeUrl;}console.log('❌ No fallback URL available');return null;};if(hasError||!currentImageUrl){return/*#__PURE__*/_jsx(\"div\",{className:`text-muted d-flex align-items-center justify-content-center ${className}`,style:style,children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-image-slash fa-2x mb-2\"}),/*#__PURE__*/_jsx(\"div\",{children:\"No image available\"})]})});}return/*#__PURE__*/_jsx(\"div\",{className:\"position-relative\",children:/*#__PURE__*/_jsx(\"img\",{src:currentImageUrl,alt:alt,className:className,style:style,onError:handleImageError,loading:\"lazy\",onLoad:()=>{console.log('✅ Image loaded successfully:',currentImageUrl);setHasError(false);}})});};// ImageCard component to isolate state for each image\nconst ImageCard=_ref2=>{let{defect,defectType,defectIdKey}=_ref2;const[isOriginal,setIsOriginal]=useState(false);// Safety check: return null if defect is not provided\nif(!defect){return null;}const toggleView=showOriginal=>{setIsOriginal(showOriginal);};// Check if this is a multi-defect image\nconst isMultiDefect=defect.detected_defects&&defect.detected_defects.length>1;const detectedDefects=defect.detected_defects||[];return/*#__PURE__*/_jsx(Col,{md:4,className:\"mb-4\",children:/*#__PURE__*/_jsxs(Card,{className:`h-100 shadow-sm ${isMultiDefect?'border-warning':''}`,children:[/*#__PURE__*/_jsxs(Card.Header,{className:isMultiDefect?'bg-warning bg-opacity-10':'',children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center\",children:[/*#__PURE__*/_jsx(\"h6\",{className:\"mb-0\",children:defectType==='cracks'?`${defect.crack_type||'Crack'} #${defect.crack_id||'N/A'}`:defectType==='kerbs'?`${defect.condition||'Kerb'} #${defect.kerb_id||'N/A'}`:`Pothole #${defect.pothole_id||'N/A'}`}),isMultiDefect&&/*#__PURE__*/_jsx(\"small\",{className:\"text-warning fw-bold\",children:\"\\uD83D\\uDD00 Multi-Defect\"})]}),isMultiDefect&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-1\",children:/*#__PURE__*/_jsxs(\"small\",{className:\"text-muted\",children:[\"Also contains: \",detectedDefects.filter(d=>d!==defectType).join(', ')]})})]}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-2 text-center\",children:/*#__PURE__*/_jsx(EnhancedImageDisplay,{imageData:defect,imageType:isOriginal?'original':'processed',alt:`${defectType==='cracks'?'Crack':defectType==='kerbs'?'Kerb':'Pothole'} ${defect[defectIdKey]}`,className:\"img-fluid mb-2 border\",style:{maxHeight:\"200px\"},onError:()=>{console.warn(`Failed to load ${isOriginal?'original':'processed'} image for ${defectType} ${defect[defectIdKey]}`);}})}),/*#__PURE__*/_jsxs(\"div\",{className:\"small\",children:[defectType==='potholes'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Area:\"}),\" \",defect.area_cm2?defect.area_cm2.toFixed(2):'N/A',\" cm\\xB2\"]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Depth:\"}),\" \",defect.depth_cm?defect.depth_cm.toFixed(2):'N/A',\" cm\"]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Volume:\"}),\" \",defect.volume?defect.volume.toFixed(2):'N/A']})]}),defectType==='cracks'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Type:\"}),\" \",defect.crack_type||'N/A']}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Area:\"}),\" \",defect.area_cm2?defect.area_cm2.toFixed(2):'N/A',\" cm\\xB2\"]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Range:\"}),\" \",defect.area_range||'N/A']})]}),defectType==='kerbs'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Type:\"}),\" \",defect.kerb_type||'N/A']}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Length:\"}),\" \",defect.length_m?defect.length_m.toFixed(2):'N/A',\" m\"]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Condition:\"}),\" \",defect.condition||'N/A']})]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Uploaded by:\"}),\" \",defect.username||'Unknown']}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Timestamp:\"}),\" \",defect.timestamp?new Date(defect.timestamp).toLocaleString('en-IN',{timeZone:'Asia/Kolkata'}):'N/A']}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-2\",children:[/*#__PURE__*/_jsx(Button,{variant:isOriginal?'primary':'outline-primary',size:\"sm\",className:\"me-2\",onClick:()=>toggleView(true),children:\"Original\"}),/*#__PURE__*/_jsx(Button,{variant:!isOriginal?'success':'outline-success',size:\"sm\",onClick:()=>toggleView(false),children:\"Processed\"})]})]})]})]})},`${defectType}-${defect[defectIdKey]||defect.image_id||Math.random()}`);};// VideoCard component for displaying processed videos with representative frames\nconst VideoCard=_ref3=>{let{video}=_ref3;// State for download progress - must be declared before any conditional logic\nconst[downloadProgress,setDownloadProgress]=useState({});const[downloadComplete,setDownloadComplete]=useState({});// Safety check: return null if video is not provided\nif(!video){return null;}const handleDownload=async videoType=>{try{// Reset states\nsetDownloadProgress(prev=>({...prev,[videoType]:0}));setDownloadComplete(prev=>({...prev,[videoType]:false}));// Use the MongoDB _id for the download endpoint\nconst videoId=video._id||video.video_id;const downloadUrl=`/api/pavement/get-s3-video/${videoId}/${videoType}`;console.log(`🔄 Starting ${videoType} video download for ID: ${videoId}`);// Fetch the video data with progress tracking\nconst response=await fetch(downloadUrl,{method:'GET',headers:{'Accept':'video/mp4, video/*, */*'}});if(!response.ok){const errorData=await response.json().catch(()=>({message:'Unknown error'}));throw new Error(errorData.message||`HTTP ${response.status}: ${response.statusText}`);}// Get content length for progress calculation\nconst contentLength=response.headers.get('content-length');const total=parseInt(contentLength,10);let loaded=0;// Create a readable stream to track progress\nconst reader=response.body.getReader();const chunks=[];while(true){const{done,value}=await reader.read();if(done)break;chunks.push(value);loaded+=value.length;// Update progress\nif(total){const progress=Math.round(loaded/total*100);setDownloadProgress(prev=>({...prev,[videoType]:progress}));}}// Create blob from chunks\nconst videoBlob=new Blob(chunks);console.log(`✅ Downloaded ${videoType} video blob - Size: ${videoBlob.size} bytes`);// Force the blob to be treated as video/mp4 if it's not already\nlet finalBlob=videoBlob;if(videoBlob.type!=='video/mp4'){finalBlob=new Blob([videoBlob],{type:'video/mp4'});console.log(`🔄 Converted blob type to 'video/mp4'`);}// Create a blob URL and trigger download\nconst blobUrl=URL.createObjectURL(finalBlob);// Use actual S3 filename if available, otherwise generate one\nlet filename;if(videoType==='original'&&video.original_video_url){filename=video.original_video_url.split('/').pop();}else if(videoType==='processed'&&video.processed_video_url){filename=video.processed_video_url.split('/').pop();}else{filename=`${videoType}_video_${(video.video_id||videoId).substring(0,8)}.mp4`;}console.log(`📁 Download filename: ${filename}`);console.log(`🔗 Blob URL created: ${blobUrl.substring(0,50)}...`);// Create and trigger download link\nconst link=document.createElement('a');link.href=blobUrl;link.download=filename;link.style.display='none';document.body.appendChild(link);console.log(`🖱️ Triggering download click for ${filename}`);// Try to trigger the download\ntry{link.click();console.log(`✅ Download triggered successfully`);// Mark download as complete\nsetDownloadComplete(prev=>({...prev,[videoType]:true}));setDownloadProgress(prev=>({...prev,[videoType]:100}));// Show completion notification and reset progress after delay\nsetTimeout(()=>{alert(`✅ ${videoType.charAt(0).toUpperCase()+videoType.slice(1)} video download completed: ${filename}`);// Reset progress after notification\nsetTimeout(()=>{setDownloadProgress(prev=>({...prev,[videoType]:0}));setDownloadComplete(prev=>({...prev,[videoType]:false}));},3000);},500);}catch(clickError){console.warn(`⚠️ Click failed, trying alternative method:`,clickError);// Fallback: try using window.open\ntry{const newWindow=window.open(blobUrl,'_blank');if(newWindow){newWindow.document.title=filename;console.log(`✅ Opened in new window as fallback`);// Mark download as complete\nsetDownloadComplete(prev=>({...prev,[videoType]:true}));setDownloadProgress(prev=>({...prev,[videoType]:100}));setTimeout(()=>{alert(`✅ ${videoType.charAt(0).toUpperCase()+videoType.slice(1)} video download completed: ${filename}`);// Reset progress after notification\nsetTimeout(()=>{setDownloadProgress(prev=>({...prev,[videoType]:0}));setDownloadComplete(prev=>({...prev,[videoType]:false}));},3000);},500);}else{throw new Error('Popup blocked');}}catch(windowError){console.warn(`⚠️ Window.open failed, trying direct navigation:`,windowError);// Last resort: direct navigation\nwindow.location.href=blobUrl;// Mark download as complete\nsetDownloadComplete(prev=>({...prev,[videoType]:true}));setDownloadProgress(prev=>({...prev,[videoType]:100}));setTimeout(()=>{alert(`✅ ${videoType.charAt(0).toUpperCase()+videoType.slice(1)} video download completed: ${filename}`);// Reset progress after notification\nsetTimeout(()=>{setDownloadProgress(prev=>({...prev,[videoType]:0}));setDownloadComplete(prev=>({...prev,[videoType]:false}));},3000);},500);}}document.body.removeChild(link);// Clean up the blob URL after a short delay to ensure download starts\nsetTimeout(()=>{URL.revokeObjectURL(blobUrl);console.log(`🧹 Cleaned up blob URL for ${videoType} video`);},2000);// Increased timeout to 2 seconds\n}catch(error){console.error(`❌ Error downloading ${videoType} video:`,error);setDownloadProgress(prev=>({...prev,[videoType]:0}));setDownloadComplete(prev=>({...prev,[videoType]:false}));alert(`Error downloading ${videoType} video: ${error.message}`);}};const handleExport=async format=>{try{const exportFormat=format.toLowerCase();const videoId=video._id||video.video_id;// Call backend API for export with detailed detection tables\nconst response=await fetch(`/api/dashboard/video-processing-export?format=${exportFormat}&video_id=${videoId}`,{method:'GET',headers:{'Content-Type':'application/json'}});if(!response.ok){throw new Error(`Export failed: ${response.statusText}`);}const data=await response.json();if(exportFormat==='pdf'){// Handle PDF download\nif(data.pdf_data){const byteCharacters=atob(data.pdf_data);const byteNumbers=new Array(byteCharacters.length);for(let i=0;i<byteCharacters.length;i++){byteNumbers[i]=byteCharacters.charCodeAt(i);}const byteArray=new Uint8Array(byteNumbers);const blob=new Blob([byteArray],{type:'application/pdf'});const link=document.createElement('a');const url=URL.createObjectURL(blob);link.setAttribute('href',url);link.setAttribute('download',`video_${(video.video_id||videoId).substring(0,8)}_detailed_report.pdf`);link.style.visibility='hidden';document.body.appendChild(link);link.click();document.body.removeChild(link);URL.revokeObjectURL(url);}}else if(exportFormat==='csv'){// Handle CSV download\nif(data.csv_data){const csvContent=data.csv_data.map(row=>row.join(',')).join('\\n');const blob=new Blob([csvContent],{type:'text/csv;charset=utf-8;'});const link=document.createElement('a');const url=URL.createObjectURL(blob);link.setAttribute('href',url);link.setAttribute('download',`video_${(video.video_id||videoId).substring(0,8)}_detailed_report.csv`);link.style.visibility='hidden';document.body.appendChild(link);link.click();document.body.removeChild(link);URL.revokeObjectURL(url);}}}catch(error){console.error(`Error exporting to ${format}:`,error);alert(`Error exporting to ${format}: ${error.message}`);}};const detectionCounts=video.detection_counts||{};const totalDetections=detectionCounts.total||0;return/*#__PURE__*/_jsx(Col,{md:4,className:\"mb-4\",children:/*#__PURE__*/_jsxs(Card,{className:\"h-100 shadow-sm\",children:[/*#__PURE__*/_jsxs(Card.Header,{className:\"bg-info bg-opacity-10\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center\",children:[/*#__PURE__*/_jsxs(\"h6\",{className:\"mb-0\",children:[\"Video #\",video.video_id.substring(0,8),\"...\"]}),/*#__PURE__*/_jsx(\"small\",{className:\"text-info fw-bold\",children:\"\\uD83D\\uDCF9 Video\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-1\",children:/*#__PURE__*/_jsxs(\"small\",{className:\"text-muted\",children:[\"Models: \",video.models_run?video.models_run.join(', '):'N/A']})})]}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-2 text-center\",children:video.representative_frame?/*#__PURE__*/_jsx(\"img\",{src:`data:image/jpeg;base64,${video.representative_frame}`,alt:\"Video thumbnail\",className:\"img-fluid mb-2 border\",style:{maxHeight:\"200px\"},onError:e=>{console.warn(`Failed to load representative frame for video ${video.video_id}`);e.target.style.display='none';}}):/*#__PURE__*/_jsx(\"div\",{className:\"d-flex align-items-center justify-content-center border\",style:{height:\"200px\",backgroundColor:\"#f8f9fa\"},children:/*#__PURE__*/_jsx(\"span\",{className:\"text-muted\",children:\"No thumbnail available\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"small\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"mb-1\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"Detections:\"})}),/*#__PURE__*/_jsxs(\"ul\",{className:\"mb-2\",style:{paddingLeft:'20px'},children:[/*#__PURE__*/_jsxs(\"li\",{children:[\"Potholes: \",detectionCounts.potholes||0]}),/*#__PURE__*/_jsxs(\"li\",{children:[\"Cracks: \",detectionCounts.cracks||0]}),/*#__PURE__*/_jsxs(\"li\",{children:[\"Kerbs: \",detectionCounts.kerbs||0]})]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Total Detections:\"}),\" \",totalDetections]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Uploaded by:\"}),\" \",video.username||'Unknown']}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Timestamp:\"}),\" \",video.timestamp?new Date(video.timestamp).toLocaleString('en-IN',{timeZone:'Asia/Kolkata'}):'N/A']}),video.original_video_url&&/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Original File:\"}),\" \",/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:video.original_video_url.split('/').pop()||'N/A'})]}),video.processed_video_url&&/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Processed File:\"}),\" \",/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:video.processed_video_url.split('/').pop()||'N/A'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-2 mb-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-2\",children:[/*#__PURE__*/_jsxs(Button,{variant:\"primary\",size:\"sm\",className:\"me-2 mb-1\",onClick:()=>handleDownload('original'),disabled:!video.original_video_url||downloadProgress.original>0,children:[downloadProgress.original>0?`📥 Downloading... ${downloadProgress.original}%`:'📥 Original Video',downloadComplete.original&&' ✅']}),downloadProgress.original>0&&downloadProgress.original<100&&/*#__PURE__*/_jsx(\"div\",{className:\"progress mb-1\",style:{height:'4px'},children:/*#__PURE__*/_jsx(\"div\",{className:\"progress-bar bg-primary\",role:\"progressbar\",style:{width:`${downloadProgress.original}%`}})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-2\",children:[/*#__PURE__*/_jsxs(Button,{variant:\"success\",size:\"sm\",className:\"me-2 mb-1\",onClick:()=>handleDownload('processed'),disabled:!video.processed_video_url||downloadProgress.processed>0,children:[downloadProgress.processed>0?`📥 Downloading... ${downloadProgress.processed}%`:'📥 Processed Video',downloadComplete.processed&&' ✅']}),downloadProgress.processed>0&&downloadProgress.processed<100&&/*#__PURE__*/_jsx(\"div\",{className:\"progress mb-1\",style:{height:'4px'},children:/*#__PURE__*/_jsx(\"div\",{className:\"progress-bar bg-success\",role:\"progressbar\",style:{width:`${downloadProgress.processed}%`}})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-2\",children:[/*#__PURE__*/_jsx(Button,{variant:\"outline-secondary\",size:\"sm\",className:\"me-2\",onClick:()=>handleExport('PDF'),children:\"\\uD83D\\uDCC4 Export PDF\"}),/*#__PURE__*/_jsx(Button,{variant:\"outline-secondary\",size:\"sm\",onClick:()=>handleExport('CSV'),children:\"\\uD83D\\uDCCA Export CSV\"})]})]})]})]})},`video-${video.video_id}`);};function Dashboard(_ref4){var _dashboardData$videos,_dashboardData$videos2,_dashboardData$videos3,_dashboardData$users2,_dashboardData$users3,_dashboardData$users4;let{user}=_ref4;const[statistics,setStatistics]=useState({potholesDetected:0,cracksDetected:0,kerbsDetected:0,totalUsers:0});const[weeklyData,setWeeklyData]=useState({days:[],issues:[]});const[issuesByType,setIssuesByType]=useState({types:[],counts:[]});const[dashboardData,setDashboardData]=useState({potholes:{count:0,by_size:{},avg_volume:0,latest:[]},cracks:{count:0,by_type:{},by_size:{},latest:[]},kerbs:{count:0,by_condition:{},latest:[]},users:{count:0,by_role:{},latest:[]},videos:{count:0,latest:[]}});const[loading,setLoading]=useState(true);const[error,setError]=useState(null);// Date filter state\nconst[startDate,setStartDate]=useState('');const[endDate,setEndDate]=useState('');const[dateFilterApplied,setDateFilterApplied]=useState(false);// User filter state\nconst[usersList,setUsersList]=useState([]);const[selectedUser,setSelectedUser]=useState('');const[userFilterApplied,setUserFilterApplied]=useState(false);// Defect type filter state\nconst[defectFilters,setDefectFilters]=useState({potholes:true,cracks:true,kerbs:true});// Filtered issues state\nconst[filteredIssuesByType,setFilteredIssuesByType]=useState({types:[],counts:[]});// Dashboard tab state\nconst[activeTab,setActiveTab]=useState('dashboard');// Set default date range to previous week and auto-apply filter\nuseEffect(()=>{const currentDate=new Date();const lastWeek=new Date();lastWeek.setDate(currentDate.getDate()-6);// 6 days ago + today = 7 days\nconst formattedEndDate=currentDate.toISOString().split('T')[0];const formattedStartDate=lastWeek.toISOString().split('T')[0];setEndDate(formattedEndDate);setStartDate(formattedStartDate);fetchData({startDate:formattedStartDate,endDate:formattedEndDate});},[]);// Fetch dashboard data from backend\nconst fetchData=async function(){let filters=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};try{setLoading(true);// Add filters to requests if provided\nconst params={};if(filters.startDate)params.start_date=filters.startDate;if(filters.endDate)params.end_date=filters.endDate;if(filters.username)params.username=filters.username;if(user!==null&&user!==void 0&&user.role)params.user_role=user.role;// Get overview statistics\nconst statsResponse=await axios.get('/api/dashboard/statistics',{params});if(statsResponse.data.success){setStatistics({potholesDetected:statsResponse.data.data.issues_by_type.potholes,cracksDetected:statsResponse.data.data.issues_by_type.cracks,kerbsDetected:statsResponse.data.data.issues_by_type.kerbs,totalUsers:statistics.totalUsers// Preserve the existing user count\n});}// Get weekly trend data\nconst weeklyResponse=await axios.get('/api/dashboard/weekly-trend',{params});setWeeklyData({days:weeklyResponse.data.days,issues:weeklyResponse.data.issues});// Get issues by type\nconst typesResponse=await axios.get('/api/dashboard/issues-by-type',{params});setIssuesByType({types:typesResponse.data.types,counts:typesResponse.data.counts});// Get detailed dashboard data including latest images with enhanced S3-MongoDB integration\nlet dashboardResponse;try{// Try enhanced API endpoint first (with comprehensive S3-MongoDB integration)\ndashboardResponse=await axios.get('/api/dashboard/summary-v2',{params});console.log('✅ Using enhanced dashboard API with comprehensive S3-MongoDB integration');}catch(enhancedError){console.warn('⚠️ Enhanced API not available, falling back to standard API:',enhancedError.message);// Fallback to original API\ndashboardResponse=await axios.get('/api/dashboard/summary',{params});console.log('✅ Using standard dashboard API');}if(dashboardResponse.data.success){const dashboardData=dashboardResponse.data.data;// Calculate multi-defect statistics\nconst multiDefectStats={totalImages:0,multiDefectImages:0,singleDefectImages:0,categoryBreakdown:{potholes:0,cracks:0,kerbs:0}};// Count multi-defect images from each category\n['potholes','cracks','kerbs'].forEach(category=>{const categoryImages=dashboardData[category].latest||[];categoryImages.forEach(item=>{if(item.multi_defect_image){multiDefectStats.multiDefectImages++;}multiDefectStats.totalImages++;multiDefectStats.categoryBreakdown[category]++;});});multiDefectStats.singleDefectImages=multiDefectStats.totalImages-multiDefectStats.multiDefectImages;// Add multi-defect statistics to dashboard data\ndashboardData.multiDefectStats=multiDefectStats;setDashboardData(dashboardData);}// Get users data\ntry{const usersResponse=await axios.get('/api/users/summary',{params});if(usersResponse.data.success){setStatistics(prevStats=>({...prevStats,totalUsers:usersResponse.data.total_users||0}));// Ensure users data is properly set in dashboardData\nsetDashboardData(prevData=>({...prevData,users:{count:usersResponse.data.total_users||0,by_role:usersResponse.data.roles_distribution||{},latest:usersResponse.data.recent_users||[]}}));}}catch(userErr){console.error('Error fetching user data:',userErr);// Non-critical error, continue with other data\n}setLoading(false);}catch(err){setError('Error fetching dashboard data');setLoading(false);console.error('Error fetching data:',err);}};// Fetch users list for filter dropdown\nuseEffect(()=>{const fetchUsers=async()=>{try{const params={};if(user!==null&&user!==void 0&&user.role)params.user_role=user.role;const response=await axios.get('/api/users/all',{params});if(response.data.success){// Users are already filtered by the backend based on RBAC\nsetUsersList(response.data.users);}}catch(error){console.error('Error fetching users:',error);}};fetchUsers();},[user]);// Handle date filter application\nconst handleApplyDateFilter=()=>{if(startDate&&endDate){fetchData({startDate,endDate,username:selectedUser||undefined});setDateFilterApplied(true);}};// Handle date filter reset\nconst handleResetDateFilter=()=>{const currentDate=new Date();const lastWeek=new Date();lastWeek.setDate(currentDate.getDate()-6);// 6 days ago + today = 7 days\nconst newEndDate=currentDate.toISOString().split('T')[0];const newStartDate=lastWeek.toISOString().split('T')[0];setEndDate(newEndDate);setStartDate(newStartDate);fetchData({startDate:newStartDate,endDate:newEndDate,username:selectedUser||undefined});setDateFilterApplied(false);};// Handle user filter application\nconst handleApplyUserFilter=()=>{fetchData({startDate,endDate,username:selectedUser||undefined});setUserFilterApplied(!!selectedUser);};// Handle user filter reset\nconst handleResetUserFilter=()=>{setSelectedUser('');fetchData({startDate,endDate});setUserFilterApplied(false);};// Handle user selection\nconst handleUserChange=e=>{setSelectedUser(e.target.value);};// Filter the issues by type whenever the filters or data changes\nuseEffect(()=>{if(issuesByType.types.length>0){const filteredTypes=[];const filteredCounts=[];issuesByType.types.forEach((type,index)=>{if(type.includes('Pothole')&&defectFilters.potholes||type.includes('Crack')&&defectFilters.cracks||type.includes('Kerb')&&defectFilters.kerbs){filteredTypes.push(type);filteredCounts.push(issuesByType.counts[index]);}});setFilteredIssuesByType({types:filteredTypes,counts:filteredCounts});}},[issuesByType,defectFilters]);// Handle defect filter change\nconst handleDefectFilterChange=defectType=>{setDefectFilters(prev=>({...prev,[defectType]:!prev[defectType]}));};// Add export handlers\nconst handleDownloadPDF=()=>{var _dashboardData$users;const doc=new jsPDF();let yPosition=20;// Header\ndoc.setFontSize(20);doc.setFont('helvetica','bold');doc.text('Road AI Safety Enhancement - Dashboard Report',105,yPosition,{align:'center'});yPosition+=15;doc.setFontSize(12);doc.setFont('helvetica','normal');doc.text(`Generated on: ${new Date().toLocaleString('en-IN',{timeZone:'Asia/Kolkata'})}`,105,yPosition,{align:'center'});yPosition+=20;// Date Range Info\nif(dateFilterApplied){doc.setFontSize(14);doc.setFont('helvetica','bold');doc.text('Date Range Filter:',14,yPosition);yPosition+=8;doc.setFontSize(12);doc.setFont('helvetica','normal');doc.text(`From: ${new Date(startDate).toLocaleDateString()} To: ${new Date(endDate).toLocaleDateString()}`,14,yPosition);yPosition+=15;}// Statistics Summary\ndoc.setFontSize(16);doc.setFont('helvetica','bold');doc.text('Statistics Summary',14,yPosition);yPosition+=10;doc.setFontSize(12);doc.setFont('helvetica','normal');doc.text(`Total Potholes Detected: ${statistics.potholesDetected}`,14,yPosition);yPosition+=7;doc.text(`Total Cracks Detected: ${statistics.cracksDetected}`,14,yPosition);yPosition+=7;doc.text(`Total Kerbs Detected: ${statistics.kerbsDetected}`,14,yPosition);yPosition+=7;doc.text(`Total Users: ${statistics.totalUsers}`,14,yPosition);yPosition+=15;// Infrastructure Distribution\ndoc.setFontSize(16);doc.setFont('helvetica','bold');doc.text('Infrastructure Distribution',14,yPosition);yPosition+=10;const totalIssues=statistics.potholesDetected+statistics.cracksDetected+statistics.kerbsDetected;if(totalIssues>0){const potholePercent=(statistics.potholesDetected/totalIssues*100).toFixed(1);const crackPercent=(statistics.cracksDetected/totalIssues*100).toFixed(1);const kerbPercent=(statistics.kerbsDetected/totalIssues*100).toFixed(1);doc.setFontSize(12);doc.setFont('helvetica','normal');doc.text(`Potholes: ${statistics.potholesDetected} (${potholePercent}%)`,14,yPosition);yPosition+=7;doc.text(`Cracks: ${statistics.cracksDetected} (${crackPercent}%)`,14,yPosition);yPosition+=7;doc.text(`Kerbs: ${statistics.kerbsDetected} (${kerbPercent}%)`,14,yPosition);yPosition+=15;}// User Overview\nif((_dashboardData$users=dashboardData.users)!==null&&_dashboardData$users!==void 0&&_dashboardData$users.latest&&dashboardData.users.latest.length>0){doc.setFontSize(16);doc.setFont('helvetica','bold');doc.text('Recent Users',14,yPosition);yPosition+=10;const userTableData=dashboardData.users.latest.map((user,idx)=>[idx+1,user.username,user.role,new Date(user.last_login).toLocaleString('en-IN',{timeZone:'Asia/Kolkata'})]);autoTable(doc,{head:[['#','Username','Role','Last Login']],body:userTableData,startY:yPosition,margin:{top:10},styles:{fontSize:10},headStyles:{fillColor:[0,123,255]}});yPosition=doc.lastAutoTable.finalY+15;}// Potholes Section\nif(dashboardData.potholes.latest&&dashboardData.potholes.latest.length>0){doc.setFontSize(16);doc.setFont('helvetica','bold');doc.text('Potholes Detected',14,yPosition);yPosition+=10;const potholeTableData=dashboardData.potholes.latest.map((defect,idx)=>[idx+1,defect.area_cm2?defect.area_cm2.toFixed(2)+' cm²':'N/A',defect.depth_cm?defect.depth_cm.toFixed(2)+' cm':'N/A',defect.volume?defect.volume.toFixed(2):'N/A',defect.username||'Unknown',new Date(defect.timestamp).toLocaleString('en-IN',{timeZone:'Asia/Kolkata'})]);autoTable(doc,{head:[['#','Area','Depth','Volume','Uploaded By','Timestamp']],body:potholeTableData,startY:yPosition,margin:{top:10},styles:{fontSize:9},headStyles:{fillColor:[220,53,69]}});yPosition=doc.lastAutoTable.finalY+15;}// Cracks Section\nif(dashboardData.cracks.latest&&dashboardData.cracks.latest.length>0){doc.setFontSize(16);doc.setFont('helvetica','bold');doc.text('Cracks Detected',14,yPosition);yPosition+=10;const crackTableData=dashboardData.cracks.latest.map((defect,idx)=>[idx+1,defect.crack_type||'Unknown',defect.area_cm2?defect.area_cm2.toFixed(2)+' cm²':'N/A',defect.area_range||'N/A',defect.username||'Unknown',new Date(defect.timestamp).toLocaleString('en-IN',{timeZone:'Asia/Kolkata'})]);autoTable(doc,{head:[['#','Type','Area','Range','Uploaded By','Timestamp']],body:crackTableData,startY:yPosition,margin:{top:10},styles:{fontSize:9},headStyles:{fillColor:[40,167,69]}});yPosition=doc.lastAutoTable.finalY+15;}// Kerbs Section\nif(dashboardData.kerbs.latest&&dashboardData.kerbs.latest.length>0){doc.setFontSize(16);doc.setFont('helvetica','bold');doc.text('Kerbs Detected',14,yPosition);yPosition+=10;const kerbTableData=dashboardData.kerbs.latest.map((defect,idx)=>[idx+1,defect.kerb_type||'Unknown',defect.length_m?defect.length_m.toFixed(2)+' m':'N/A',defect.condition||'Unknown',defect.username||'Unknown',new Date(defect.timestamp).toLocaleString('en-IN',{timeZone:'Asia/Kolkata'})]);autoTable(doc,{head:[['#','Type','Length','Condition','Uploaded By','Timestamp']],body:kerbTableData,startY:yPosition,margin:{top:10},styles:{fontSize:9},headStyles:{fillColor:[0,123,255]}});}// Footer\nconst pageCount=doc.internal.getNumberOfPages();for(let i=1;i<=pageCount;i++){doc.setPage(i);doc.setFontSize(10);doc.setFont('helvetica','italic');doc.text(`Page ${i} of ${pageCount}`,105,doc.internal.pageSize.height-10,{align:'center'});}doc.save('Dashboard_Report.pdf');};const handleDownloadExcel=()=>{const wsData=[['#','Area (cm²)','Depth (cm)','Volume','Uploaded By','Timestamp'],...(dashboardData.potholes.latest||[]).map((defect,idx)=>[idx+1,defect.area,defect.depth,defect.volume,defect.username,new Date(defect.timestamp).toLocaleString('en-IN',{timeZone:'Asia/Kolkata'})])];const ws=XLSX.utils.aoa_to_sheet(wsData);const wb=XLSX.utils.book_new();XLSX.utils.book_append_sheet(wb,ws,'Processed Report');const wbout=XLSX.write(wb,{bookType:'xlsx',type:'array'});saveAs(new Blob([wbout],{type:'application/octet-stream'}),'Processed_Report.xlsx');};return/*#__PURE__*/_jsxs(Container,{fluid:true,className:\"dashboard-container\",children:[/*#__PURE__*/_jsxs(Card,{className:\"mb-3 shadow-sm dashboard-card filters-card\",children:[/*#__PURE__*/_jsx(Card.Header,{className:\"bg-primary text-white py-2\",children:/*#__PURE__*/_jsx(\"h6\",{className:\"mb-0\",children:\"Filters\"})}),/*#__PURE__*/_jsx(Card.Body,{className:\"py-3\",children:/*#__PURE__*/_jsxs(Row,{className:\"g-3\",children:[/*#__PURE__*/_jsx(Col,{lg:6,children:/*#__PURE__*/_jsxs(\"div\",{className:\"filter-section\",children:[/*#__PURE__*/_jsx(\"h6\",{className:\"mb-2\",children:\"Date Range\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-controls\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"filter-field\",children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{className:\"small mb-1\",children:\"Start Date\"}),/*#__PURE__*/_jsx(Form.Control,{type:\"date\",value:startDate,onChange:e=>setStartDate(e.target.value),size:\"sm\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"filter-field\",children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{className:\"small mb-1\",children:\"End Date\"}),/*#__PURE__*/_jsx(Form.Control,{type:\"date\",value:endDate,onChange:e=>setEndDate(e.target.value),size:\"sm\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-actions\",children:[/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"primary\",onClick:handleApplyDateFilter,disabled:!startDate||!endDate,children:\"Apply\"}),/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"outline-secondary\",onClick:handleResetDateFilter,disabled:!dateFilterApplied,children:\"Reset\"})]})]}),dateFilterApplied&&/*#__PURE__*/_jsx(\"div\",{className:\"filter-status text-success mt-2 p-2\",children:/*#__PURE__*/_jsxs(\"small\",{children:[\"Showing data from \",new Date(startDate).toLocaleDateString(),\" to \",new Date(endDate).toLocaleDateString()]})})]})}),/*#__PURE__*/_jsx(Col,{lg:6,children:/*#__PURE__*/_jsxs(\"div\",{className:\"filter-section\",children:[/*#__PURE__*/_jsx(\"h6\",{className:\"mb-2\",children:\"User Filter\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-controls\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"filter-field\",children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{className:\"small mb-1\",children:\"Select User\"}),/*#__PURE__*/_jsxs(Form.Select,{value:selectedUser,onChange:handleUserChange,size:\"sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Users\"}),usersList.map((user,index)=>/*#__PURE__*/_jsxs(\"option\",{value:user.username,children:[user.username,\" (\",user.role,\")\"]},index))]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-actions\",children:[/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"primary\",onClick:handleApplyUserFilter,children:\"Apply\"}),/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"outline-secondary\",onClick:handleResetUserFilter,disabled:!userFilterApplied,children:\"Reset\"})]})]}),userFilterApplied&&/*#__PURE__*/_jsx(\"div\",{className:\"filter-status text-success mt-2 p-2\",children:/*#__PURE__*/_jsxs(\"small\",{children:[\"Showing data for user: \",selectedUser]})})]})})]})})]}),loading?/*#__PURE__*/_jsx(\"div\",{className:\"text-center\",children:/*#__PURE__*/_jsx(\"div\",{className:\"spinner-border text-primary\",role:\"status\",children:/*#__PURE__*/_jsx(\"span\",{className:\"visually-hidden\",children:\"Loading...\"})})}):error?/*#__PURE__*/_jsx(\"div\",{className:\"alert alert-danger p-3\",children:error}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Card,{className:\"mb-3 shadow-sm dashboard-card\",children:/*#__PURE__*/_jsx(Card.Body,{className:\"p-0\",children:/*#__PURE__*/_jsxs(Tabs,{activeKey:activeTab,onSelect:k=>setActiveTab(k),className:\"dashboard-tabs\",children:[/*#__PURE__*/_jsx(Tab,{eventKey:\"dashboard\",title:\"Dashboard View\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"p-3\",children:[/*#__PURE__*/_jsxs(Row,{className:\"mb-3 g-3\",children:[/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(Card,{className:\"h-100 shadow-sm dashboard-card stats-card\",children:/*#__PURE__*/_jsxs(Card.Body,{className:\"text-center py-3\",children:[/*#__PURE__*/_jsx(\"h6\",{className:\"card-title mb-2\",children:\"Potholes\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-primary mb-0\",children:statistics.potholesDetected})]})})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(Card,{className:\"h-100 shadow-sm dashboard-card stats-card\",children:/*#__PURE__*/_jsxs(Card.Body,{className:\"text-center py-3\",children:[/*#__PURE__*/_jsx(\"h6\",{className:\"card-title mb-2\",children:\"Cracks\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-primary mb-0\",children:statistics.cracksDetected})]})})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(Card,{className:\"h-100 shadow-sm dashboard-card stats-card\",children:/*#__PURE__*/_jsxs(Card.Body,{className:\"text-center py-3\",children:[/*#__PURE__*/_jsx(\"h6\",{className:\"card-title mb-2\",children:\"Kerbs\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-primary mb-0\",children:statistics.kerbsDetected})]})})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(Card,{className:\"h-100 shadow-sm dashboard-card stats-card\",children:/*#__PURE__*/_jsxs(Card.Body,{className:\"text-center py-3\",children:[/*#__PURE__*/_jsx(\"h6\",{className:\"card-title mb-2\",children:\"Users\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-success mb-0\",children:statistics.totalUsers})]})})})]}),/*#__PURE__*/_jsxs(Row,{className:\"mb-3 g-3\",children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Card,{className:\"shadow-sm dashboard-card\",children:[/*#__PURE__*/_jsx(Card.Header,{className:\"bg-primary text-white py-2\",children:/*#__PURE__*/_jsx(\"h6\",{className:\"mb-0\",children:\"Weekly Detection Trend\"})}),/*#__PURE__*/_jsx(Card.Body,{className:\"py-3\",children:/*#__PURE__*/_jsx(ChartContainer,{data:[{x:weeklyData.days,y:weeklyData.issues,type:'scatter',mode:'lines+markers',marker:{color:'#007bff'}}],layout:{xaxis:{title:'Day'},yaxis:{title:'Issues Detected'}}})})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Card,{className:\"shadow-sm dashboard-card\",children:[/*#__PURE__*/_jsx(Card.Header,{className:\"bg-primary text-white py-2\",children:/*#__PURE__*/_jsx(\"h6\",{className:\"mb-0\",children:\"Issues by Type\"})}),/*#__PURE__*/_jsx(Card.Body,{className:\"py-3\",children:/*#__PURE__*/_jsx(ChartContainer,{data:[{type:'bar',x:filteredIssuesByType.types,y:filteredIssuesByType.counts,marker:{color:filteredIssuesByType.types.map(type=>{if(type.includes('Pothole'))return'#007bff';if(type.includes('Crack'))return'#28a745';if(type.includes('Kerb'))return'#dc3545';return'#6c757d';})}}],layout:{xaxis:{title:'Issue Type',tickangle:-45,automargin:true},yaxis:{title:'Count'},margin:{t:10,b:80,l:50,r:10}},showLegend:true,legendItems:[{label:'Potholes',color:'#007bff',checked:defectFilters.potholes,onChange:()=>handleDefectFilterChange('potholes')},{label:'Cracks',color:'#28a745',checked:defectFilters.cracks,onChange:()=>handleDefectFilterChange('cracks')},{label:'Kerbs',color:'#dc3545',checked:defectFilters.kerbs,onChange:()=>handleDefectFilterChange('kerbs')}],className:\"compact-legend\"})})]})})]}),/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsx(Col,{md:12,children:/*#__PURE__*/_jsxs(Card,{className:\"shadow-sm dashboard-card\",children:[/*#__PURE__*/_jsx(Card.Header,{className:\"bg-primary text-white py-2\",children:/*#__PURE__*/_jsx(\"h6\",{className:\"mb-0\",children:\"Infrastructure Distribution\"})}),/*#__PURE__*/_jsx(Card.Body,{className:\"py-3\",children:/*#__PURE__*/_jsx(ChartContainer,{data:[{type:'pie',labels:['Potholes','Cracks','Kerbs'],values:[statistics.potholesDetected,statistics.cracksDetected,statistics.kerbsDetected],marker:{colors:['#007bff','#28a745','#dc3545']},textinfo:\"label+percent\",insidetextorientation:\"radial\"}],layout:{height:300},isPieChart:true})})]})})}),/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsx(Col,{md:12,children:/*#__PURE__*/_jsxs(Card,{className:\"shadow-sm dashboard-card\",children:[/*#__PURE__*/_jsx(Card.Header,{className:\"bg-primary text-white py-2\",children:/*#__PURE__*/_jsx(\"h6\",{className:\"mb-0\",children:\"All Uploaded Images\"})}),/*#__PURE__*/_jsx(Card.Body,{className:\"py-3\",children:/*#__PURE__*/_jsxs(Tabs,{defaultActiveKey:\"potholes\",className:\"mb-2\",children:[/*#__PURE__*/_jsx(Tab,{eventKey:\"potholes\",title:`Potholes (${dashboardData.potholes.latest.length})`,children:/*#__PURE__*/_jsx(\"div\",{style:{maxHeight:'700px',overflowY:'auto',paddingRight:'10px'},children:/*#__PURE__*/_jsx(Row,{children:dashboardData.potholes.latest.map((pothole,index)=>/*#__PURE__*/_jsx(ImageCard,{defect:pothole,defectType:\"potholes\",defectIdKey:\"pothole_id\"},`pothole-${pothole.pothole_id||pothole.image_id||index}`))})})}),/*#__PURE__*/_jsx(Tab,{eventKey:\"cracks\",title:`Cracks (${dashboardData.cracks.latest.length})`,children:/*#__PURE__*/_jsx(\"div\",{style:{maxHeight:'700px',overflowY:'auto',paddingRight:'10px'},children:/*#__PURE__*/_jsx(Row,{children:dashboardData.cracks.latest.map((crack,index)=>/*#__PURE__*/_jsx(ImageCard,{defect:crack,defectType:\"cracks\",defectIdKey:\"crack_id\"},`crack-${crack.crack_id||crack.image_id||index}`))})})}),/*#__PURE__*/_jsx(Tab,{eventKey:\"kerbs\",title:`Kerbs (${dashboardData.kerbs.latest.length})`,children:/*#__PURE__*/_jsx(\"div\",{style:{maxHeight:'700px',overflowY:'auto',paddingRight:'10px'},children:/*#__PURE__*/_jsx(Row,{children:dashboardData.kerbs&&dashboardData.kerbs.latest&&dashboardData.kerbs.latest.length>0?dashboardData.kerbs.latest.map((kerb,index)=>/*#__PURE__*/_jsx(ImageCard,{defect:kerb,defectType:\"kerbs\",defectIdKey:\"kerb_id\"},`kerb-${kerb.kerb_id||kerb.image_id||index}`)):/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(\"div\",{className:\"alert alert-info p-3\",children:\"No kerb images available yet. Upload some kerb images using the Pavement Analysis tool.\"})})})})})]})})]})})}),/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsx(Col,{md:12,children:/*#__PURE__*/_jsxs(Card,{className:\"shadow-sm dashboard-card\",children:[/*#__PURE__*/_jsx(Card.Header,{className:\"bg-info text-white py-2\",children:/*#__PURE__*/_jsxs(\"h6\",{className:\"mb-0\",children:[\"All Videos Processed (\",((_dashboardData$videos=dashboardData.videos)===null||_dashboardData$videos===void 0?void 0:(_dashboardData$videos2=_dashboardData$videos.latest)===null||_dashboardData$videos2===void 0?void 0:_dashboardData$videos2.length)||0,\")\"]})}),/*#__PURE__*/_jsx(Card.Body,{className:\"py-3\",children:(_dashboardData$videos3=dashboardData.videos)!==null&&_dashboardData$videos3!==void 0&&_dashboardData$videos3.latest&&dashboardData.videos.latest.length>0?/*#__PURE__*/_jsx(\"div\",{style:{maxHeight:'700px',overflowY:'auto',paddingRight:'10px'},children:/*#__PURE__*/_jsx(Row,{children:dashboardData.videos.latest.map((video,index)=>/*#__PURE__*/_jsx(VideoCard,{video:video},`video-${video.video_id||index}`))})}):/*#__PURE__*/_jsx(\"div\",{className:\"alert alert-info p-3\",children:\"No processed videos available yet. Upload and process some videos using the Video Defect Detection tool.\"})})]})})})]})}),/*#__PURE__*/_jsx(Tab,{eventKey:\"map\",title:\"Defect Map View\",children:/*#__PURE__*/_jsx(\"div\",{className:\"p-4\",children:/*#__PURE__*/_jsx(DefectMap,{user:user})})}),/*#__PURE__*/_jsx(Tab,{eventKey:\"users\",title:\"Users Overview\",children:/*#__PURE__*/_jsx(\"div\",{className:\"p-3\",children:/*#__PURE__*/_jsxs(Row,{className:\"g-3\",children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Card,{className:\"shadow-sm dashboard-card\",children:[/*#__PURE__*/_jsx(Card.Header,{className:\"bg-primary text-white py-2\",children:/*#__PURE__*/_jsx(\"h6\",{className:\"mb-0\",children:\"User Distribution by Role\"})}),/*#__PURE__*/_jsx(Card.Body,{className:\"py-3\",children:/*#__PURE__*/_jsx(ChartContainer,{data:[{type:'pie',labels:Object.keys(((_dashboardData$users2=dashboardData.users)===null||_dashboardData$users2===void 0?void 0:_dashboardData$users2.by_role)||{}),values:Object.values(((_dashboardData$users3=dashboardData.users)===null||_dashboardData$users3===void 0?void 0:_dashboardData$users3.by_role)||{}),marker:{colors:['#007bff','#28a745','#dc3545','#6c757d']},textinfo:\"label+percent\",insidetextorientation:\"radial\"}],isPieChart:true})})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Card,{className:\"shadow-sm dashboard-card\",children:[/*#__PURE__*/_jsx(Card.Header,{className:\"bg-primary text-white py-2\",children:/*#__PURE__*/_jsx(\"h6\",{className:\"mb-0\",children:\"Recent Users\"})}),/*#__PURE__*/_jsx(Card.Body,{className:\"py-3\",children:/*#__PURE__*/_jsx(\"div\",{className:\"table-responsive\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"table table-sm table-hover\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Username\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Role\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Last Login\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:(_dashboardData$users4=dashboardData.users)!==null&&_dashboardData$users4!==void 0&&_dashboardData$users4.latest&&dashboardData.users.latest.length>0?dashboardData.users.latest.map((user,index)=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:user.username}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"span\",{className:`badge bg-${user.role==='admin'?'danger':user.role==='manager'?'warning':'primary'}`,children:user.role})}),/*#__PURE__*/_jsx(\"td\",{children:new Date(user.last_login).toLocaleString('en-IN',{timeZone:'Asia/Kolkata'})})]},`user-${index}`)):/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"3\",className:\"text-center\",children:\"No recent user activity\"})})})]})})})]})})]})})})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-end mb-2\",children:[/*#__PURE__*/_jsxs(Button,{variant:\"outline-danger\",size:\"sm\",className:\"me-2\",onClick:handleDownloadPDF,children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-file-pdf me-1\"}),\"Download PDF\"]}),/*#__PURE__*/_jsxs(Button,{variant:\"outline-success\",size:\"sm\",onClick:handleDownloadExcel,children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-file-excel me-1\"}),\"Download Excel\"]})]})]})]});}export default Dashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Container", "Row", "Col", "Card", "Tabs", "Tab", "Form", "<PERSON><PERSON>", "Plot", "ChartContainer", "DefectMap", "jsPDF", "autoTable", "XLSX", "saveAs", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "getImageUrlForDisplay", "imageData", "imageType", "arguments", "length", "undefined", "console", "log", "fullUrlField", "urlParts", "split", "bucketIndex", "findIndex", "part", "includes", "s3Key", "slice", "join", "proxyUrl", "encodeURIComponent", "s3KeyField", "<PERSON><PERSON><PERSON>", "map", "url", "gridfsIdField", "EnhancedImageDisplay", "_ref", "alt", "className", "style", "onError", "currentImageUrl", "setCurrentImageUrl", "<PERSON><PERSON><PERSON><PERSON>", "setHasError", "fallback<PERSON><PERSON><PERSON>s", "set<PERSON><PERSON>back<PERSON>tte<PERSON>s", "imageUrl", "generatedUrl", "s3KeyValue", "fullUrlValue", "handleImageError", "event", "_event$target", "_event$target2", "_event$target3", "_event$target4", "_event$target5", "error", "target", "src", "naturalWidth", "naturalHeight", "complete", "fetch", "method", "then", "response", "status", "statusText", "headers", "Object", "fromEntries", "entries", "catch", "fetchError", "message", "fallbackUrl", "getFallbackImageUrl", "alternativeUrl", "children", "loading", "onLoad", "ImageCard", "_ref2", "defect", "defectType", "defectId<PERSON><PERSON>", "isOriginal", "setIsOriginal", "to<PERSON><PERSON><PERSON><PERSON>", "showOriginal", "isMultiDefect", "detected_defects", "detectedDefects", "md", "Header", "crack_type", "crack_id", "condition", "kerb_id", "pothole_id", "filter", "d", "Body", "maxHeight", "warn", "area_cm2", "toFixed", "depth_cm", "volume", "area_range", "kerb_type", "length_m", "username", "timestamp", "Date", "toLocaleString", "timeZone", "variant", "size", "onClick", "image_id", "Math", "random", "VideoCard", "_ref3", "video", "downloadProgress", "setDownloadProgress", "downloadComplete", "setDownloadComplete", "handleDownload", "videoType", "prev", "videoId", "_id", "video_id", "downloadUrl", "ok", "errorData", "json", "Error", "contentLength", "get", "total", "parseInt", "loaded", "reader", "body", "<PERSON><PERSON><PERSON><PERSON>", "chunks", "done", "value", "read", "push", "progress", "round", "videoBlob", "Blob", "finalBlob", "type", "blobUrl", "URL", "createObjectURL", "filename", "original_video_url", "pop", "processed_video_url", "substring", "link", "document", "createElement", "href", "download", "display", "append<PERSON><PERSON><PERSON>", "click", "setTimeout", "alert", "char<PERSON>t", "toUpperCase", "clickError", "newWindow", "window", "open", "title", "windowError", "location", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleExport", "format", "exportFormat", "toLowerCase", "data", "pdf_data", "byteCharacters", "atob", "byteNumbers", "Array", "i", "charCodeAt", "byteArray", "Uint8Array", "blob", "setAttribute", "visibility", "csv_data", "csv<PERSON><PERSON>nt", "row", "detectionCounts", "detection_counts", "totalDetections", "models_run", "representative_frame", "e", "height", "backgroundColor", "paddingLeft", "potholes", "cracks", "kerbs", "disabled", "original", "role", "width", "processed", "Dashboard", "_ref4", "_dashboardData$videos", "_dashboardData$videos2", "_dashboardData$videos3", "_dashboardData$users2", "_dashboardData$users3", "_dashboardData$users4", "user", "statistics", "setStatistics", "potholesDetected", "cracksDetected", "kerbsDetected", "totalUsers", "weeklyData", "setWeeklyData", "days", "issues", "issuesByType", "setIssuesByType", "types", "counts", "dashboardData", "setDashboardData", "count", "by_size", "avg_volume", "latest", "by_type", "by_condition", "users", "by_role", "videos", "setLoading", "setError", "startDate", "setStartDate", "endDate", "setEndDate", "dateFilterApplied", "setDateFilterApplied", "usersList", "setUsersList", "selected<PERSON>ser", "setSelectedUser", "userFilterApplied", "setUserFilterApplied", "defectFilters", "setDefectFilters", "filteredIssuesByType", "setFilteredIssuesByType", "activeTab", "setActiveTab", "currentDate", "lastWeek", "setDate", "getDate", "formattedEndDate", "toISOString", "formattedStartDate", "fetchData", "filters", "params", "start_date", "end_date", "user_role", "statsResponse", "success", "issues_by_type", "weeklyResponse", "typesResponse", "dashboardResponse", "enhancedError", "multiDefectStats", "totalImages", "multiDefectImages", "singleDefectImages", "categoryBreakdown", "for<PERSON>ach", "category", "categoryImages", "item", "multi_defect_image", "usersResponse", "prevStats", "total_users", "prevData", "roles_distribution", "recent_users", "userErr", "err", "fetchUsers", "handleApplyDateFilter", "handleResetDateFilter", "newEndDate", "newStartDate", "handleApplyUserFilter", "handleResetUserFilter", "handleUserChange", "filteredTypes", "filteredCounts", "index", "handleDefectFilterChange", "handleDownloadPDF", "_dashboardData$users", "doc", "yPosition", "setFontSize", "setFont", "text", "align", "toLocaleDateString", "totalIssues", "potholePercent", "crackPercent", "kerb<PERSON>ercent", "userTableData", "idx", "last_login", "head", "startY", "margin", "top", "styles", "fontSize", "headStyles", "fillColor", "lastAutoTable", "finalY", "potholeTableData", "crackTableData", "kerbTableData", "pageCount", "internal", "getNumberOfPages", "setPage", "pageSize", "save", "handleDownloadExcel", "wsData", "area", "depth", "ws", "utils", "aoa_to_sheet", "wb", "book_new", "book_append_sheet", "wbout", "write", "bookType", "fluid", "lg", "Group", "Label", "Control", "onChange", "Select", "active<PERSON><PERSON>", "onSelect", "k", "eventKey", "x", "y", "mode", "marker", "color", "layout", "xaxis", "yaxis", "tickangle", "automargin", "t", "b", "l", "r", "showLegend", "legendItems", "label", "checked", "labels", "values", "colors", "textinfo", "insidetextorientation", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultActiveKey", "overflowY", "paddingRight", "pothole", "crack", "kerb", "keys", "colSpan"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport { Container, Row, Col, Card, Tabs, Tab, Form, Button } from 'react-bootstrap';\r\nimport Plot from 'react-plotly.js';\r\nimport ChartContainer from '../components/ChartContainer';\r\nimport DefectMap from '../components/DefectMap';\r\nimport './dashboard.css';\r\nimport jsPDF from 'jspdf';\r\nimport autoTable from 'jspdf-autotable';\r\nimport * as XLSX from 'xlsx';\r\nimport { saveAs } from 'file-saver';\r\n\r\n/**\r\n * Comprehensive Image URL Resolution Logic\r\n * Handles both S3 URLs (new data) and GridFS IDs (legacy data)\r\n *\r\n * Priority order:\r\n * 1. S3 Full URL (direct HTTPS link)\r\n * 2. S3 Key (generate URL via API)\r\n * 3. GridFS ID (legacy endpoint)\r\n * 4. Fallback to \"No image available\"\r\n */\r\nconst getImageUrlForDisplay = (imageData, imageType = 'original') => {\r\n  console.log('getImageUrlForDisplay called:', { imageData, imageType });\r\n\r\n  if (!imageData) {\r\n    console.log('No imageData provided');\r\n    return null;\r\n  }\r\n\r\n  // Try S3 full URL first (new images with pre-generated URLs) - proxy through backend\r\n  const fullUrlField = `${imageType}_image_full_url`;\r\n  if (imageData[fullUrlField]) {\r\n    console.log('Using full URL field:', fullUrlField, imageData[fullUrlField]);\r\n    // Extract S3 key from full URL and use proxy endpoint\r\n    const urlParts = imageData[fullUrlField].split('/');\r\n    const bucketIndex = urlParts.findIndex(part => part.includes('.s3.'));\r\n    if (bucketIndex !== -1 && bucketIndex + 1 < urlParts.length) {\r\n      const s3Key = urlParts.slice(bucketIndex + 1).join('/');\r\n      const proxyUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\r\n      console.log('Generated proxy URL from full URL:', proxyUrl);\r\n      return proxyUrl;\r\n    }\r\n  }\r\n\r\n  // Try S3 key with proxy endpoint (new images without full URL)\r\n  const s3KeyField = `${imageType}_image_s3_url`;\r\n  if (imageData[s3KeyField]) {\r\n    console.log('Using S3 key field:', s3KeyField, imageData[s3KeyField]);\r\n\r\n    // Properly encode the S3 key for URL path\r\n    const s3Key = imageData[s3KeyField];\r\n    const encodedKey = s3Key.split('/').map(part => encodeURIComponent(part)).join('/');\r\n    const url = `/api/pavement/get-s3-image/${encodedKey}`;\r\n\r\n    console.log('Generated proxy URL from S3 key:', url);\r\n    console.log('Original S3 key:', s3Key);\r\n    console.log('Encoded S3 key:', encodedKey);\r\n\r\n    return url;\r\n  }\r\n\r\n  // Fall back to GridFS endpoint (legacy images)\r\n  const gridfsIdField = `${imageType}_image_id`;\r\n  if (imageData[gridfsIdField]) {\r\n    console.log('Using GridFS field:', gridfsIdField, imageData[gridfsIdField]);\r\n    const url = `/api/pavement/get-image/${imageData[gridfsIdField]}`;\r\n    console.log('Generated GridFS URL:', url);\r\n    return url;\r\n  }\r\n\r\n  // No image URL available\r\n  console.log('No image URL available for:', imageType, imageData);\r\n  return null;\r\n};\r\n\r\n/**\r\n * Enhanced Image Component with comprehensive error handling\r\n * Supports S3 URLs, GridFS fallback, and graceful error handling\r\n */\r\nconst EnhancedImageDisplay = ({ imageData, imageType = 'original', alt, className, style, onError }) => {\r\n  const [currentImageUrl, setCurrentImageUrl] = useState(null);\r\n  const [hasError, setHasError] = useState(false);\r\n  const [fallbackAttempts, setFallbackAttempts] = useState(0);\r\n\r\n  useEffect(() => {\r\n    // Reset state when imageData changes\r\n    setHasError(false);\r\n    setFallbackAttempts(0);\r\n\r\n    // Get initial image URL\r\n    const imageUrl = getImageUrlForDisplay(imageData, imageType);\r\n\r\n    // Debug logging\r\n    console.log('EnhancedImageDisplay Debug:', {\r\n      imageType,\r\n      imageData,\r\n      generatedUrl: imageUrl,\r\n      s3KeyField: `${imageType}_image_s3_url`,\r\n      s3KeyValue: imageData?.[`${imageType}_image_s3_url`],\r\n      fullUrlField: `${imageType}_image_full_url`,\r\n      fullUrlValue: imageData?.[`${imageType}_image_full_url`]\r\n    });\r\n\r\n    setCurrentImageUrl(imageUrl);\r\n  }, [imageData, imageType]);\r\n\r\n  const handleImageError = (event) => {\r\n    console.error('🚨 Image load error:', {\r\n      imageType,\r\n      currentImageUrl,\r\n      fallbackAttempts,\r\n      error: event?.target?.error,\r\n      src: event?.target?.src,\r\n      naturalWidth: event?.target?.naturalWidth,\r\n      naturalHeight: event?.target?.naturalHeight,\r\n      complete: event?.target?.complete\r\n    });\r\n\r\n    // Test if the URL is reachable\r\n    if (currentImageUrl) {\r\n      fetch(currentImageUrl, { method: 'HEAD' })\r\n        .then(response => {\r\n          console.log('🔍 URL HEAD check:', {\r\n            url: currentImageUrl,\r\n            status: response.status,\r\n            statusText: response.statusText,\r\n            headers: Object.fromEntries(response.headers.entries())\r\n          });\r\n        })\r\n        .catch(fetchError => {\r\n          console.error('🚨 URL HEAD check failed:', {\r\n            url: currentImageUrl,\r\n            error: fetchError.message\r\n          });\r\n        });\r\n    }\r\n\r\n    if (fallbackAttempts === 0) {\r\n      // First error: try alternative image type or fallback\r\n      const fallbackUrl = getFallbackImageUrl(imageData, imageType);\r\n      console.log('🔄 Trying fallback URL:', fallbackUrl);\r\n\r\n      if (fallbackUrl && fallbackUrl !== currentImageUrl) {\r\n        setCurrentImageUrl(fallbackUrl);\r\n        setFallbackAttempts(1);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // All fallbacks failed\r\n    console.error('❌ All image loading attempts failed for:', imageType);\r\n    setHasError(true);\r\n    if (onError) onError();\r\n  };\r\n\r\n  const getFallbackImageUrl = (imageData, imageType) => {\r\n    console.log('🔄 Getting fallback URL for:', imageType, imageData);\r\n\r\n    // Try direct S3 URL if we have the full URL field\r\n    const fullUrlField = `${imageType}_image_full_url`;\r\n    if (imageData[fullUrlField]) {\r\n      console.log('🔄 Trying direct S3 URL:', imageData[fullUrlField]);\r\n      return imageData[fullUrlField];\r\n    }\r\n\r\n    // Try GridFS if S3 failed\r\n    const gridfsIdField = `${imageType}_image_id`;\r\n    if (imageData[gridfsIdField]) {\r\n      console.log('🔄 Trying GridFS URL:', imageData[gridfsIdField]);\r\n      return `/api/pavement/get-image/${imageData[gridfsIdField]}`;\r\n    }\r\n\r\n    // Try alternative S3 proxy with different encoding\r\n    const s3KeyField = `${imageType}_image_s3_url`;\r\n    if (imageData[s3KeyField]) {\r\n      console.log('🔄 Trying alternative S3 proxy encoding');\r\n      const s3Key = imageData[s3KeyField];\r\n      const alternativeUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\r\n      console.log('🔄 Alternative proxy URL:', alternativeUrl);\r\n      return alternativeUrl;\r\n    }\r\n\r\n    console.log('❌ No fallback URL available');\r\n    return null;\r\n  };\r\n\r\n  if (hasError || !currentImageUrl) {\r\n    return (\r\n      <div className={`text-muted d-flex align-items-center justify-content-center ${className}`} style={style}>\r\n        <div className=\"text-center\">\r\n          <i className=\"fas fa-image-slash fa-2x mb-2\"></i>\r\n          <div>No image available</div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"position-relative\">\r\n      <img\r\n        src={currentImageUrl}\r\n        alt={alt}\r\n        className={className}\r\n        style={style}\r\n        onError={handleImageError}\r\n        loading=\"lazy\"\r\n        onLoad={() => {\r\n          console.log('✅ Image loaded successfully:', currentImageUrl);\r\n          setHasError(false);\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\n// ImageCard component to isolate state for each image\r\nconst ImageCard = ({ defect, defectType, defectIdKey }) => {\r\n  const [isOriginal, setIsOriginal] = useState(false);\r\n\r\n  // Safety check: return null if defect is not provided\r\n  if (!defect) {\r\n    return null;\r\n  }\r\n\r\n  const toggleView = (showOriginal) => {\r\n    setIsOriginal(showOriginal);\r\n  };\r\n\r\n  // Check if this is a multi-defect image\r\n  const isMultiDefect = defect.detected_defects && defect.detected_defects.length > 1;\r\n  const detectedDefects = defect.detected_defects || [];\r\n  \r\n  return (\r\n    <Col md={4} className=\"mb-4\" key={`${defectType}-${defect[defectIdKey] || defect.image_id || Math.random()}`}>\r\n      <Card className={`h-100 shadow-sm ${isMultiDefect ? 'border-warning' : ''}`}>\r\n        <Card.Header className={isMultiDefect ? 'bg-warning bg-opacity-10' : ''}>\r\n          <div className=\"d-flex justify-content-between align-items-center\">\r\n            <h6 className=\"mb-0\">\r\n              {defectType === 'cracks' ? `${defect.crack_type || 'Crack'} #${defect.crack_id || 'N/A'}` :\r\n               defectType === 'kerbs' ? `${defect.condition || 'Kerb'} #${defect.kerb_id || 'N/A'}` :\r\n               `Pothole #${defect.pothole_id || 'N/A'}`}\r\n            </h6>\r\n            {isMultiDefect && (\r\n              <small className=\"text-warning fw-bold\">\r\n                🔀 Multi-Defect\r\n              </small>\r\n            )}\r\n          </div>\r\n          {isMultiDefect && (\r\n            <div className=\"mt-1\">\r\n              <small className=\"text-muted\">\r\n                Also contains: {detectedDefects.filter(d => d !== defectType).join(', ')}\r\n              </small>\r\n            </div>\r\n          )}\r\n        </Card.Header>\r\n        <Card.Body>\r\n          <div className=\"mb-2 text-center\">\r\n            <EnhancedImageDisplay\r\n              imageData={defect}\r\n              imageType={isOriginal ? 'original' : 'processed'}\r\n              alt={`${defectType === 'cracks' ? 'Crack' : defectType === 'kerbs' ? 'Kerb' : 'Pothole'} ${defect[defectIdKey]}`}\r\n              className=\"img-fluid mb-2 border\"\r\n              style={{ maxHeight: \"200px\" }}\r\n              onError={() => {\r\n                console.warn(`Failed to load ${isOriginal ? 'original' : 'processed'} image for ${defectType} ${defect[defectIdKey]}`);\r\n              }}\r\n            />\r\n          </div>\r\n          <div className=\"small\">\r\n            {defectType === 'potholes' && (\r\n              <>\r\n                <p className=\"mb-1\"><strong>Area:</strong> {defect.area_cm2 ? defect.area_cm2.toFixed(2) : 'N/A'} cm²</p>\r\n                <p className=\"mb-1\"><strong>Depth:</strong> {defect.depth_cm ? defect.depth_cm.toFixed(2) : 'N/A'} cm</p>\r\n                <p className=\"mb-1\"><strong>Volume:</strong> {defect.volume ? defect.volume.toFixed(2) : 'N/A'}</p>\r\n              </>\r\n            )}\r\n            {defectType === 'cracks' && (\r\n              <>\r\n                <p className=\"mb-1\"><strong>Type:</strong> {defect.crack_type || 'N/A'}</p>\r\n                <p className=\"mb-1\"><strong>Area:</strong> {defect.area_cm2 ? defect.area_cm2.toFixed(2) : 'N/A'} cm²</p>\r\n                <p className=\"mb-1\"><strong>Range:</strong> {defect.area_range || 'N/A'}</p>\r\n              </>\r\n            )}\r\n            {defectType === 'kerbs' && (\r\n              <>\r\n                <p className=\"mb-1\"><strong>Type:</strong> {defect.kerb_type || 'N/A'}</p>\r\n                <p className=\"mb-1\"><strong>Length:</strong> {defect.length_m ? defect.length_m.toFixed(2) : 'N/A'} m</p>\r\n                <p className=\"mb-1\"><strong>Condition:</strong> {defect.condition || 'N/A'}</p>\r\n              </>\r\n            )}\r\n            <p className=\"mb-1\"><strong>Uploaded by:</strong> {defect.username || 'Unknown'}</p>\r\n            <p className=\"mb-1\"><strong>Timestamp:</strong> {defect.timestamp ? new Date(defect.timestamp).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' }) : 'N/A'}</p>\r\n            <div className=\"mt-2\">\r\n              <Button\r\n                variant={isOriginal ? 'primary' : 'outline-primary'}\r\n                size=\"sm\"\r\n                className=\"me-2\"\r\n                onClick={() => toggleView(true)}\r\n              >\r\n                Original\r\n              </Button>\r\n              <Button\r\n                variant={!isOriginal ? 'success' : 'outline-success'}\r\n                size=\"sm\"\r\n                onClick={() => toggleView(false)}\r\n              >\r\n                Processed\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </Card.Body>\r\n      </Card>\r\n    </Col>\r\n  );\r\n};\r\n\r\n// VideoCard component for displaying processed videos with representative frames\r\nconst VideoCard = ({ video }) => {\r\n  // State for download progress - must be declared before any conditional logic\r\n  const [downloadProgress, setDownloadProgress] = useState({});\r\n  const [downloadComplete, setDownloadComplete] = useState({});\r\n\r\n  // Safety check: return null if video is not provided\r\n  if (!video) {\r\n    return null;\r\n  }\r\n\r\n  const handleDownload = async (videoType) => {\r\n    try {\r\n      // Reset states\r\n      setDownloadProgress(prev => ({ ...prev, [videoType]: 0 }));\r\n      setDownloadComplete(prev => ({ ...prev, [videoType]: false }));\r\n\r\n      // Use the MongoDB _id for the download endpoint\r\n      const videoId = video._id || video.video_id;\r\n      const downloadUrl = `/api/pavement/get-s3-video/${videoId}/${videoType}`;\r\n\r\n      console.log(`🔄 Starting ${videoType} video download for ID: ${videoId}`);\r\n\r\n      // Fetch the video data with progress tracking\r\n      const response = await fetch(downloadUrl, {\r\n        method: 'GET',\r\n        headers: {\r\n          'Accept': 'video/mp4, video/*, */*'\r\n        }\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));\r\n        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);\r\n      }\r\n\r\n      // Get content length for progress calculation\r\n      const contentLength = response.headers.get('content-length');\r\n      const total = parseInt(contentLength, 10);\r\n      let loaded = 0;\r\n\r\n      // Create a readable stream to track progress\r\n      const reader = response.body.getReader();\r\n      const chunks = [];\r\n\r\n      while (true) {\r\n        const { done, value } = await reader.read();\r\n\r\n        if (done) break;\r\n\r\n        chunks.push(value);\r\n        loaded += value.length;\r\n\r\n        // Update progress\r\n        if (total) {\r\n          const progress = Math.round((loaded / total) * 100);\r\n          setDownloadProgress(prev => ({ ...prev, [videoType]: progress }));\r\n        }\r\n      }\r\n\r\n      // Create blob from chunks\r\n      const videoBlob = new Blob(chunks);\r\n      console.log(`✅ Downloaded ${videoType} video blob - Size: ${videoBlob.size} bytes`);\r\n\r\n      // Force the blob to be treated as video/mp4 if it's not already\r\n      let finalBlob = videoBlob;\r\n      if (videoBlob.type !== 'video/mp4') {\r\n        finalBlob = new Blob([videoBlob], { type: 'video/mp4' });\r\n        console.log(`🔄 Converted blob type to 'video/mp4'`);\r\n      }\r\n\r\n      // Create a blob URL and trigger download\r\n      const blobUrl = URL.createObjectURL(finalBlob);\r\n\r\n      // Use actual S3 filename if available, otherwise generate one\r\n      let filename;\r\n      if (videoType === 'original' && video.original_video_url) {\r\n        filename = video.original_video_url.split('/').pop();\r\n      } else if (videoType === 'processed' && video.processed_video_url) {\r\n        filename = video.processed_video_url.split('/').pop();\r\n      } else {\r\n        filename = `${videoType}_video_${(video.video_id || videoId).substring(0, 8)}.mp4`;\r\n      }\r\n\r\n      console.log(`📁 Download filename: ${filename}`);\r\n      console.log(`🔗 Blob URL created: ${blobUrl.substring(0, 50)}...`);\r\n\r\n      // Create and trigger download link\r\n      const link = document.createElement('a');\r\n      link.href = blobUrl;\r\n      link.download = filename;\r\n      link.style.display = 'none';\r\n      document.body.appendChild(link);\r\n\r\n      console.log(`🖱️ Triggering download click for ${filename}`);\r\n\r\n      // Try to trigger the download\r\n      try {\r\n        link.click();\r\n        console.log(`✅ Download triggered successfully`);\r\n\r\n        // Mark download as complete\r\n        setDownloadComplete(prev => ({ ...prev, [videoType]: true }));\r\n        setDownloadProgress(prev => ({ ...prev, [videoType]: 100 }));\r\n\r\n        // Show completion notification and reset progress after delay\r\n        setTimeout(() => {\r\n          alert(`✅ ${videoType.charAt(0).toUpperCase() + videoType.slice(1)} video download completed: ${filename}`);\r\n          // Reset progress after notification\r\n          setTimeout(() => {\r\n            setDownloadProgress(prev => ({ ...prev, [videoType]: 0 }));\r\n            setDownloadComplete(prev => ({ ...prev, [videoType]: false }));\r\n          }, 3000);\r\n        }, 500);\r\n\r\n      } catch (clickError) {\r\n        console.warn(`⚠️ Click failed, trying alternative method:`, clickError);\r\n\r\n        // Fallback: try using window.open\r\n        try {\r\n          const newWindow = window.open(blobUrl, '_blank');\r\n          if (newWindow) {\r\n            newWindow.document.title = filename;\r\n            console.log(`✅ Opened in new window as fallback`);\r\n\r\n            // Mark download as complete\r\n            setDownloadComplete(prev => ({ ...prev, [videoType]: true }));\r\n            setDownloadProgress(prev => ({ ...prev, [videoType]: 100 }));\r\n\r\n            setTimeout(() => {\r\n              alert(`✅ ${videoType.charAt(0).toUpperCase() + videoType.slice(1)} video download completed: ${filename}`);\r\n              // Reset progress after notification\r\n              setTimeout(() => {\r\n                setDownloadProgress(prev => ({ ...prev, [videoType]: 0 }));\r\n                setDownloadComplete(prev => ({ ...prev, [videoType]: false }));\r\n              }, 3000);\r\n            }, 500);\r\n          } else {\r\n            throw new Error('Popup blocked');\r\n          }\r\n        } catch (windowError) {\r\n          console.warn(`⚠️ Window.open failed, trying direct navigation:`, windowError);\r\n          // Last resort: direct navigation\r\n          window.location.href = blobUrl;\r\n\r\n          // Mark download as complete\r\n          setDownloadComplete(prev => ({ ...prev, [videoType]: true }));\r\n          setDownloadProgress(prev => ({ ...prev, [videoType]: 100 }));\r\n\r\n          setTimeout(() => {\r\n            alert(`✅ ${videoType.charAt(0).toUpperCase() + videoType.slice(1)} video download completed: ${filename}`);\r\n            // Reset progress after notification\r\n            setTimeout(() => {\r\n              setDownloadProgress(prev => ({ ...prev, [videoType]: 0 }));\r\n              setDownloadComplete(prev => ({ ...prev, [videoType]: false }));\r\n            }, 3000);\r\n          }, 500);\r\n        }\r\n      }\r\n\r\n      document.body.removeChild(link);\r\n\r\n      // Clean up the blob URL after a short delay to ensure download starts\r\n      setTimeout(() => {\r\n        URL.revokeObjectURL(blobUrl);\r\n        console.log(`🧹 Cleaned up blob URL for ${videoType} video`);\r\n      }, 2000); // Increased timeout to 2 seconds\r\n\r\n    } catch (error) {\r\n      console.error(`❌ Error downloading ${videoType} video:`, error);\r\n      setDownloadProgress(prev => ({ ...prev, [videoType]: 0 }));\r\n      setDownloadComplete(prev => ({ ...prev, [videoType]: false }));\r\n      alert(`Error downloading ${videoType} video: ${error.message}`);\r\n    }\r\n  };\r\n\r\n  const handleExport = async (format) => {\r\n    try {\r\n      const exportFormat = format.toLowerCase();\r\n      const videoId = video._id || video.video_id;\r\n\r\n      // Call backend API for export with detailed detection tables\r\n      const response = await fetch(`/api/dashboard/video-processing-export?format=${exportFormat}&video_id=${videoId}`, {\r\n        method: 'GET',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Export failed: ${response.statusText}`);\r\n      }\r\n\r\n      const data = await response.json();\r\n\r\n      if (exportFormat === 'pdf') {\r\n        // Handle PDF download\r\n        if (data.pdf_data) {\r\n          const byteCharacters = atob(data.pdf_data);\r\n          const byteNumbers = new Array(byteCharacters.length);\r\n          for (let i = 0; i < byteCharacters.length; i++) {\r\n            byteNumbers[i] = byteCharacters.charCodeAt(i);\r\n          }\r\n          const byteArray = new Uint8Array(byteNumbers);\r\n          const blob = new Blob([byteArray], { type: 'application/pdf' });\r\n\r\n          const link = document.createElement('a');\r\n          const url = URL.createObjectURL(blob);\r\n          link.setAttribute('href', url);\r\n          link.setAttribute('download', `video_${(video.video_id || videoId).substring(0, 8)}_detailed_report.pdf`);\r\n          link.style.visibility = 'hidden';\r\n          document.body.appendChild(link);\r\n          link.click();\r\n          document.body.removeChild(link);\r\n          URL.revokeObjectURL(url);\r\n        }\r\n      } else if (exportFormat === 'csv') {\r\n        // Handle CSV download\r\n        if (data.csv_data) {\r\n          const csvContent = data.csv_data.map(row => row.join(',')).join('\\n');\r\n          const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n\r\n          const link = document.createElement('a');\r\n          const url = URL.createObjectURL(blob);\r\n          link.setAttribute('href', url);\r\n          link.setAttribute('download', `video_${(video.video_id || videoId).substring(0, 8)}_detailed_report.csv`);\r\n          link.style.visibility = 'hidden';\r\n          document.body.appendChild(link);\r\n          link.click();\r\n          document.body.removeChild(link);\r\n          URL.revokeObjectURL(url);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(`Error exporting to ${format}:`, error);\r\n      alert(`Error exporting to ${format}: ${error.message}`);\r\n    }\r\n  };\r\n\r\n  const detectionCounts = video.detection_counts || {};\r\n  const totalDetections = detectionCounts.total || 0;\r\n\r\n  return (\r\n    <Col md={4} className=\"mb-4\" key={`video-${video.video_id}`}>\r\n      <Card className=\"h-100 shadow-sm\">\r\n        <Card.Header className=\"bg-info bg-opacity-10\">\r\n          <div className=\"d-flex justify-content-between align-items-center\">\r\n            <h6 className=\"mb-0\">\r\n              Video #{video.video_id.substring(0, 8)}...\r\n            </h6>\r\n            <small className=\"text-info fw-bold\">\r\n              📹 Video\r\n            </small>\r\n          </div>\r\n          <div className=\"mt-1\">\r\n            <small className=\"text-muted\">\r\n              Models: {video.models_run ? video.models_run.join(', ') : 'N/A'}\r\n            </small>\r\n          </div>\r\n        </Card.Header>\r\n        <Card.Body>\r\n          <div className=\"mb-2 text-center\">\r\n            {video.representative_frame ? (\r\n              <img\r\n                src={`data:image/jpeg;base64,${video.representative_frame}`}\r\n                alt=\"Video thumbnail\"\r\n                className=\"img-fluid mb-2 border\"\r\n                style={{ maxHeight: \"200px\" }}\r\n                onError={(e) => {\r\n                  console.warn(`Failed to load representative frame for video ${video.video_id}`);\r\n                  e.target.style.display = 'none';\r\n                }}\r\n              />\r\n            ) : (\r\n              <div className=\"d-flex align-items-center justify-content-center border\" style={{ height: \"200px\", backgroundColor: \"#f8f9fa\" }}>\r\n                <span className=\"text-muted\">No thumbnail available</span>\r\n              </div>\r\n            )}\r\n          </div>\r\n          <div className=\"small\">\r\n            <p className=\"mb-1\"><strong>Detections:</strong></p>\r\n            <ul className=\"mb-2\" style={{ paddingLeft: '20px' }}>\r\n              <li>Potholes: {detectionCounts.potholes || 0}</li>\r\n              <li>Cracks: {detectionCounts.cracks || 0}</li>\r\n              <li>Kerbs: {detectionCounts.kerbs || 0}</li>\r\n            </ul>\r\n            <p className=\"mb-1\"><strong>Total Detections:</strong> {totalDetections}</p>\r\n            <p className=\"mb-1\"><strong>Uploaded by:</strong> {video.username || 'Unknown'}</p>\r\n            <p className=\"mb-1\"><strong>Timestamp:</strong> {video.timestamp ? new Date(video.timestamp).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' }) : 'N/A'}</p>\r\n\r\n            {/* Video File Names */}\r\n            {video.original_video_url && (\r\n              <p className=\"mb-1\"><strong>Original File:</strong> <small className=\"text-muted\">{video.original_video_url.split('/').pop() || 'N/A'}</small></p>\r\n            )}\r\n            {video.processed_video_url && (\r\n              <p className=\"mb-1\"><strong>Processed File:</strong> <small className=\"text-muted\">{video.processed_video_url.split('/').pop() || 'N/A'}</small></p>\r\n            )}\r\n\r\n            {/* Download Buttons with Progress */}\r\n            <div className=\"mt-2 mb-2\">\r\n              <div className=\"mb-2\">\r\n                <Button\r\n                  variant=\"primary\"\r\n                  size=\"sm\"\r\n                  className=\"me-2 mb-1\"\r\n                  onClick={() => handleDownload('original')}\r\n                  disabled={!video.original_video_url || downloadProgress.original > 0}\r\n                >\r\n                  {downloadProgress.original > 0 ? `📥 Downloading... ${downloadProgress.original}%` : '📥 Original Video'}\r\n                  {downloadComplete.original && ' ✅'}\r\n                </Button>\r\n                {downloadProgress.original > 0 && downloadProgress.original < 100 && (\r\n                  <div className=\"progress mb-1\" style={{ height: '4px' }}>\r\n                    <div\r\n                      className=\"progress-bar bg-primary\"\r\n                      role=\"progressbar\"\r\n                      style={{ width: `${downloadProgress.original}%` }}\r\n                    ></div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              <div className=\"mb-2\">\r\n                <Button\r\n                  variant=\"success\"\r\n                  size=\"sm\"\r\n                  className=\"me-2 mb-1\"\r\n                  onClick={() => handleDownload('processed')}\r\n                  disabled={!video.processed_video_url || downloadProgress.processed > 0}\r\n                >\r\n                  {downloadProgress.processed > 0 ? `📥 Downloading... ${downloadProgress.processed}%` : '📥 Processed Video'}\r\n                  {downloadComplete.processed && ' ✅'}\r\n                </Button>\r\n                {downloadProgress.processed > 0 && downloadProgress.processed < 100 && (\r\n                  <div className=\"progress mb-1\" style={{ height: '4px' }}>\r\n                    <div\r\n                      className=\"progress-bar bg-success\"\r\n                      role=\"progressbar\"\r\n                      style={{ width: `${downloadProgress.processed}%` }}\r\n                    ></div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Export Buttons */}\r\n            <div className=\"mt-2\">\r\n              <Button\r\n                variant=\"outline-secondary\"\r\n                size=\"sm\"\r\n                className=\"me-2\"\r\n                onClick={() => handleExport('PDF')}\r\n              >\r\n                📄 Export PDF\r\n              </Button>\r\n              <Button\r\n                variant=\"outline-secondary\"\r\n                size=\"sm\"\r\n                onClick={() => handleExport('CSV')}\r\n              >\r\n                📊 Export CSV\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </Card.Body>\r\n      </Card>\r\n    </Col>\r\n  );\r\n};\r\n\r\nfunction Dashboard({ user }) {\r\n  const [statistics, setStatistics] = useState({\r\n    potholesDetected: 0,\r\n    cracksDetected: 0,\r\n    kerbsDetected: 0,\r\n    totalUsers: 0\r\n  });\r\n  const [weeklyData, setWeeklyData] = useState({\r\n    days: [],\r\n    issues: []\r\n  });\r\n  const [issuesByType, setIssuesByType] = useState({\r\n    types: [],\r\n    counts: []\r\n  });\r\n  const [dashboardData, setDashboardData] = useState({\r\n    potholes: {\r\n      count: 0,\r\n      by_size: {},\r\n      avg_volume: 0,\r\n      latest: []\r\n    },\r\n    cracks: {\r\n      count: 0,\r\n      by_type: {},\r\n      by_size: {},\r\n      latest: []\r\n    },\r\n    kerbs: {\r\n      count: 0,\r\n      by_condition: {},\r\n      latest: []\r\n    },\r\n    users: {\r\n      count: 0,\r\n      by_role: {},\r\n      latest: []\r\n    },\r\n    videos: {\r\n      count: 0,\r\n      latest: []\r\n    }\r\n  });\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  \r\n  // Date filter state\r\n  const [startDate, setStartDate] = useState('');\r\n  const [endDate, setEndDate] = useState('');\r\n  const [dateFilterApplied, setDateFilterApplied] = useState(false);\r\n\r\n  // User filter state\r\n  const [usersList, setUsersList] = useState([]);\r\n  const [selectedUser, setSelectedUser] = useState('');\r\n  const [userFilterApplied, setUserFilterApplied] = useState(false);\r\n\r\n  // Defect type filter state\r\n  const [defectFilters, setDefectFilters] = useState({\r\n    potholes: true,\r\n    cracks: true,\r\n    kerbs: true\r\n  });\r\n  \r\n  // Filtered issues state\r\n  const [filteredIssuesByType, setFilteredIssuesByType] = useState({\r\n    types: [],\r\n    counts: []\r\n  });\r\n  \r\n  // Dashboard tab state\r\n  const [activeTab, setActiveTab] = useState('dashboard');\r\n\r\n  // Set default date range to previous week and auto-apply filter\r\n  useEffect(() => {\r\n    const currentDate = new Date();\r\n    const lastWeek = new Date();\r\n    lastWeek.setDate(currentDate.getDate() - 6); // 6 days ago + today = 7 days\r\n\r\n    const formattedEndDate = currentDate.toISOString().split('T')[0];\r\n    const formattedStartDate = lastWeek.toISOString().split('T')[0];\r\n\r\n    setEndDate(formattedEndDate);\r\n    setStartDate(formattedStartDate);\r\n    fetchData({ startDate: formattedStartDate, endDate: formattedEndDate });\r\n  }, []);\r\n\r\n  // Fetch dashboard data from backend\r\n  const fetchData = async (filters = {}) => {\r\n    try {\r\n      setLoading(true);\r\n      \r\n      // Add filters to requests if provided\r\n      const params = {};\r\n      if (filters.startDate) params.start_date = filters.startDate;\r\n      if (filters.endDate) params.end_date = filters.endDate;\r\n      if (filters.username) params.username = filters.username;\r\n      if (user?.role) params.user_role = user.role;\r\n      \r\n      // Get overview statistics\r\n      const statsResponse = await axios.get('/api/dashboard/statistics', { params });\r\n      if (statsResponse.data.success) {\r\n        setStatistics({\r\n          potholesDetected: statsResponse.data.data.issues_by_type.potholes,\r\n          cracksDetected: statsResponse.data.data.issues_by_type.cracks,\r\n          kerbsDetected: statsResponse.data.data.issues_by_type.kerbs,\r\n          totalUsers: statistics.totalUsers // Preserve the existing user count\r\n        });\r\n      }\r\n      \r\n      // Get weekly trend data\r\n      const weeklyResponse = await axios.get('/api/dashboard/weekly-trend', { params });\r\n      setWeeklyData({\r\n        days: weeklyResponse.data.days,\r\n        issues: weeklyResponse.data.issues\r\n      });\r\n      \r\n      // Get issues by type\r\n      const typesResponse = await axios.get('/api/dashboard/issues-by-type', { params });\r\n      setIssuesByType({\r\n        types: typesResponse.data.types,\r\n        counts: typesResponse.data.counts\r\n      });\r\n      \r\n      // Get detailed dashboard data including latest images with enhanced S3-MongoDB integration\r\n      let dashboardResponse;\r\n      try {\r\n        // Try enhanced API endpoint first (with comprehensive S3-MongoDB integration)\r\n        dashboardResponse = await axios.get('/api/dashboard/summary-v2', { params });\r\n        console.log('✅ Using enhanced dashboard API with comprehensive S3-MongoDB integration');\r\n      } catch (enhancedError) {\r\n        console.warn('⚠️ Enhanced API not available, falling back to standard API:', enhancedError.message);\r\n        // Fallback to original API\r\n        dashboardResponse = await axios.get('/api/dashboard/summary', { params });\r\n        console.log('✅ Using standard dashboard API');\r\n      }\r\n\r\n      if (dashboardResponse.data.success) {\r\n        const dashboardData = dashboardResponse.data.data;\r\n\r\n        // Calculate multi-defect statistics\r\n        const multiDefectStats = {\r\n          totalImages: 0,\r\n          multiDefectImages: 0,\r\n          singleDefectImages: 0,\r\n          categoryBreakdown: {\r\n            potholes: 0,\r\n            cracks: 0,\r\n            kerbs: 0\r\n          }\r\n        };\r\n        \r\n        // Count multi-defect images from each category\r\n        ['potholes', 'cracks', 'kerbs'].forEach(category => {\r\n          const categoryImages = dashboardData[category].latest || [];\r\n          categoryImages.forEach(item => {\r\n            if (item.multi_defect_image) {\r\n              multiDefectStats.multiDefectImages++;\r\n            }\r\n            multiDefectStats.totalImages++;\r\n            multiDefectStats.categoryBreakdown[category]++;\r\n          });\r\n        });\r\n        \r\n        multiDefectStats.singleDefectImages = multiDefectStats.totalImages - multiDefectStats.multiDefectImages;\r\n        \r\n        // Add multi-defect statistics to dashboard data\r\n        dashboardData.multiDefectStats = multiDefectStats;\r\n        \r\n        setDashboardData(dashboardData);\r\n      }\r\n      \r\n      // Get users data\r\n      try {\r\n        const usersResponse = await axios.get('/api/users/summary', { params });\r\n        if (usersResponse.data.success) {\r\n          setStatistics(prevStats => ({\r\n            ...prevStats,\r\n            totalUsers: usersResponse.data.total_users || 0\r\n          }));\r\n          \r\n          // Ensure users data is properly set in dashboardData\r\n          setDashboardData(prevData => ({\r\n            ...prevData,\r\n            users: {\r\n              count: usersResponse.data.total_users || 0,\r\n              by_role: usersResponse.data.roles_distribution || {},\r\n              latest: usersResponse.data.recent_users || []\r\n            }\r\n          }));\r\n        }\r\n      } catch (userErr) {\r\n        console.error('Error fetching user data:', userErr);\r\n        // Non-critical error, continue with other data\r\n      }\r\n      \r\n      setLoading(false);\r\n    } catch (err) {\r\n      setError('Error fetching dashboard data');\r\n      setLoading(false);\r\n      console.error('Error fetching data:', err);\r\n    }\r\n  };\r\n\r\n  // Fetch users list for filter dropdown\r\n  useEffect(() => {\r\n    const fetchUsers = async () => {\r\n      try {\r\n        const params = {};\r\n        if (user?.role) params.user_role = user.role;\r\n        \r\n        const response = await axios.get('/api/users/all', { params });\r\n        if (response.data.success) {\r\n          // Users are already filtered by the backend based on RBAC\r\n          setUsersList(response.data.users);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error fetching users:', error);\r\n      }\r\n    };\r\n    \r\n    fetchUsers();\r\n  }, [user]);\r\n\r\n  // Handle date filter application\r\n  const handleApplyDateFilter = () => {\r\n    if (startDate && endDate) {\r\n      fetchData({ \r\n        startDate, \r\n        endDate,\r\n        username: selectedUser || undefined\r\n      });\r\n      setDateFilterApplied(true);\r\n    }\r\n  };\r\n\r\n  // Handle date filter reset\r\n  const handleResetDateFilter = () => {\r\n    const currentDate = new Date();\r\n    const lastWeek = new Date();\r\n    lastWeek.setDate(currentDate.getDate() - 6); // 6 days ago + today = 7 days\r\n\r\n    const newEndDate = currentDate.toISOString().split('T')[0];\r\n    const newStartDate = lastWeek.toISOString().split('T')[0];\r\n    \r\n    setEndDate(newEndDate);\r\n    setStartDate(newStartDate);\r\n    fetchData({ \r\n      startDate: newStartDate, \r\n      endDate: newEndDate,\r\n      username: selectedUser || undefined\r\n    });\r\n    setDateFilterApplied(false);\r\n  };\r\n\r\n  // Handle user filter application\r\n  const handleApplyUserFilter = () => {\r\n    fetchData({\r\n      startDate,\r\n      endDate,\r\n      username: selectedUser || undefined\r\n    });\r\n    setUserFilterApplied(!!selectedUser);\r\n  };\r\n\r\n  // Handle user filter reset\r\n  const handleResetUserFilter = () => {\r\n    setSelectedUser('');\r\n    fetchData({\r\n      startDate,\r\n      endDate\r\n    });\r\n    setUserFilterApplied(false);\r\n  };\r\n\r\n  // Handle user selection\r\n  const handleUserChange = (e) => {\r\n    setSelectedUser(e.target.value);\r\n  };\r\n\r\n  // Filter the issues by type whenever the filters or data changes\r\n  useEffect(() => {\r\n    if (issuesByType.types.length > 0) {\r\n      const filteredTypes = [];\r\n      const filteredCounts = [];\r\n      \r\n      issuesByType.types.forEach((type, index) => {\r\n        if (\r\n          (type.includes('Pothole') && defectFilters.potholes) ||\r\n          (type.includes('Crack') && defectFilters.cracks) ||\r\n          (type.includes('Kerb') && defectFilters.kerbs)\r\n        ) {\r\n          filteredTypes.push(type);\r\n          filteredCounts.push(issuesByType.counts[index]);\r\n        }\r\n      });\r\n      \r\n      setFilteredIssuesByType({\r\n        types: filteredTypes,\r\n        counts: filteredCounts\r\n      });\r\n    }\r\n  }, [issuesByType, defectFilters]);\r\n\r\n  // Handle defect filter change\r\n  const handleDefectFilterChange = (defectType) => {\r\n    setDefectFilters(prev => ({\r\n      ...prev,\r\n      [defectType]: !prev[defectType]\r\n    }));\r\n  };\r\n\r\n  // Add export handlers\r\n  const handleDownloadPDF = () => {\r\n    const doc = new jsPDF();\r\n    let yPosition = 20;\r\n    \r\n    // Header\r\n    doc.setFontSize(20);\r\n    doc.setFont('helvetica', 'bold');\r\n    doc.text('Road AI Safety Enhancement - Dashboard Report', 105, yPosition, { align: 'center' });\r\n    yPosition += 15;\r\n    \r\n    doc.setFontSize(12);\r\n    doc.setFont('helvetica', 'normal');\r\n    doc.text(`Generated on: ${new Date().toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })}`, 105, yPosition, { align: 'center' });\r\n    yPosition += 20;\r\n    \r\n    // Date Range Info\r\n    if (dateFilterApplied) {\r\n      doc.setFontSize(14);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Date Range Filter:', 14, yPosition);\r\n      yPosition += 8;\r\n      doc.setFontSize(12);\r\n      doc.setFont('helvetica', 'normal');\r\n      doc.text(`From: ${new Date(startDate).toLocaleDateString()} To: ${new Date(endDate).toLocaleDateString()}`, 14, yPosition);\r\n      yPosition += 15;\r\n    }\r\n    \r\n    // Statistics Summary\r\n    doc.setFontSize(16);\r\n    doc.setFont('helvetica', 'bold');\r\n    doc.text('Statistics Summary', 14, yPosition);\r\n    yPosition += 10;\r\n    \r\n    doc.setFontSize(12);\r\n    doc.setFont('helvetica', 'normal');\r\n    doc.text(`Total Potholes Detected: ${statistics.potholesDetected}`, 14, yPosition);\r\n    yPosition += 7;\r\n    doc.text(`Total Cracks Detected: ${statistics.cracksDetected}`, 14, yPosition);\r\n    yPosition += 7;\r\n    doc.text(`Total Kerbs Detected: ${statistics.kerbsDetected}`, 14, yPosition);\r\n    yPosition += 7;\r\n    doc.text(`Total Users: ${statistics.totalUsers}`, 14, yPosition);\r\n    yPosition += 15;\r\n    \r\n    // Infrastructure Distribution\r\n    doc.setFontSize(16);\r\n    doc.setFont('helvetica', 'bold');\r\n    doc.text('Infrastructure Distribution', 14, yPosition);\r\n    yPosition += 10;\r\n    \r\n    const totalIssues = statistics.potholesDetected + statistics.cracksDetected + statistics.kerbsDetected;\r\n    if (totalIssues > 0) {\r\n      const potholePercent = ((statistics.potholesDetected / totalIssues) * 100).toFixed(1);\r\n      const crackPercent = ((statistics.cracksDetected / totalIssues) * 100).toFixed(1);\r\n      const kerbPercent = ((statistics.kerbsDetected / totalIssues) * 100).toFixed(1);\r\n      \r\n      doc.setFontSize(12);\r\n      doc.setFont('helvetica', 'normal');\r\n      doc.text(`Potholes: ${statistics.potholesDetected} (${potholePercent}%)`, 14, yPosition);\r\n      yPosition += 7;\r\n      doc.text(`Cracks: ${statistics.cracksDetected} (${crackPercent}%)`, 14, yPosition);\r\n      yPosition += 7;\r\n      doc.text(`Kerbs: ${statistics.kerbsDetected} (${kerbPercent}%)`, 14, yPosition);\r\n      yPosition += 15;\r\n    }\r\n    \r\n    // User Overview\r\n    if (dashboardData.users?.latest && dashboardData.users.latest.length > 0) {\r\n      doc.setFontSize(16);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Recent Users', 14, yPosition);\r\n      yPosition += 10;\r\n      \r\n      const userTableData = dashboardData.users.latest.map((user, idx) => [\r\n        idx + 1,\r\n        user.username,\r\n        user.role,\r\n        new Date(user.last_login).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })\r\n      ]);\r\n      \r\n      autoTable(doc, {\r\n        head: [['#', 'Username', 'Role', 'Last Login']],\r\n        body: userTableData,\r\n        startY: yPosition,\r\n        margin: { top: 10 },\r\n        styles: { fontSize: 10 },\r\n        headStyles: { fillColor: [0, 123, 255] }\r\n      });\r\n      \r\n      yPosition = doc.lastAutoTable.finalY + 15;\r\n    }\r\n    \r\n    // Potholes Section\r\n    if (dashboardData.potholes.latest && dashboardData.potholes.latest.length > 0) {\r\n      doc.setFontSize(16);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Potholes Detected', 14, yPosition);\r\n      yPosition += 10;\r\n      \r\n      const potholeTableData = dashboardData.potholes.latest.map((defect, idx) => [\r\n        idx + 1,\r\n        defect.area_cm2 ? defect.area_cm2.toFixed(2) + ' cm²' : 'N/A',\r\n        defect.depth_cm ? defect.depth_cm.toFixed(2) + ' cm' : 'N/A',\r\n        defect.volume ? defect.volume.toFixed(2) : 'N/A',\r\n        defect.username || 'Unknown',\r\n        new Date(defect.timestamp).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })\r\n      ]);\r\n      \r\n      autoTable(doc, {\r\n        head: [['#', 'Area', 'Depth', 'Volume', 'Uploaded By', 'Timestamp']],\r\n        body: potholeTableData,\r\n        startY: yPosition,\r\n        margin: { top: 10 },\r\n        styles: { fontSize: 9 },\r\n        headStyles: { fillColor: [220, 53, 69] }\r\n      });\r\n      \r\n      yPosition = doc.lastAutoTable.finalY + 15;\r\n    }\r\n    \r\n    // Cracks Section\r\n    if (dashboardData.cracks.latest && dashboardData.cracks.latest.length > 0) {\r\n      doc.setFontSize(16);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Cracks Detected', 14, yPosition);\r\n      yPosition += 10;\r\n      \r\n      const crackTableData = dashboardData.cracks.latest.map((defect, idx) => [\r\n        idx + 1,\r\n        defect.crack_type || 'Unknown',\r\n        defect.area_cm2 ? defect.area_cm2.toFixed(2) + ' cm²' : 'N/A',\r\n        defect.area_range || 'N/A',\r\n        defect.username || 'Unknown',\r\n        new Date(defect.timestamp).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })\r\n      ]);\r\n      \r\n      autoTable(doc, {\r\n        head: [['#', 'Type', 'Area', 'Range', 'Uploaded By', 'Timestamp']],\r\n        body: crackTableData,\r\n        startY: yPosition,\r\n        margin: { top: 10 },\r\n        styles: { fontSize: 9 },\r\n        headStyles: { fillColor: [40, 167, 69] }\r\n      });\r\n      \r\n      yPosition = doc.lastAutoTable.finalY + 15;\r\n    }\r\n    \r\n    // Kerbs Section\r\n    if (dashboardData.kerbs.latest && dashboardData.kerbs.latest.length > 0) {\r\n      doc.setFontSize(16);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Kerbs Detected', 14, yPosition);\r\n      yPosition += 10;\r\n      \r\n      const kerbTableData = dashboardData.kerbs.latest.map((defect, idx) => [\r\n        idx + 1,\r\n        defect.kerb_type || 'Unknown',\r\n        defect.length_m ? defect.length_m.toFixed(2) + ' m' : 'N/A',\r\n        defect.condition || 'Unknown',\r\n        defect.username || 'Unknown',\r\n        new Date(defect.timestamp).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })\r\n      ]);\r\n      \r\n      autoTable(doc, {\r\n        head: [['#', 'Type', 'Length', 'Condition', 'Uploaded By', 'Timestamp']],\r\n        body: kerbTableData,\r\n        startY: yPosition,\r\n        margin: { top: 10 },\r\n        styles: { fontSize: 9 },\r\n        headStyles: { fillColor: [0, 123, 255] }\r\n      });\r\n    }\r\n    \r\n    // Footer\r\n    const pageCount = doc.internal.getNumberOfPages();\r\n    for (let i = 1; i <= pageCount; i++) {\r\n      doc.setPage(i);\r\n      doc.setFontSize(10);\r\n      doc.setFont('helvetica', 'italic');\r\n      doc.text(`Page ${i} of ${pageCount}`, 105, doc.internal.pageSize.height - 10, { align: 'center' });\r\n    }\r\n    \r\n    doc.save('Dashboard_Report.pdf');\r\n  };\r\n\r\n  const handleDownloadExcel = () => {\r\n    const wsData = [\r\n      ['#', 'Area (cm²)', 'Depth (cm)', 'Volume', 'Uploaded By', 'Timestamp'],\r\n      ...(dashboardData.potholes.latest || []).map((defect, idx) => [\r\n        idx + 1,\r\n        defect.area,\r\n        defect.depth,\r\n        defect.volume,\r\n        defect.username,\r\n        new Date(defect.timestamp).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })\r\n      ])\r\n    ];\r\n    const ws = XLSX.utils.aoa_to_sheet(wsData);\r\n    const wb = XLSX.utils.book_new();\r\n    XLSX.utils.book_append_sheet(wb, ws, 'Processed Report');\r\n    const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });\r\n    saveAs(new Blob([wbout], { type: 'application/octet-stream' }), 'Processed_Report.xlsx');\r\n  };\r\n\r\n  return (\r\n    <Container fluid className=\"dashboard-container\">\r\n      {/* Filters Card */}\r\n      <Card className=\"mb-3 shadow-sm dashboard-card filters-card\">\r\n        <Card.Header className=\"bg-primary text-white py-2\">\r\n          <h6 className=\"mb-0\">Filters</h6>\r\n        </Card.Header>\r\n        <Card.Body className=\"py-3\">\r\n          <Row className=\"g-3\">\r\n            <Col lg={6}>\r\n              <div className=\"filter-section\">\r\n                <h6 className=\"mb-2\">Date Range</h6>\r\n                <div className=\"filter-controls\">\r\n                  <div className=\"filter-field\">\r\n                    <Form.Group>\r\n                      <Form.Label className=\"small mb-1\">Start Date</Form.Label>\r\n                      <Form.Control\r\n                        type=\"date\"\r\n                        value={startDate}\r\n                        onChange={(e) => setStartDate(e.target.value)}\r\n                        size=\"sm\"\r\n                      />\r\n                    </Form.Group>\r\n                  </div>\r\n                  <div className=\"filter-field\">\r\n                    <Form.Group>\r\n                      <Form.Label className=\"small mb-1\">End Date</Form.Label>\r\n                      <Form.Control\r\n                        type=\"date\"\r\n                        value={endDate}\r\n                        onChange={(e) => setEndDate(e.target.value)}\r\n                        size=\"sm\"\r\n                      />\r\n                    </Form.Group>\r\n                  </div>\r\n                  <div className=\"filter-actions\">\r\n                    <Button\r\n                      size=\"sm\"\r\n                      variant=\"primary\"\r\n                      onClick={handleApplyDateFilter}\r\n                      disabled={!startDate || !endDate}\r\n                    >\r\n                      Apply\r\n                    </Button>\r\n                    <Button\r\n                      size=\"sm\"\r\n                      variant=\"outline-secondary\"\r\n                      onClick={handleResetDateFilter}\r\n                      disabled={!dateFilterApplied}\r\n                    >\r\n                      Reset\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n                {dateFilterApplied && (\r\n                  <div className=\"filter-status text-success mt-2 p-2\">\r\n                    <small>Showing data from {new Date(startDate).toLocaleDateString()} to {new Date(endDate).toLocaleDateString()}</small>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </Col>\r\n            <Col lg={6}>\r\n              <div className=\"filter-section\">\r\n                <h6 className=\"mb-2\">User Filter</h6>\r\n                <div className=\"filter-controls\">\r\n                  <div className=\"filter-field\">\r\n                    <Form.Group>\r\n                      <Form.Label className=\"small mb-1\">Select User</Form.Label>\r\n                      <Form.Select\r\n                        value={selectedUser}\r\n                        onChange={handleUserChange}\r\n                        size=\"sm\"\r\n                      >\r\n                        <option value=\"\">All Users</option>\r\n                        {usersList.map((user, index) => (\r\n                          <option key={index} value={user.username}>\r\n                            {user.username} ({user.role})\r\n                          </option>\r\n                        ))}\r\n                      </Form.Select>\r\n                    </Form.Group>\r\n                  </div>\r\n                  <div className=\"filter-actions\">\r\n                    <Button\r\n                      size=\"sm\"\r\n                      variant=\"primary\"\r\n                      onClick={handleApplyUserFilter}\r\n                    >\r\n                      Apply\r\n                    </Button>\r\n                    <Button\r\n                      size=\"sm\"\r\n                      variant=\"outline-secondary\"\r\n                      onClick={handleResetUserFilter}\r\n                      disabled={!userFilterApplied}\r\n                    >\r\n                      Reset\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n                {userFilterApplied && (\r\n                  <div className=\"filter-status text-success mt-2 p-2\">\r\n                    <small>Showing data for user: {selectedUser}</small>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </Col>\r\n          </Row>\r\n        </Card.Body>\r\n      </Card>\r\n      \r\n      {loading ? (\r\n        <div className=\"text-center\">\r\n          <div className=\"spinner-border text-primary\" role=\"status\">\r\n            <span className=\"visually-hidden\">Loading...</span>\r\n          </div>\r\n        </div>\r\n      ) : error ? (\r\n        <div className=\"alert alert-danger p-3\">{error}</div>\r\n      ) : (\r\n        <>\r\n          {/* Dashboard Tabs */}\r\n          <Card className=\"mb-3 shadow-sm dashboard-card\">\r\n            <Card.Body className=\"p-0\">\r\n              <Tabs \r\n                activeKey={activeTab} \r\n                onSelect={(k) => setActiveTab(k)} \r\n                className=\"dashboard-tabs\"\r\n              >\r\n                {/* Dashboard View Tab */}\r\n                <Tab eventKey=\"dashboard\" title=\"Dashboard View\">\r\n                  <div className=\"p-3\">\r\n                    {/* Top Stats Cards */}\r\n                    <Row className=\"mb-3 g-3\">\r\n                      <Col md={3}>\r\n                        <Card className=\"h-100 shadow-sm dashboard-card stats-card\">\r\n                          <Card.Body className=\"text-center py-3\">\r\n                            <h6 className=\"card-title mb-2\">Potholes</h6>\r\n                            <h3 className=\"text-primary mb-0\">{statistics.potholesDetected}</h3>\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                      <Col md={3}>\r\n                        <Card className=\"h-100 shadow-sm dashboard-card stats-card\">\r\n                          <Card.Body className=\"text-center py-3\">\r\n                            <h6 className=\"card-title mb-2\">Cracks</h6>\r\n                            <h3 className=\"text-primary mb-0\">{statistics.cracksDetected}</h3>\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                      <Col md={3}>\r\n                        <Card className=\"h-100 shadow-sm dashboard-card stats-card\">\r\n                          <Card.Body className=\"text-center py-3\">\r\n                            <h6 className=\"card-title mb-2\">Kerbs</h6>\r\n                            <h3 className=\"text-primary mb-0\">{statistics.kerbsDetected}</h3>\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                      <Col md={3}>\r\n                        <Card className=\"h-100 shadow-sm dashboard-card stats-card\">\r\n                          <Card.Body className=\"text-center py-3\">\r\n                            <h6 className=\"card-title mb-2\">Users</h6>\r\n                            <h3 className=\"text-success mb-0\">{statistics.totalUsers}</h3>\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                    </Row>\r\n\r\n                    {/* Charts Row */}\r\n                    <Row className=\"mb-3 g-3\">\r\n                      <Col md={6}>\r\n                        <Card className=\"shadow-sm dashboard-card\">\r\n                          <Card.Header className=\"bg-primary text-white py-2\">\r\n                            <h6 className=\"mb-0\">Weekly Detection Trend</h6>\r\n                          </Card.Header>\r\n                          <Card.Body className=\"py-3\">\r\n                            <ChartContainer\r\n                              data={[\r\n                                {\r\n                                  x: weeklyData.days,\r\n                                  y: weeklyData.issues,\r\n                                  type: 'scatter',\r\n                                  mode: 'lines+markers',\r\n                                  marker: { color: '#007bff' }\r\n                                }\r\n                              ]}\r\n                              layout={{\r\n                                xaxis: { title: 'Day' },\r\n                                yaxis: { title: 'Issues Detected' }\r\n                              }}\r\n                            />\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                      \r\n                      <Col md={6}>\r\n                        <Card className=\"shadow-sm dashboard-card\">\r\n                          <Card.Header className=\"bg-primary text-white py-2\">\r\n                            <h6 className=\"mb-0\">Issues by Type</h6>\r\n                          </Card.Header>\r\n                          <Card.Body className=\"py-3\">\r\n                            <ChartContainer\r\n                              data={[\r\n                                {\r\n                                  type: 'bar',\r\n                                  x: filteredIssuesByType.types,\r\n                                  y: filteredIssuesByType.counts,\r\n                                  marker: {\r\n                                    color: filteredIssuesByType.types.map(type => {\r\n                                      if (type.includes('Pothole')) return '#007bff';\r\n                                      if (type.includes('Crack')) return '#28a745';\r\n                                      if (type.includes('Kerb')) return '#dc3545';\r\n                                      return '#6c757d';\r\n                                    })\r\n                                  }\r\n                                }\r\n                              ]}\r\n                              layout={{\r\n                                xaxis: { \r\n                                  title: 'Issue Type',\r\n                                  tickangle: -45,\r\n                                  automargin: true\r\n                                },\r\n                                yaxis: { title: 'Count' },\r\n                                margin: { t: 10, b: 80, l: 50, r: 10 }\r\n                              }}\r\n                              showLegend={true}\r\n                              legendItems={[\r\n                                {\r\n                                  label: 'Potholes',\r\n                                  color: '#007bff',\r\n                                  checked: defectFilters.potholes,\r\n                                  onChange: () => handleDefectFilterChange('potholes')\r\n                                },\r\n                                {\r\n                                  label: 'Cracks',\r\n                                  color: '#28a745',\r\n                                  checked: defectFilters.cracks,\r\n                                  onChange: () => handleDefectFilterChange('cracks')\r\n                                },\r\n                                {\r\n                                  label: 'Kerbs',\r\n                                  color: '#dc3545',\r\n                                  checked: defectFilters.kerbs,\r\n                                  onChange: () => handleDefectFilterChange('kerbs')\r\n                                }\r\n                              ]}\r\n                              className=\"compact-legend\"\r\n                            />\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                    </Row>\r\n\r\n                    {/* Infrastructure Distribution */}\r\n                    <Row className=\"mb-3\">\r\n                      <Col md={12}>\r\n                        <Card className=\"shadow-sm dashboard-card\">\r\n                          <Card.Header className=\"bg-primary text-white py-2\">\r\n                            <h6 className=\"mb-0\">Infrastructure Distribution</h6>\r\n                          </Card.Header>\r\n                          <Card.Body className=\"py-3\">\r\n                            <ChartContainer\r\n                              data={[\r\n                                {\r\n                                  type: 'pie',\r\n                                  labels: ['Potholes', 'Cracks', 'Kerbs'],\r\n                                  values: [\r\n                                    statistics.potholesDetected,\r\n                                    statistics.cracksDetected,\r\n                                    statistics.kerbsDetected\r\n                                  ],\r\n                                  marker: {\r\n                                    colors: ['#007bff', '#28a745', '#dc3545']\r\n                                  },\r\n                                  textinfo: \"label+percent\",\r\n                                  insidetextorientation: \"radial\"\r\n                                }\r\n                              ]}\r\n                              layout={{\r\n                                height: 300\r\n                              }}\r\n                              isPieChart={true}\r\n                            />\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                    </Row>\r\n\r\n                    {/* All Uploaded Images Section */}\r\n                    <Row className=\"mb-3\">\r\n                      <Col md={12}>\r\n                        <Card className=\"shadow-sm dashboard-card\">\r\n                          <Card.Header className=\"bg-primary text-white py-2\">\r\n                            <h6 className=\"mb-0\">All Uploaded Images</h6>\r\n                          </Card.Header>\r\n                          <Card.Body className=\"py-3\">\r\n                            <Tabs defaultActiveKey=\"potholes\" className=\"mb-2\">\r\n                              <Tab eventKey=\"potholes\" title={`Potholes (${dashboardData.potholes.latest.length})`}>\r\n                                <div style={{ maxHeight: '700px', overflowY: 'auto', paddingRight: '10px' }}>\r\n                                  <Row>\r\n                                    {dashboardData.potholes.latest.map((pothole, index) => (\r\n                                      <ImageCard\r\n                                        key={`pothole-${pothole.pothole_id || pothole.image_id || index}`}\r\n                                        defect={pothole}\r\n                                        defectType=\"potholes\"\r\n                                        defectIdKey=\"pothole_id\"\r\n                                      />\r\n                                    ))}\r\n                                  </Row>\r\n                                </div>\r\n                              </Tab>\r\n                              <Tab eventKey=\"cracks\" title={`Cracks (${dashboardData.cracks.latest.length})`}>\r\n                                <div style={{ maxHeight: '700px', overflowY: 'auto', paddingRight: '10px' }}>\r\n                                  <Row>\r\n                                    {dashboardData.cracks.latest.map((crack, index) => (\r\n                                      <ImageCard\r\n                                        key={`crack-${crack.crack_id || crack.image_id || index}`}\r\n                                        defect={crack}\r\n                                        defectType=\"cracks\"\r\n                                        defectIdKey=\"crack_id\"\r\n                                      />\r\n                                    ))}\r\n                                  </Row>\r\n                                </div>\r\n                              </Tab>\r\n                              <Tab eventKey=\"kerbs\" title={`Kerbs (${dashboardData.kerbs.latest.length})`}>\r\n                                <div style={{ maxHeight: '700px', overflowY: 'auto', paddingRight: '10px' }}>\r\n                                  <Row>\r\n                                    {dashboardData.kerbs && dashboardData.kerbs.latest && dashboardData.kerbs.latest.length > 0 ? (\r\n                                      dashboardData.kerbs.latest.map((kerb, index) => (\r\n                                        <ImageCard\r\n                                          key={`kerb-${kerb.kerb_id || kerb.image_id || index}`}\r\n                                          defect={kerb}\r\n                                          defectType=\"kerbs\"\r\n                                          defectIdKey=\"kerb_id\"\r\n                                        />\r\n                                      ))\r\n                                    ) : (\r\n                                      <Col>\r\n                                        <div className=\"alert alert-info p-3\">\r\n                                          No kerb images available yet. Upload some kerb images using the Pavement Analysis tool.\r\n                                        </div>\r\n                                      </Col>\r\n                                    )}\r\n                                  </Row>\r\n                                </div>\r\n                              </Tab>\r\n                            </Tabs>\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                    </Row>\r\n\r\n                    {/* All Videos Processed Section */}\r\n                    <Row className=\"mb-3\">\r\n                      <Col md={12}>\r\n                        <Card className=\"shadow-sm dashboard-card\">\r\n                          <Card.Header className=\"bg-info text-white py-2\">\r\n                            <h6 className=\"mb-0\">All Videos Processed ({dashboardData.videos?.latest?.length || 0})</h6>\r\n                          </Card.Header>\r\n                          <Card.Body className=\"py-3\">\r\n                            {dashboardData.videos?.latest && dashboardData.videos.latest.length > 0 ? (\r\n                              <div style={{ maxHeight: '700px', overflowY: 'auto', paddingRight: '10px' }}>\r\n                                <Row>\r\n                                  {dashboardData.videos.latest.map((video, index) => (\r\n                                    <VideoCard\r\n                                      key={`video-${video.video_id || index}`}\r\n                                      video={video}\r\n                                    />\r\n                                  ))}\r\n                                </Row>\r\n                              </div>\r\n                            ) : (\r\n                              <div className=\"alert alert-info p-3\">\r\n                                No processed videos available yet. Upload and process some videos using the Video Defect Detection tool.\r\n                              </div>\r\n                            )}\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                    </Row>\r\n                  </div>\r\n                </Tab>\r\n\r\n                {/* Defect Map Tab */}\r\n                <Tab eventKey=\"map\" title=\"Defect Map View\">\r\n                  <div className=\"p-4\">\r\n                    <DefectMap user={user} />\r\n                  </div>\r\n                </Tab>\r\n\r\n                {/* Users Overview Tab */}\r\n                <Tab eventKey=\"users\" title=\"Users Overview\">\r\n                  <div className=\"p-3\">\r\n                    <Row className=\"g-3\">\r\n                      <Col md={6}>\r\n                        <Card className=\"shadow-sm dashboard-card\">\r\n                          <Card.Header className=\"bg-primary text-white py-2\">\r\n                            <h6 className=\"mb-0\">User Distribution by Role</h6>\r\n                          </Card.Header>\r\n                          <Card.Body className=\"py-3\">\r\n                            <ChartContainer\r\n                              data={[\r\n                                {\r\n                                  type: 'pie',\r\n                                  labels: Object.keys(dashboardData.users?.by_role || {}),\r\n                                  values: Object.values(dashboardData.users?.by_role || {}),\r\n                                  marker: {\r\n                                    colors: ['#007bff', '#28a745', '#dc3545', '#6c757d']\r\n                                  },\r\n                                  textinfo: \"label+percent\",\r\n                                  insidetextorientation: \"radial\"\r\n                                }\r\n                              ]}\r\n                              isPieChart={true}\r\n                            />\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                      <Col md={6}>\r\n                        <Card className=\"shadow-sm dashboard-card\">\r\n                          <Card.Header className=\"bg-primary text-white py-2\">\r\n                            <h6 className=\"mb-0\">Recent Users</h6>\r\n                          </Card.Header>\r\n                          <Card.Body className=\"py-3\">\r\n                            <div className=\"table-responsive\">\r\n                              <table className=\"table table-sm table-hover\">\r\n                                <thead>\r\n                                  <tr>\r\n                                    <th>Username</th>\r\n                                    <th>Role</th>\r\n                                    <th>Last Login</th>\r\n                                  </tr>\r\n                                </thead>\r\n                                <tbody>\r\n                                  {dashboardData.users?.latest && dashboardData.users.latest.length > 0 ? (\r\n                                    dashboardData.users.latest.map((user, index) => (\r\n                                      <tr key={`user-${index}`}>\r\n                                        <td>{user.username}</td>\r\n                                        <td>\r\n                                          <span className={`badge bg-${\r\n                                            user.role === 'admin' ? 'danger' : \r\n                                            user.role === 'manager' ? 'warning' : \r\n                                            'primary'\r\n                                          }`}>\r\n                                            {user.role}\r\n                                          </span>\r\n                                        </td>\r\n                                        <td>{new Date(user.last_login).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })}</td>\r\n                                      </tr>\r\n                                    ))\r\n                                  ) : (\r\n                                    <tr>\r\n                                      <td colSpan=\"3\" className=\"text-center\">No recent user activity</td>\r\n                                    </tr>\r\n                                  )}\r\n                                </tbody>\r\n                              </table>\r\n                            </div>\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                    </Row>\r\n                  </div>\r\n                </Tab>\r\n              </Tabs>\r\n            </Card.Body>\r\n          </Card>\r\n\r\n          {/* Export buttons */}\r\n          <div className=\"d-flex justify-content-end mb-2\">\r\n            <Button variant=\"outline-danger\" size=\"sm\" className=\"me-2\" onClick={handleDownloadPDF}>\r\n              <i className=\"fas fa-file-pdf me-1\"></i>Download PDF\r\n            </Button>\r\n            <Button variant=\"outline-success\" size=\"sm\" onClick={handleDownloadExcel}>\r\n              <i className=\"fas fa-file-excel me-1\"></i>Download Excel\r\n            </Button>\r\n          </div>\r\n        </>\r\n      )}\r\n    </Container>\r\n  );\r\n}\r\n\r\nexport default Dashboard; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,IAAI,CAAEC,GAAG,CAAEC,IAAI,CAAEC,MAAM,KAAQ,iBAAiB,CACpF,MAAO,CAAAC,IAAI,KAAM,iBAAiB,CAClC,MAAO,CAAAC,cAAc,KAAM,8BAA8B,CACzD,MAAO,CAAAC,SAAS,KAAM,yBAAyB,CAC/C,MAAO,iBAAiB,CACxB,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,SAAS,KAAM,iBAAiB,CACvC,MAAO,GAAK,CAAAC,IAAI,KAAM,MAAM,CAC5B,OAASC,MAAM,KAAQ,YAAY,CAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GATA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAUA,KAAM,CAAAC,qBAAqB,CAAG,QAAAA,CAACC,SAAS,CAA6B,IAA3B,CAAAC,SAAS,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,UAAU,CAC9DG,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAE,CAAEN,SAAS,CAAEC,SAAU,CAAC,CAAC,CAEtE,GAAI,CAACD,SAAS,CAAE,CACdK,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC,CACpC,MAAO,KAAI,CACb,CAEA;AACA,KAAM,CAAAC,YAAY,CAAG,GAAGN,SAAS,iBAAiB,CAClD,GAAID,SAAS,CAACO,YAAY,CAAC,CAAE,CAC3BF,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEC,YAAY,CAAEP,SAAS,CAACO,YAAY,CAAC,CAAC,CAC3E;AACA,KAAM,CAAAC,QAAQ,CAAGR,SAAS,CAACO,YAAY,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CACnD,KAAM,CAAAC,WAAW,CAAGF,QAAQ,CAACG,SAAS,CAACC,IAAI,EAAIA,IAAI,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAC,CACrE,GAAIH,WAAW,GAAK,CAAC,CAAC,EAAIA,WAAW,CAAG,CAAC,CAAGF,QAAQ,CAACL,MAAM,CAAE,CAC3D,KAAM,CAAAW,KAAK,CAAGN,QAAQ,CAACO,KAAK,CAACL,WAAW,CAAG,CAAC,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC,CACvD,KAAM,CAAAC,QAAQ,CAAG,8BAA8BC,kBAAkB,CAACJ,KAAK,CAAC,EAAE,CAC1ET,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAEW,QAAQ,CAAC,CAC3D,MAAO,CAAAA,QAAQ,CACjB,CACF,CAEA;AACA,KAAM,CAAAE,UAAU,CAAG,GAAGlB,SAAS,eAAe,CAC9C,GAAID,SAAS,CAACmB,UAAU,CAAC,CAAE,CACzBd,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAEa,UAAU,CAAEnB,SAAS,CAACmB,UAAU,CAAC,CAAC,CAErE;AACA,KAAM,CAAAL,KAAK,CAAGd,SAAS,CAACmB,UAAU,CAAC,CACnC,KAAM,CAAAC,UAAU,CAAGN,KAAK,CAACL,KAAK,CAAC,GAAG,CAAC,CAACY,GAAG,CAACT,IAAI,EAAIM,kBAAkB,CAACN,IAAI,CAAC,CAAC,CAACI,IAAI,CAAC,GAAG,CAAC,CACnF,KAAM,CAAAM,GAAG,CAAG,8BAA8BF,UAAU,EAAE,CAEtDf,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAEgB,GAAG,CAAC,CACpDjB,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAEQ,KAAK,CAAC,CACtCT,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAEc,UAAU,CAAC,CAE1C,MAAO,CAAAE,GAAG,CACZ,CAEA;AACA,KAAM,CAAAC,aAAa,CAAG,GAAGtB,SAAS,WAAW,CAC7C,GAAID,SAAS,CAACuB,aAAa,CAAC,CAAE,CAC5BlB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAEiB,aAAa,CAAEvB,SAAS,CAACuB,aAAa,CAAC,CAAC,CAC3E,KAAM,CAAAD,GAAG,CAAG,2BAA2BtB,SAAS,CAACuB,aAAa,CAAC,EAAE,CACjElB,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEgB,GAAG,CAAC,CACzC,MAAO,CAAAA,GAAG,CACZ,CAEA;AACAjB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAEL,SAAS,CAAED,SAAS,CAAC,CAChE,MAAO,KAAI,CACb,CAAC,CAED;AACA;AACA;AACA,GACA,KAAM,CAAAwB,oBAAoB,CAAGC,IAAA,EAA2E,IAA1E,CAAEzB,SAAS,CAAEC,SAAS,CAAG,UAAU,CAAEyB,GAAG,CAAEC,SAAS,CAAEC,KAAK,CAAEC,OAAQ,CAAC,CAAAJ,IAAA,CACjG,KAAM,CAACK,eAAe,CAAEC,kBAAkB,CAAC,CAAGxD,QAAQ,CAAC,IAAI,CAAC,CAC5D,KAAM,CAACyD,QAAQ,CAAEC,WAAW,CAAC,CAAG1D,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAC2D,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG5D,QAAQ,CAAC,CAAC,CAAC,CAE3DC,SAAS,CAAC,IAAM,CACd;AACAyD,WAAW,CAAC,KAAK,CAAC,CAClBE,mBAAmB,CAAC,CAAC,CAAC,CAEtB;AACA,KAAM,CAAAC,QAAQ,CAAGrC,qBAAqB,CAACC,SAAS,CAAEC,SAAS,CAAC,CAE5D;AACAI,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAE,CACzCL,SAAS,CACTD,SAAS,CACTqC,YAAY,CAAED,QAAQ,CACtBjB,UAAU,CAAE,GAAGlB,SAAS,eAAe,CACvCqC,UAAU,CAAEtC,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAG,GAAGC,SAAS,eAAe,CAAC,CACpDM,YAAY,CAAE,GAAGN,SAAS,iBAAiB,CAC3CsC,YAAY,CAAEvC,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAG,GAAGC,SAAS,iBAAiB,CACzD,CAAC,CAAC,CAEF8B,kBAAkB,CAACK,QAAQ,CAAC,CAC9B,CAAC,CAAE,CAACpC,SAAS,CAAEC,SAAS,CAAC,CAAC,CAE1B,KAAM,CAAAuC,gBAAgB,CAAIC,KAAK,EAAK,KAAAC,aAAA,CAAAC,cAAA,CAAAC,cAAA,CAAAC,cAAA,CAAAC,cAAA,CAClCzC,OAAO,CAAC0C,KAAK,CAAC,sBAAsB,CAAE,CACpC9C,SAAS,CACT6B,eAAe,CACfI,gBAAgB,CAChBa,KAAK,CAAEN,KAAK,SAALA,KAAK,kBAAAC,aAAA,CAALD,KAAK,CAAEO,MAAM,UAAAN,aAAA,iBAAbA,aAAA,CAAeK,KAAK,CAC3BE,GAAG,CAAER,KAAK,SAALA,KAAK,kBAAAE,cAAA,CAALF,KAAK,CAAEO,MAAM,UAAAL,cAAA,iBAAbA,cAAA,CAAeM,GAAG,CACvBC,YAAY,CAAET,KAAK,SAALA,KAAK,kBAAAG,cAAA,CAALH,KAAK,CAAEO,MAAM,UAAAJ,cAAA,iBAAbA,cAAA,CAAeM,YAAY,CACzCC,aAAa,CAAEV,KAAK,SAALA,KAAK,kBAAAI,cAAA,CAALJ,KAAK,CAAEO,MAAM,UAAAH,cAAA,iBAAbA,cAAA,CAAeM,aAAa,CAC3CC,QAAQ,CAAEX,KAAK,SAALA,KAAK,kBAAAK,cAAA,CAALL,KAAK,CAAEO,MAAM,UAAAF,cAAA,iBAAbA,cAAA,CAAeM,QAC3B,CAAC,CAAC,CAEF;AACA,GAAItB,eAAe,CAAE,CACnBuB,KAAK,CAACvB,eAAe,CAAE,CAAEwB,MAAM,CAAE,MAAO,CAAC,CAAC,CACvCC,IAAI,CAACC,QAAQ,EAAI,CAChBnD,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAE,CAChCgB,GAAG,CAAEQ,eAAe,CACpB2B,MAAM,CAAED,QAAQ,CAACC,MAAM,CACvBC,UAAU,CAAEF,QAAQ,CAACE,UAAU,CAC/BC,OAAO,CAAEC,MAAM,CAACC,WAAW,CAACL,QAAQ,CAACG,OAAO,CAACG,OAAO,CAAC,CAAC,CACxD,CAAC,CAAC,CACJ,CAAC,CAAC,CACDC,KAAK,CAACC,UAAU,EAAI,CACnB3D,OAAO,CAAC0C,KAAK,CAAC,2BAA2B,CAAE,CACzCzB,GAAG,CAAEQ,eAAe,CACpBiB,KAAK,CAAEiB,UAAU,CAACC,OACpB,CAAC,CAAC,CACJ,CAAC,CAAC,CACN,CAEA,GAAI/B,gBAAgB,GAAK,CAAC,CAAE,CAC1B;AACA,KAAM,CAAAgC,WAAW,CAAGC,mBAAmB,CAACnE,SAAS,CAAEC,SAAS,CAAC,CAC7DI,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAE4D,WAAW,CAAC,CAEnD,GAAIA,WAAW,EAAIA,WAAW,GAAKpC,eAAe,CAAE,CAClDC,kBAAkB,CAACmC,WAAW,CAAC,CAC/B/B,mBAAmB,CAAC,CAAC,CAAC,CACtB,OACF,CACF,CAEA;AACA9B,OAAO,CAAC0C,KAAK,CAAC,0CAA0C,CAAE9C,SAAS,CAAC,CACpEgC,WAAW,CAAC,IAAI,CAAC,CACjB,GAAIJ,OAAO,CAAEA,OAAO,CAAC,CAAC,CACxB,CAAC,CAED,KAAM,CAAAsC,mBAAmB,CAAGA,CAACnE,SAAS,CAAEC,SAAS,GAAK,CACpDI,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAEL,SAAS,CAAED,SAAS,CAAC,CAEjE;AACA,KAAM,CAAAO,YAAY,CAAG,GAAGN,SAAS,iBAAiB,CAClD,GAAID,SAAS,CAACO,YAAY,CAAC,CAAE,CAC3BF,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAEN,SAAS,CAACO,YAAY,CAAC,CAAC,CAChE,MAAO,CAAAP,SAAS,CAACO,YAAY,CAAC,CAChC,CAEA;AACA,KAAM,CAAAgB,aAAa,CAAG,GAAGtB,SAAS,WAAW,CAC7C,GAAID,SAAS,CAACuB,aAAa,CAAC,CAAE,CAC5BlB,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEN,SAAS,CAACuB,aAAa,CAAC,CAAC,CAC9D,MAAO,2BAA2BvB,SAAS,CAACuB,aAAa,CAAC,EAAE,CAC9D,CAEA;AACA,KAAM,CAAAJ,UAAU,CAAG,GAAGlB,SAAS,eAAe,CAC9C,GAAID,SAAS,CAACmB,UAAU,CAAC,CAAE,CACzBd,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC,CACtD,KAAM,CAAAQ,KAAK,CAAGd,SAAS,CAACmB,UAAU,CAAC,CACnC,KAAM,CAAAiD,cAAc,CAAG,8BAA8BlD,kBAAkB,CAACJ,KAAK,CAAC,EAAE,CAChFT,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAE8D,cAAc,CAAC,CACxD,MAAO,CAAAA,cAAc,CACvB,CAEA/D,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC,CAC1C,MAAO,KAAI,CACb,CAAC,CAED,GAAI0B,QAAQ,EAAI,CAACF,eAAe,CAAE,CAChC,mBACEpC,IAAA,QAAKiC,SAAS,CAAE,+DAA+DA,SAAS,EAAG,CAACC,KAAK,CAAEA,KAAM,CAAAyC,QAAA,cACvGzE,KAAA,QAAK+B,SAAS,CAAC,aAAa,CAAA0C,QAAA,eAC1B3E,IAAA,MAAGiC,SAAS,CAAC,+BAA+B,CAAI,CAAC,cACjDjC,IAAA,QAAA2E,QAAA,CAAK,oBAAkB,CAAK,CAAC,EAC1B,CAAC,CACH,CAAC,CAEV,CAEA,mBACE3E,IAAA,QAAKiC,SAAS,CAAC,mBAAmB,CAAA0C,QAAA,cAChC3E,IAAA,QACEuD,GAAG,CAAEnB,eAAgB,CACrBJ,GAAG,CAAEA,GAAI,CACTC,SAAS,CAAEA,SAAU,CACrBC,KAAK,CAAEA,KAAM,CACbC,OAAO,CAAEW,gBAAiB,CAC1B8B,OAAO,CAAC,MAAM,CACdC,MAAM,CAAEA,CAAA,GAAM,CACZlE,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAEwB,eAAe,CAAC,CAC5DG,WAAW,CAAC,KAAK,CAAC,CACpB,CAAE,CACH,CAAC,CACC,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAuC,SAAS,CAAGC,KAAA,EAAyC,IAAxC,CAAEC,MAAM,CAAEC,UAAU,CAAEC,WAAY,CAAC,CAAAH,KAAA,CACpD,KAAM,CAACI,UAAU,CAAEC,aAAa,CAAC,CAAGvG,QAAQ,CAAC,KAAK,CAAC,CAEnD;AACA,GAAI,CAACmG,MAAM,CAAE,CACX,MAAO,KAAI,CACb,CAEA,KAAM,CAAAK,UAAU,CAAIC,YAAY,EAAK,CACnCF,aAAa,CAACE,YAAY,CAAC,CAC7B,CAAC,CAED;AACA,KAAM,CAAAC,aAAa,CAAGP,MAAM,CAACQ,gBAAgB,EAAIR,MAAM,CAACQ,gBAAgB,CAAC/E,MAAM,CAAG,CAAC,CACnF,KAAM,CAAAgF,eAAe,CAAGT,MAAM,CAACQ,gBAAgB,EAAI,EAAE,CAErD,mBACExF,IAAA,CAACd,GAAG,EAACwG,EAAE,CAAE,CAAE,CAACzD,SAAS,CAAC,MAAM,CAAA0C,QAAA,cAC1BzE,KAAA,CAACf,IAAI,EAAC8C,SAAS,CAAE,mBAAmBsD,aAAa,CAAG,gBAAgB,CAAG,EAAE,EAAG,CAAAZ,QAAA,eAC1EzE,KAAA,CAACf,IAAI,CAACwG,MAAM,EAAC1D,SAAS,CAAEsD,aAAa,CAAG,0BAA0B,CAAG,EAAG,CAAAZ,QAAA,eACtEzE,KAAA,QAAK+B,SAAS,CAAC,mDAAmD,CAAA0C,QAAA,eAChE3E,IAAA,OAAIiC,SAAS,CAAC,MAAM,CAAA0C,QAAA,CACjBM,UAAU,GAAK,QAAQ,CAAG,GAAGD,MAAM,CAACY,UAAU,EAAI,OAAO,KAAKZ,MAAM,CAACa,QAAQ,EAAI,KAAK,EAAE,CACxFZ,UAAU,GAAK,OAAO,CAAG,GAAGD,MAAM,CAACc,SAAS,EAAI,MAAM,KAAKd,MAAM,CAACe,OAAO,EAAI,KAAK,EAAE,CACpF,YAAYf,MAAM,CAACgB,UAAU,EAAI,KAAK,EAAE,CACvC,CAAC,CACJT,aAAa,eACZvF,IAAA,UAAOiC,SAAS,CAAC,sBAAsB,CAAA0C,QAAA,CAAC,2BAExC,CAAO,CACR,EACE,CAAC,CACLY,aAAa,eACZvF,IAAA,QAAKiC,SAAS,CAAC,MAAM,CAAA0C,QAAA,cACnBzE,KAAA,UAAO+B,SAAS,CAAC,YAAY,CAAA0C,QAAA,EAAC,iBACb,CAACc,eAAe,CAACQ,MAAM,CAACC,CAAC,EAAIA,CAAC,GAAKjB,UAAU,CAAC,CAAC3D,IAAI,CAAC,IAAI,CAAC,EACnE,CAAC,CACL,CACN,EACU,CAAC,cACdpB,KAAA,CAACf,IAAI,CAACgH,IAAI,EAAAxB,QAAA,eACR3E,IAAA,QAAKiC,SAAS,CAAC,kBAAkB,CAAA0C,QAAA,cAC/B3E,IAAA,CAAC8B,oBAAoB,EACnBxB,SAAS,CAAE0E,MAAO,CAClBzE,SAAS,CAAE4E,UAAU,CAAG,UAAU,CAAG,WAAY,CACjDnD,GAAG,CAAE,GAAGiD,UAAU,GAAK,QAAQ,CAAG,OAAO,CAAGA,UAAU,GAAK,OAAO,CAAG,MAAM,CAAG,SAAS,IAAID,MAAM,CAACE,WAAW,CAAC,EAAG,CACjHjD,SAAS,CAAC,uBAAuB,CACjCC,KAAK,CAAE,CAAEkE,SAAS,CAAE,OAAQ,CAAE,CAC9BjE,OAAO,CAAEA,CAAA,GAAM,CACbxB,OAAO,CAAC0F,IAAI,CAAC,kBAAkBlB,UAAU,CAAG,UAAU,CAAG,WAAW,cAAcF,UAAU,IAAID,MAAM,CAACE,WAAW,CAAC,EAAE,CAAC,CACxH,CAAE,CACH,CAAC,CACC,CAAC,cACNhF,KAAA,QAAK+B,SAAS,CAAC,OAAO,CAAA0C,QAAA,EACnBM,UAAU,GAAK,UAAU,eACxB/E,KAAA,CAAAE,SAAA,EAAAuE,QAAA,eACEzE,KAAA,MAAG+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,eAAC3E,IAAA,WAAA2E,QAAA,CAAQ,OAAK,CAAQ,CAAC,IAAC,CAACK,MAAM,CAACsB,QAAQ,CAAGtB,MAAM,CAACsB,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAAC,SAAI,EAAG,CAAC,cACzGrG,KAAA,MAAG+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,eAAC3E,IAAA,WAAA2E,QAAA,CAAQ,QAAM,CAAQ,CAAC,IAAC,CAACK,MAAM,CAACwB,QAAQ,CAAGxB,MAAM,CAACwB,QAAQ,CAACD,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAAC,KAAG,EAAG,CAAC,cACzGrG,KAAA,MAAG+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,eAAC3E,IAAA,WAAA2E,QAAA,CAAQ,SAAO,CAAQ,CAAC,IAAC,CAACK,MAAM,CAACyB,MAAM,CAAGzB,MAAM,CAACyB,MAAM,CAACF,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,EAAI,CAAC,EACnG,CACH,CACAtB,UAAU,GAAK,QAAQ,eACtB/E,KAAA,CAAAE,SAAA,EAAAuE,QAAA,eACEzE,KAAA,MAAG+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,eAAC3E,IAAA,WAAA2E,QAAA,CAAQ,OAAK,CAAQ,CAAC,IAAC,CAACK,MAAM,CAACY,UAAU,EAAI,KAAK,EAAI,CAAC,cAC3E1F,KAAA,MAAG+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,eAAC3E,IAAA,WAAA2E,QAAA,CAAQ,OAAK,CAAQ,CAAC,IAAC,CAACK,MAAM,CAACsB,QAAQ,CAAGtB,MAAM,CAACsB,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAAC,SAAI,EAAG,CAAC,cACzGrG,KAAA,MAAG+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,eAAC3E,IAAA,WAAA2E,QAAA,CAAQ,QAAM,CAAQ,CAAC,IAAC,CAACK,MAAM,CAAC0B,UAAU,EAAI,KAAK,EAAI,CAAC,EAC5E,CACH,CACAzB,UAAU,GAAK,OAAO,eACrB/E,KAAA,CAAAE,SAAA,EAAAuE,QAAA,eACEzE,KAAA,MAAG+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,eAAC3E,IAAA,WAAA2E,QAAA,CAAQ,OAAK,CAAQ,CAAC,IAAC,CAACK,MAAM,CAAC2B,SAAS,EAAI,KAAK,EAAI,CAAC,cAC1EzG,KAAA,MAAG+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,eAAC3E,IAAA,WAAA2E,QAAA,CAAQ,SAAO,CAAQ,CAAC,IAAC,CAACK,MAAM,CAAC4B,QAAQ,CAAG5B,MAAM,CAAC4B,QAAQ,CAACL,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAAC,IAAE,EAAG,CAAC,cACzGrG,KAAA,MAAG+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,eAAC3E,IAAA,WAAA2E,QAAA,CAAQ,YAAU,CAAQ,CAAC,IAAC,CAACK,MAAM,CAACc,SAAS,EAAI,KAAK,EAAI,CAAC,EAC/E,CACH,cACD5F,KAAA,MAAG+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,eAAC3E,IAAA,WAAA2E,QAAA,CAAQ,cAAY,CAAQ,CAAC,IAAC,CAACK,MAAM,CAAC6B,QAAQ,EAAI,SAAS,EAAI,CAAC,cACpF3G,KAAA,MAAG+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,eAAC3E,IAAA,WAAA2E,QAAA,CAAQ,YAAU,CAAQ,CAAC,IAAC,CAACK,MAAM,CAAC8B,SAAS,CAAG,GAAI,CAAAC,IAAI,CAAC/B,MAAM,CAAC8B,SAAS,CAAC,CAACE,cAAc,CAAC,OAAO,CAAE,CAAEC,QAAQ,CAAE,cAAe,CAAC,CAAC,CAAG,KAAK,EAAI,CAAC,cACjK/G,KAAA,QAAK+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,eACnB3E,IAAA,CAACT,MAAM,EACL2H,OAAO,CAAE/B,UAAU,CAAG,SAAS,CAAG,iBAAkB,CACpDgC,IAAI,CAAC,IAAI,CACTlF,SAAS,CAAC,MAAM,CAChBmF,OAAO,CAAEA,CAAA,GAAM/B,UAAU,CAAC,IAAI,CAAE,CAAAV,QAAA,CACjC,UAED,CAAQ,CAAC,cACT3E,IAAA,CAACT,MAAM,EACL2H,OAAO,CAAE,CAAC/B,UAAU,CAAG,SAAS,CAAG,iBAAkB,CACrDgC,IAAI,CAAC,IAAI,CACTC,OAAO,CAAEA,CAAA,GAAM/B,UAAU,CAAC,KAAK,CAAE,CAAAV,QAAA,CAClC,WAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,EACG,CAAC,EACR,CAAC,EA/EyB,GAAGM,UAAU,IAAID,MAAM,CAACE,WAAW,CAAC,EAAIF,MAAM,CAACqC,QAAQ,EAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,EAgFrG,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAC,SAAS,CAAGC,KAAA,EAAe,IAAd,CAAEC,KAAM,CAAC,CAAAD,KAAA,CAC1B;AACA,KAAM,CAACE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG/I,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC5D,KAAM,CAACgJ,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGjJ,QAAQ,CAAC,CAAC,CAAC,CAAC,CAE5D;AACA,GAAI,CAAC6I,KAAK,CAAE,CACV,MAAO,KAAI,CACb,CAEA,KAAM,CAAAK,cAAc,CAAG,KAAO,CAAAC,SAAS,EAAK,CAC1C,GAAI,CACF;AACAJ,mBAAmB,CAACK,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,SAAS,EAAG,CAAE,CAAC,CAAC,CAAC,CAC1DF,mBAAmB,CAACG,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,SAAS,EAAG,KAAM,CAAC,CAAC,CAAC,CAE9D;AACA,KAAM,CAAAE,OAAO,CAAGR,KAAK,CAACS,GAAG,EAAIT,KAAK,CAACU,QAAQ,CAC3C,KAAM,CAAAC,WAAW,CAAG,8BAA8BH,OAAO,IAAIF,SAAS,EAAE,CAExErH,OAAO,CAACC,GAAG,CAAC,eAAeoH,SAAS,2BAA2BE,OAAO,EAAE,CAAC,CAEzE;AACA,KAAM,CAAApE,QAAQ,CAAG,KAAM,CAAAH,KAAK,CAAC0E,WAAW,CAAE,CACxCzE,MAAM,CAAE,KAAK,CACbK,OAAO,CAAE,CACP,QAAQ,CAAE,yBACZ,CACF,CAAC,CAAC,CAEF,GAAI,CAACH,QAAQ,CAACwE,EAAE,CAAE,CAChB,KAAM,CAAAC,SAAS,CAAG,KAAM,CAAAzE,QAAQ,CAAC0E,IAAI,CAAC,CAAC,CAACnE,KAAK,CAAC,KAAO,CAAEE,OAAO,CAAE,eAAgB,CAAC,CAAC,CAAC,CACnF,KAAM,IAAI,CAAAkE,KAAK,CAACF,SAAS,CAAChE,OAAO,EAAI,QAAQT,QAAQ,CAACC,MAAM,KAAKD,QAAQ,CAACE,UAAU,EAAE,CAAC,CACzF,CAEA;AACA,KAAM,CAAA0E,aAAa,CAAG5E,QAAQ,CAACG,OAAO,CAAC0E,GAAG,CAAC,gBAAgB,CAAC,CAC5D,KAAM,CAAAC,KAAK,CAAGC,QAAQ,CAACH,aAAa,CAAE,EAAE,CAAC,CACzC,GAAI,CAAAI,MAAM,CAAG,CAAC,CAEd;AACA,KAAM,CAAAC,MAAM,CAAGjF,QAAQ,CAACkF,IAAI,CAACC,SAAS,CAAC,CAAC,CACxC,KAAM,CAAAC,MAAM,CAAG,EAAE,CAEjB,MAAO,IAAI,CAAE,CACX,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,MAAM,CAACM,IAAI,CAAC,CAAC,CAE3C,GAAIF,IAAI,CAAE,MAEVD,MAAM,CAACI,IAAI,CAACF,KAAK,CAAC,CAClBN,MAAM,EAAIM,KAAK,CAAC3I,MAAM,CAEtB;AACA,GAAImI,KAAK,CAAE,CACT,KAAM,CAAAW,QAAQ,CAAGjC,IAAI,CAACkC,KAAK,CAAEV,MAAM,CAAGF,KAAK,CAAI,GAAG,CAAC,CACnDhB,mBAAmB,CAACK,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,SAAS,EAAGuB,QAAS,CAAC,CAAC,CAAC,CACnE,CACF,CAEA;AACA,KAAM,CAAAE,SAAS,CAAG,GAAI,CAAAC,IAAI,CAACR,MAAM,CAAC,CAClCvI,OAAO,CAACC,GAAG,CAAC,gBAAgBoH,SAAS,uBAAuByB,SAAS,CAACtC,IAAI,QAAQ,CAAC,CAEnF;AACA,GAAI,CAAAwC,SAAS,CAAGF,SAAS,CACzB,GAAIA,SAAS,CAACG,IAAI,GAAK,WAAW,CAAE,CAClCD,SAAS,CAAG,GAAI,CAAAD,IAAI,CAAC,CAACD,SAAS,CAAC,CAAE,CAAEG,IAAI,CAAE,WAAY,CAAC,CAAC,CACxDjJ,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC,CACtD,CAEA;AACA,KAAM,CAAAiJ,OAAO,CAAGC,GAAG,CAACC,eAAe,CAACJ,SAAS,CAAC,CAE9C;AACA,GAAI,CAAAK,QAAQ,CACZ,GAAIhC,SAAS,GAAK,UAAU,EAAIN,KAAK,CAACuC,kBAAkB,CAAE,CACxDD,QAAQ,CAAGtC,KAAK,CAACuC,kBAAkB,CAAClJ,KAAK,CAAC,GAAG,CAAC,CAACmJ,GAAG,CAAC,CAAC,CACtD,CAAC,IAAM,IAAIlC,SAAS,GAAK,WAAW,EAAIN,KAAK,CAACyC,mBAAmB,CAAE,CACjEH,QAAQ,CAAGtC,KAAK,CAACyC,mBAAmB,CAACpJ,KAAK,CAAC,GAAG,CAAC,CAACmJ,GAAG,CAAC,CAAC,CACvD,CAAC,IAAM,CACLF,QAAQ,CAAG,GAAGhC,SAAS,UAAU,CAACN,KAAK,CAACU,QAAQ,EAAIF,OAAO,EAAEkC,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,MAAM,CACpF,CAEAzJ,OAAO,CAACC,GAAG,CAAC,yBAAyBoJ,QAAQ,EAAE,CAAC,CAChDrJ,OAAO,CAACC,GAAG,CAAC,wBAAwBiJ,OAAO,CAACO,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,KAAK,CAAC,CAElE;AACA,KAAM,CAAAC,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAGX,OAAO,CACnBQ,IAAI,CAACI,QAAQ,CAAGT,QAAQ,CACxBK,IAAI,CAACnI,KAAK,CAACwI,OAAO,CAAG,MAAM,CAC3BJ,QAAQ,CAACtB,IAAI,CAAC2B,WAAW,CAACN,IAAI,CAAC,CAE/B1J,OAAO,CAACC,GAAG,CAAC,qCAAqCoJ,QAAQ,EAAE,CAAC,CAE5D;AACA,GAAI,CACFK,IAAI,CAACO,KAAK,CAAC,CAAC,CACZjK,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC,CAEhD;AACAkH,mBAAmB,CAACG,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,SAAS,EAAG,IAAK,CAAC,CAAC,CAAC,CAC7DJ,mBAAmB,CAACK,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,SAAS,EAAG,GAAI,CAAC,CAAC,CAAC,CAE5D;AACA6C,UAAU,CAAC,IAAM,CACfC,KAAK,CAAC,KAAK9C,SAAS,CAAC+C,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGhD,SAAS,CAAC3G,KAAK,CAAC,CAAC,CAAC,8BAA8B2I,QAAQ,EAAE,CAAC,CAC1G;AACAa,UAAU,CAAC,IAAM,CACfjD,mBAAmB,CAACK,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,SAAS,EAAG,CAAE,CAAC,CAAC,CAAC,CAC1DF,mBAAmB,CAACG,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,SAAS,EAAG,KAAM,CAAC,CAAC,CAAC,CAChE,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAAE,GAAG,CAAC,CAET,CAAE,MAAOiD,UAAU,CAAE,CACnBtK,OAAO,CAAC0F,IAAI,CAAC,6CAA6C,CAAE4E,UAAU,CAAC,CAEvE;AACA,GAAI,CACF,KAAM,CAAAC,SAAS,CAAGC,MAAM,CAACC,IAAI,CAACvB,OAAO,CAAE,QAAQ,CAAC,CAChD,GAAIqB,SAAS,CAAE,CACbA,SAAS,CAACZ,QAAQ,CAACe,KAAK,CAAGrB,QAAQ,CACnCrJ,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC,CAEjD;AACAkH,mBAAmB,CAACG,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,SAAS,EAAG,IAAK,CAAC,CAAC,CAAC,CAC7DJ,mBAAmB,CAACK,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,SAAS,EAAG,GAAI,CAAC,CAAC,CAAC,CAE5D6C,UAAU,CAAC,IAAM,CACfC,KAAK,CAAC,KAAK9C,SAAS,CAAC+C,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGhD,SAAS,CAAC3G,KAAK,CAAC,CAAC,CAAC,8BAA8B2I,QAAQ,EAAE,CAAC,CAC1G;AACAa,UAAU,CAAC,IAAM,CACfjD,mBAAmB,CAACK,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,SAAS,EAAG,CAAE,CAAC,CAAC,CAAC,CAC1DF,mBAAmB,CAACG,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,SAAS,EAAG,KAAM,CAAC,CAAC,CAAC,CAChE,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,IAAM,CACL,KAAM,IAAI,CAAAS,KAAK,CAAC,eAAe,CAAC,CAClC,CACF,CAAE,MAAO6C,WAAW,CAAE,CACpB3K,OAAO,CAAC0F,IAAI,CAAC,kDAAkD,CAAEiF,WAAW,CAAC,CAC7E;AACAH,MAAM,CAACI,QAAQ,CAACf,IAAI,CAAGX,OAAO,CAE9B;AACA/B,mBAAmB,CAACG,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,SAAS,EAAG,IAAK,CAAC,CAAC,CAAC,CAC7DJ,mBAAmB,CAACK,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,SAAS,EAAG,GAAI,CAAC,CAAC,CAAC,CAE5D6C,UAAU,CAAC,IAAM,CACfC,KAAK,CAAC,KAAK9C,SAAS,CAAC+C,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGhD,SAAS,CAAC3G,KAAK,CAAC,CAAC,CAAC,8BAA8B2I,QAAQ,EAAE,CAAC,CAC1G;AACAa,UAAU,CAAC,IAAM,CACfjD,mBAAmB,CAACK,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,SAAS,EAAG,CAAE,CAAC,CAAC,CAAC,CAC1DF,mBAAmB,CAACG,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,SAAS,EAAG,KAAM,CAAC,CAAC,CAAC,CAChE,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAAE,GAAG,CAAC,CACT,CACF,CAEAsC,QAAQ,CAACtB,IAAI,CAACwC,WAAW,CAACnB,IAAI,CAAC,CAE/B;AACAQ,UAAU,CAAC,IAAM,CACff,GAAG,CAAC2B,eAAe,CAAC5B,OAAO,CAAC,CAC5BlJ,OAAO,CAACC,GAAG,CAAC,8BAA8BoH,SAAS,QAAQ,CAAC,CAC9D,CAAC,CAAE,IAAI,CAAC,CAAE;AAEZ,CAAE,MAAO3E,KAAK,CAAE,CACd1C,OAAO,CAAC0C,KAAK,CAAC,uBAAuB2E,SAAS,SAAS,CAAE3E,KAAK,CAAC,CAC/DuE,mBAAmB,CAACK,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,SAAS,EAAG,CAAE,CAAC,CAAC,CAAC,CAC1DF,mBAAmB,CAACG,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,SAAS,EAAG,KAAM,CAAC,CAAC,CAAC,CAC9D8C,KAAK,CAAC,qBAAqB9C,SAAS,WAAW3E,KAAK,CAACkB,OAAO,EAAE,CAAC,CACjE,CACF,CAAC,CAED,KAAM,CAAAmH,YAAY,CAAG,KAAO,CAAAC,MAAM,EAAK,CACrC,GAAI,CACF,KAAM,CAAAC,YAAY,CAAGD,MAAM,CAACE,WAAW,CAAC,CAAC,CACzC,KAAM,CAAA3D,OAAO,CAAGR,KAAK,CAACS,GAAG,EAAIT,KAAK,CAACU,QAAQ,CAE3C;AACA,KAAM,CAAAtE,QAAQ,CAAG,KAAM,CAAAH,KAAK,CAAC,iDAAiDiI,YAAY,aAAa1D,OAAO,EAAE,CAAE,CAChHtE,MAAM,CAAE,KAAK,CACbK,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF,GAAI,CAACH,QAAQ,CAACwE,EAAE,CAAE,CAChB,KAAM,IAAI,CAAAG,KAAK,CAAC,kBAAkB3E,QAAQ,CAACE,UAAU,EAAE,CAAC,CAC1D,CAEA,KAAM,CAAA8H,IAAI,CAAG,KAAM,CAAAhI,QAAQ,CAAC0E,IAAI,CAAC,CAAC,CAElC,GAAIoD,YAAY,GAAK,KAAK,CAAE,CAC1B;AACA,GAAIE,IAAI,CAACC,QAAQ,CAAE,CACjB,KAAM,CAAAC,cAAc,CAAGC,IAAI,CAACH,IAAI,CAACC,QAAQ,CAAC,CAC1C,KAAM,CAAAG,WAAW,CAAG,GAAI,CAAAC,KAAK,CAACH,cAAc,CAACvL,MAAM,CAAC,CACpD,IAAK,GAAI,CAAA2L,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGJ,cAAc,CAACvL,MAAM,CAAE2L,CAAC,EAAE,CAAE,CAC9CF,WAAW,CAACE,CAAC,CAAC,CAAGJ,cAAc,CAACK,UAAU,CAACD,CAAC,CAAC,CAC/C,CACA,KAAM,CAAAE,SAAS,CAAG,GAAI,CAAAC,UAAU,CAACL,WAAW,CAAC,CAC7C,KAAM,CAAAM,IAAI,CAAG,GAAI,CAAA9C,IAAI,CAAC,CAAC4C,SAAS,CAAC,CAAE,CAAE1C,IAAI,CAAE,iBAAkB,CAAC,CAAC,CAE/D,KAAM,CAAAS,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxC,KAAM,CAAA3I,GAAG,CAAGkI,GAAG,CAACC,eAAe,CAACyC,IAAI,CAAC,CACrCnC,IAAI,CAACoC,YAAY,CAAC,MAAM,CAAE7K,GAAG,CAAC,CAC9ByI,IAAI,CAACoC,YAAY,CAAC,UAAU,CAAE,SAAS,CAAC/E,KAAK,CAACU,QAAQ,EAAIF,OAAO,EAAEkC,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,sBAAsB,CAAC,CACzGC,IAAI,CAACnI,KAAK,CAACwK,UAAU,CAAG,QAAQ,CAChCpC,QAAQ,CAACtB,IAAI,CAAC2B,WAAW,CAACN,IAAI,CAAC,CAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC,CACZN,QAAQ,CAACtB,IAAI,CAACwC,WAAW,CAACnB,IAAI,CAAC,CAC/BP,GAAG,CAAC2B,eAAe,CAAC7J,GAAG,CAAC,CAC1B,CACF,CAAC,IAAM,IAAIgK,YAAY,GAAK,KAAK,CAAE,CACjC;AACA,GAAIE,IAAI,CAACa,QAAQ,CAAE,CACjB,KAAM,CAAAC,UAAU,CAAGd,IAAI,CAACa,QAAQ,CAAChL,GAAG,CAACkL,GAAG,EAAIA,GAAG,CAACvL,IAAI,CAAC,GAAG,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC,CACrE,KAAM,CAAAkL,IAAI,CAAG,GAAI,CAAA9C,IAAI,CAAC,CAACkD,UAAU,CAAC,CAAE,CAAEhD,IAAI,CAAE,yBAA0B,CAAC,CAAC,CAExE,KAAM,CAAAS,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxC,KAAM,CAAA3I,GAAG,CAAGkI,GAAG,CAACC,eAAe,CAACyC,IAAI,CAAC,CACrCnC,IAAI,CAACoC,YAAY,CAAC,MAAM,CAAE7K,GAAG,CAAC,CAC9ByI,IAAI,CAACoC,YAAY,CAAC,UAAU,CAAE,SAAS,CAAC/E,KAAK,CAACU,QAAQ,EAAIF,OAAO,EAAEkC,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,sBAAsB,CAAC,CACzGC,IAAI,CAACnI,KAAK,CAACwK,UAAU,CAAG,QAAQ,CAChCpC,QAAQ,CAACtB,IAAI,CAAC2B,WAAW,CAACN,IAAI,CAAC,CAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC,CACZN,QAAQ,CAACtB,IAAI,CAACwC,WAAW,CAACnB,IAAI,CAAC,CAC/BP,GAAG,CAAC2B,eAAe,CAAC7J,GAAG,CAAC,CAC1B,CACF,CACF,CAAE,MAAOyB,KAAK,CAAE,CACd1C,OAAO,CAAC0C,KAAK,CAAC,sBAAsBsI,MAAM,GAAG,CAAEtI,KAAK,CAAC,CACrDyH,KAAK,CAAC,sBAAsBa,MAAM,KAAKtI,KAAK,CAACkB,OAAO,EAAE,CAAC,CACzD,CACF,CAAC,CAED,KAAM,CAAAuI,eAAe,CAAGpF,KAAK,CAACqF,gBAAgB,EAAI,CAAC,CAAC,CACpD,KAAM,CAAAC,eAAe,CAAGF,eAAe,CAAClE,KAAK,EAAI,CAAC,CAElD,mBACE5I,IAAA,CAACd,GAAG,EAACwG,EAAE,CAAE,CAAE,CAACzD,SAAS,CAAC,MAAM,CAAA0C,QAAA,cAC1BzE,KAAA,CAACf,IAAI,EAAC8C,SAAS,CAAC,iBAAiB,CAAA0C,QAAA,eAC/BzE,KAAA,CAACf,IAAI,CAACwG,MAAM,EAAC1D,SAAS,CAAC,uBAAuB,CAAA0C,QAAA,eAC5CzE,KAAA,QAAK+B,SAAS,CAAC,mDAAmD,CAAA0C,QAAA,eAChEzE,KAAA,OAAI+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,EAAC,SACZ,CAAC+C,KAAK,CAACU,QAAQ,CAACgC,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,KACzC,EAAI,CAAC,cACLpK,IAAA,UAAOiC,SAAS,CAAC,mBAAmB,CAAA0C,QAAA,CAAC,oBAErC,CAAO,CAAC,EACL,CAAC,cACN3E,IAAA,QAAKiC,SAAS,CAAC,MAAM,CAAA0C,QAAA,cACnBzE,KAAA,UAAO+B,SAAS,CAAC,YAAY,CAAA0C,QAAA,EAAC,UACpB,CAAC+C,KAAK,CAACuF,UAAU,CAAGvF,KAAK,CAACuF,UAAU,CAAC3L,IAAI,CAAC,IAAI,CAAC,CAAG,KAAK,EAC1D,CAAC,CACL,CAAC,EACK,CAAC,cACdpB,KAAA,CAACf,IAAI,CAACgH,IAAI,EAAAxB,QAAA,eACR3E,IAAA,QAAKiC,SAAS,CAAC,kBAAkB,CAAA0C,QAAA,CAC9B+C,KAAK,CAACwF,oBAAoB,cACzBlN,IAAA,QACEuD,GAAG,CAAE,0BAA0BmE,KAAK,CAACwF,oBAAoB,EAAG,CAC5DlL,GAAG,CAAC,iBAAiB,CACrBC,SAAS,CAAC,uBAAuB,CACjCC,KAAK,CAAE,CAAEkE,SAAS,CAAE,OAAQ,CAAE,CAC9BjE,OAAO,CAAGgL,CAAC,EAAK,CACdxM,OAAO,CAAC0F,IAAI,CAAC,iDAAiDqB,KAAK,CAACU,QAAQ,EAAE,CAAC,CAC/E+E,CAAC,CAAC7J,MAAM,CAACpB,KAAK,CAACwI,OAAO,CAAG,MAAM,CACjC,CAAE,CACH,CAAC,cAEF1K,IAAA,QAAKiC,SAAS,CAAC,yDAAyD,CAACC,KAAK,CAAE,CAAEkL,MAAM,CAAE,OAAO,CAAEC,eAAe,CAAE,SAAU,CAAE,CAAA1I,QAAA,cAC9H3E,IAAA,SAAMiC,SAAS,CAAC,YAAY,CAAA0C,QAAA,CAAC,wBAAsB,CAAM,CAAC,CACvD,CACN,CACE,CAAC,cACNzE,KAAA,QAAK+B,SAAS,CAAC,OAAO,CAAA0C,QAAA,eACpB3E,IAAA,MAAGiC,SAAS,CAAC,MAAM,CAAA0C,QAAA,cAAC3E,IAAA,WAAA2E,QAAA,CAAQ,aAAW,CAAQ,CAAC,CAAG,CAAC,cACpDzE,KAAA,OAAI+B,SAAS,CAAC,MAAM,CAACC,KAAK,CAAE,CAAEoL,WAAW,CAAE,MAAO,CAAE,CAAA3I,QAAA,eAClDzE,KAAA,OAAAyE,QAAA,EAAI,YAAU,CAACmI,eAAe,CAACS,QAAQ,EAAI,CAAC,EAAK,CAAC,cAClDrN,KAAA,OAAAyE,QAAA,EAAI,UAAQ,CAACmI,eAAe,CAACU,MAAM,EAAI,CAAC,EAAK,CAAC,cAC9CtN,KAAA,OAAAyE,QAAA,EAAI,SAAO,CAACmI,eAAe,CAACW,KAAK,EAAI,CAAC,EAAK,CAAC,EAC1C,CAAC,cACLvN,KAAA,MAAG+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,eAAC3E,IAAA,WAAA2E,QAAA,CAAQ,mBAAiB,CAAQ,CAAC,IAAC,CAACqI,eAAe,EAAI,CAAC,cAC5E9M,KAAA,MAAG+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,eAAC3E,IAAA,WAAA2E,QAAA,CAAQ,cAAY,CAAQ,CAAC,IAAC,CAAC+C,KAAK,CAACb,QAAQ,EAAI,SAAS,EAAI,CAAC,cACnF3G,KAAA,MAAG+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,eAAC3E,IAAA,WAAA2E,QAAA,CAAQ,YAAU,CAAQ,CAAC,IAAC,CAAC+C,KAAK,CAACZ,SAAS,CAAG,GAAI,CAAAC,IAAI,CAACW,KAAK,CAACZ,SAAS,CAAC,CAACE,cAAc,CAAC,OAAO,CAAE,CAAEC,QAAQ,CAAE,cAAe,CAAC,CAAC,CAAG,KAAK,EAAI,CAAC,CAG9JS,KAAK,CAACuC,kBAAkB,eACvB/J,KAAA,MAAG+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,eAAC3E,IAAA,WAAA2E,QAAA,CAAQ,gBAAc,CAAQ,CAAC,IAAC,cAAA3E,IAAA,UAAOiC,SAAS,CAAC,YAAY,CAAA0C,QAAA,CAAE+C,KAAK,CAACuC,kBAAkB,CAAClJ,KAAK,CAAC,GAAG,CAAC,CAACmJ,GAAG,CAAC,CAAC,EAAI,KAAK,CAAQ,CAAC,EAAG,CAClJ,CACAxC,KAAK,CAACyC,mBAAmB,eACxBjK,KAAA,MAAG+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,eAAC3E,IAAA,WAAA2E,QAAA,CAAQ,iBAAe,CAAQ,CAAC,IAAC,cAAA3E,IAAA,UAAOiC,SAAS,CAAC,YAAY,CAAA0C,QAAA,CAAE+C,KAAK,CAACyC,mBAAmB,CAACpJ,KAAK,CAAC,GAAG,CAAC,CAACmJ,GAAG,CAAC,CAAC,EAAI,KAAK,CAAQ,CAAC,EAAG,CACpJ,cAGDhK,KAAA,QAAK+B,SAAS,CAAC,WAAW,CAAA0C,QAAA,eACxBzE,KAAA,QAAK+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,eACnBzE,KAAA,CAACX,MAAM,EACL2H,OAAO,CAAC,SAAS,CACjBC,IAAI,CAAC,IAAI,CACTlF,SAAS,CAAC,WAAW,CACrBmF,OAAO,CAAEA,CAAA,GAAMW,cAAc,CAAC,UAAU,CAAE,CAC1C2F,QAAQ,CAAE,CAAChG,KAAK,CAACuC,kBAAkB,EAAItC,gBAAgB,CAACgG,QAAQ,CAAG,CAAE,CAAAhJ,QAAA,EAEpEgD,gBAAgB,CAACgG,QAAQ,CAAG,CAAC,CAAG,qBAAqBhG,gBAAgB,CAACgG,QAAQ,GAAG,CAAG,mBAAmB,CACvG9F,gBAAgB,CAAC8F,QAAQ,EAAI,IAAI,EAC5B,CAAC,CACRhG,gBAAgB,CAACgG,QAAQ,CAAG,CAAC,EAAIhG,gBAAgB,CAACgG,QAAQ,CAAG,GAAG,eAC/D3N,IAAA,QAAKiC,SAAS,CAAC,eAAe,CAACC,KAAK,CAAE,CAAEkL,MAAM,CAAE,KAAM,CAAE,CAAAzI,QAAA,cACtD3E,IAAA,QACEiC,SAAS,CAAC,yBAAyB,CACnC2L,IAAI,CAAC,aAAa,CAClB1L,KAAK,CAAE,CAAE2L,KAAK,CAAE,GAAGlG,gBAAgB,CAACgG,QAAQ,GAAI,CAAE,CAC9C,CAAC,CACJ,CACN,EACE,CAAC,cAENzN,KAAA,QAAK+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,eACnBzE,KAAA,CAACX,MAAM,EACL2H,OAAO,CAAC,SAAS,CACjBC,IAAI,CAAC,IAAI,CACTlF,SAAS,CAAC,WAAW,CACrBmF,OAAO,CAAEA,CAAA,GAAMW,cAAc,CAAC,WAAW,CAAE,CAC3C2F,QAAQ,CAAE,CAAChG,KAAK,CAACyC,mBAAmB,EAAIxC,gBAAgB,CAACmG,SAAS,CAAG,CAAE,CAAAnJ,QAAA,EAEtEgD,gBAAgB,CAACmG,SAAS,CAAG,CAAC,CAAG,qBAAqBnG,gBAAgB,CAACmG,SAAS,GAAG,CAAG,oBAAoB,CAC1GjG,gBAAgB,CAACiG,SAAS,EAAI,IAAI,EAC7B,CAAC,CACRnG,gBAAgB,CAACmG,SAAS,CAAG,CAAC,EAAInG,gBAAgB,CAACmG,SAAS,CAAG,GAAG,eACjE9N,IAAA,QAAKiC,SAAS,CAAC,eAAe,CAACC,KAAK,CAAE,CAAEkL,MAAM,CAAE,KAAM,CAAE,CAAAzI,QAAA,cACtD3E,IAAA,QACEiC,SAAS,CAAC,yBAAyB,CACnC2L,IAAI,CAAC,aAAa,CAClB1L,KAAK,CAAE,CAAE2L,KAAK,CAAE,GAAGlG,gBAAgB,CAACmG,SAAS,GAAI,CAAE,CAC/C,CAAC,CACJ,CACN,EACE,CAAC,EACH,CAAC,cAGN5N,KAAA,QAAK+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,eACnB3E,IAAA,CAACT,MAAM,EACL2H,OAAO,CAAC,mBAAmB,CAC3BC,IAAI,CAAC,IAAI,CACTlF,SAAS,CAAC,MAAM,CAChBmF,OAAO,CAAEA,CAAA,GAAMsE,YAAY,CAAC,KAAK,CAAE,CAAA/G,QAAA,CACpC,yBAED,CAAQ,CAAC,cACT3E,IAAA,CAACT,MAAM,EACL2H,OAAO,CAAC,mBAAmB,CAC3BC,IAAI,CAAC,IAAI,CACTC,OAAO,CAAEA,CAAA,GAAMsE,YAAY,CAAC,KAAK,CAAE,CAAA/G,QAAA,CACpC,yBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,EACG,CAAC,EACR,CAAC,EA1HyB,SAAS+C,KAAK,CAACU,QAAQ,EA2HpD,CAAC,CAEV,CAAC,CAED,QAAS,CAAA2F,SAASA,CAAAC,KAAA,CAAW,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,IAAV,CAAEC,IAAK,CAAC,CAAAP,KAAA,CACzB,KAAM,CAACQ,UAAU,CAAEC,aAAa,CAAC,CAAG5P,QAAQ,CAAC,CAC3C6P,gBAAgB,CAAE,CAAC,CACnBC,cAAc,CAAE,CAAC,CACjBC,aAAa,CAAE,CAAC,CAChBC,UAAU,CAAE,CACd,CAAC,CAAC,CACF,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGlQ,QAAQ,CAAC,CAC3CmQ,IAAI,CAAE,EAAE,CACRC,MAAM,CAAE,EACV,CAAC,CAAC,CACF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGtQ,QAAQ,CAAC,CAC/CuQ,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EACV,CAAC,CAAC,CACF,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAG1Q,QAAQ,CAAC,CACjD0O,QAAQ,CAAE,CACRiC,KAAK,CAAE,CAAC,CACRC,OAAO,CAAE,CAAC,CAAC,CACXC,UAAU,CAAE,CAAC,CACbC,MAAM,CAAE,EACV,CAAC,CACDnC,MAAM,CAAE,CACNgC,KAAK,CAAE,CAAC,CACRI,OAAO,CAAE,CAAC,CAAC,CACXH,OAAO,CAAE,CAAC,CAAC,CACXE,MAAM,CAAE,EACV,CAAC,CACDlC,KAAK,CAAE,CACL+B,KAAK,CAAE,CAAC,CACRK,YAAY,CAAE,CAAC,CAAC,CAChBF,MAAM,CAAE,EACV,CAAC,CACDG,KAAK,CAAE,CACLN,KAAK,CAAE,CAAC,CACRO,OAAO,CAAE,CAAC,CAAC,CACXJ,MAAM,CAAE,EACV,CAAC,CACDK,MAAM,CAAE,CACNR,KAAK,CAAE,CAAC,CACRG,MAAM,CAAE,EACV,CACF,CAAC,CAAC,CACF,KAAM,CAAC/K,OAAO,CAAEqL,UAAU,CAAC,CAAGpR,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACwE,KAAK,CAAE6M,QAAQ,CAAC,CAAGrR,QAAQ,CAAC,IAAI,CAAC,CAExC;AACA,KAAM,CAACsR,SAAS,CAAEC,YAAY,CAAC,CAAGvR,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACwR,OAAO,CAAEC,UAAU,CAAC,CAAGzR,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC0R,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG3R,QAAQ,CAAC,KAAK,CAAC,CAEjE;AACA,KAAM,CAAC4R,SAAS,CAAEC,YAAY,CAAC,CAAG7R,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC8R,YAAY,CAAEC,eAAe,CAAC,CAAG/R,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACgS,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGjS,QAAQ,CAAC,KAAK,CAAC,CAEjE;AACA,KAAM,CAACkS,aAAa,CAAEC,gBAAgB,CAAC,CAAGnS,QAAQ,CAAC,CACjD0O,QAAQ,CAAE,IAAI,CACdC,MAAM,CAAE,IAAI,CACZC,KAAK,CAAE,IACT,CAAC,CAAC,CAEF;AACA,KAAM,CAACwD,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGrS,QAAQ,CAAC,CAC/DuQ,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EACV,CAAC,CAAC,CAEF;AACA,KAAM,CAAC8B,SAAS,CAAEC,YAAY,CAAC,CAAGvS,QAAQ,CAAC,WAAW,CAAC,CAEvD;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAuS,WAAW,CAAG,GAAI,CAAAtK,IAAI,CAAC,CAAC,CAC9B,KAAM,CAAAuK,QAAQ,CAAG,GAAI,CAAAvK,IAAI,CAAC,CAAC,CAC3BuK,QAAQ,CAACC,OAAO,CAACF,WAAW,CAACG,OAAO,CAAC,CAAC,CAAG,CAAC,CAAC,CAAE;AAE7C,KAAM,CAAAC,gBAAgB,CAAGJ,WAAW,CAACK,WAAW,CAAC,CAAC,CAAC3Q,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAChE,KAAM,CAAA4Q,kBAAkB,CAAGL,QAAQ,CAACI,WAAW,CAAC,CAAC,CAAC3Q,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAE/DuP,UAAU,CAACmB,gBAAgB,CAAC,CAC5BrB,YAAY,CAACuB,kBAAkB,CAAC,CAChCC,SAAS,CAAC,CAAEzB,SAAS,CAAEwB,kBAAkB,CAAEtB,OAAO,CAAEoB,gBAAiB,CAAC,CAAC,CACzE,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAG,SAAS,CAAG,cAAAA,CAAA,CAAwB,IAAjB,CAAAC,OAAO,CAAArR,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CACnC,GAAI,CACFyP,UAAU,CAAC,IAAI,CAAC,CAEhB;AACA,KAAM,CAAA6B,MAAM,CAAG,CAAC,CAAC,CACjB,GAAID,OAAO,CAAC1B,SAAS,CAAE2B,MAAM,CAACC,UAAU,CAAGF,OAAO,CAAC1B,SAAS,CAC5D,GAAI0B,OAAO,CAACxB,OAAO,CAAEyB,MAAM,CAACE,QAAQ,CAAGH,OAAO,CAACxB,OAAO,CACtD,GAAIwB,OAAO,CAAChL,QAAQ,CAAEiL,MAAM,CAACjL,QAAQ,CAAGgL,OAAO,CAAChL,QAAQ,CACxD,GAAI0H,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEX,IAAI,CAAEkE,MAAM,CAACG,SAAS,CAAG1D,IAAI,CAACX,IAAI,CAE5C;AACA,KAAM,CAAAsE,aAAa,CAAG,KAAM,CAAAnT,KAAK,CAAC4J,GAAG,CAAC,2BAA2B,CAAE,CAAEmJ,MAAO,CAAC,CAAC,CAC9E,GAAII,aAAa,CAACpG,IAAI,CAACqG,OAAO,CAAE,CAC9B1D,aAAa,CAAC,CACZC,gBAAgB,CAAEwD,aAAa,CAACpG,IAAI,CAACA,IAAI,CAACsG,cAAc,CAAC7E,QAAQ,CACjEoB,cAAc,CAAEuD,aAAa,CAACpG,IAAI,CAACA,IAAI,CAACsG,cAAc,CAAC5E,MAAM,CAC7DoB,aAAa,CAAEsD,aAAa,CAACpG,IAAI,CAACA,IAAI,CAACsG,cAAc,CAAC3E,KAAK,CAC3DoB,UAAU,CAAEL,UAAU,CAACK,UAAW;AACpC,CAAC,CAAC,CACJ,CAEA;AACA,KAAM,CAAAwD,cAAc,CAAG,KAAM,CAAAtT,KAAK,CAAC4J,GAAG,CAAC,6BAA6B,CAAE,CAAEmJ,MAAO,CAAC,CAAC,CACjF/C,aAAa,CAAC,CACZC,IAAI,CAAEqD,cAAc,CAACvG,IAAI,CAACkD,IAAI,CAC9BC,MAAM,CAAEoD,cAAc,CAACvG,IAAI,CAACmD,MAC9B,CAAC,CAAC,CAEF;AACA,KAAM,CAAAqD,aAAa,CAAG,KAAM,CAAAvT,KAAK,CAAC4J,GAAG,CAAC,+BAA+B,CAAE,CAAEmJ,MAAO,CAAC,CAAC,CAClF3C,eAAe,CAAC,CACdC,KAAK,CAAEkD,aAAa,CAACxG,IAAI,CAACsD,KAAK,CAC/BC,MAAM,CAAEiD,aAAa,CAACxG,IAAI,CAACuD,MAC7B,CAAC,CAAC,CAEF;AACA,GAAI,CAAAkD,iBAAiB,CACrB,GAAI,CACF;AACAA,iBAAiB,CAAG,KAAM,CAAAxT,KAAK,CAAC4J,GAAG,CAAC,2BAA2B,CAAE,CAAEmJ,MAAO,CAAC,CAAC,CAC5EnR,OAAO,CAACC,GAAG,CAAC,0EAA0E,CAAC,CACzF,CAAE,MAAO4R,aAAa,CAAE,CACtB7R,OAAO,CAAC0F,IAAI,CAAC,8DAA8D,CAAEmM,aAAa,CAACjO,OAAO,CAAC,CACnG;AACAgO,iBAAiB,CAAG,KAAM,CAAAxT,KAAK,CAAC4J,GAAG,CAAC,wBAAwB,CAAE,CAAEmJ,MAAO,CAAC,CAAC,CACzEnR,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC,CAC/C,CAEA,GAAI2R,iBAAiB,CAACzG,IAAI,CAACqG,OAAO,CAAE,CAClC,KAAM,CAAA7C,aAAa,CAAGiD,iBAAiB,CAACzG,IAAI,CAACA,IAAI,CAEjD;AACA,KAAM,CAAA2G,gBAAgB,CAAG,CACvBC,WAAW,CAAE,CAAC,CACdC,iBAAiB,CAAE,CAAC,CACpBC,kBAAkB,CAAE,CAAC,CACrBC,iBAAiB,CAAE,CACjBtF,QAAQ,CAAE,CAAC,CACXC,MAAM,CAAE,CAAC,CACTC,KAAK,CAAE,CACT,CACF,CAAC,CAED;AACA,CAAC,UAAU,CAAE,QAAQ,CAAE,OAAO,CAAC,CAACqF,OAAO,CAACC,QAAQ,EAAI,CAClD,KAAM,CAAAC,cAAc,CAAG1D,aAAa,CAACyD,QAAQ,CAAC,CAACpD,MAAM,EAAI,EAAE,CAC3DqD,cAAc,CAACF,OAAO,CAACG,IAAI,EAAI,CAC7B,GAAIA,IAAI,CAACC,kBAAkB,CAAE,CAC3BT,gBAAgB,CAACE,iBAAiB,EAAE,CACtC,CACAF,gBAAgB,CAACC,WAAW,EAAE,CAC9BD,gBAAgB,CAACI,iBAAiB,CAACE,QAAQ,CAAC,EAAE,CAChD,CAAC,CAAC,CACJ,CAAC,CAAC,CAEFN,gBAAgB,CAACG,kBAAkB,CAAGH,gBAAgB,CAACC,WAAW,CAAGD,gBAAgB,CAACE,iBAAiB,CAEvG;AACArD,aAAa,CAACmD,gBAAgB,CAAGA,gBAAgB,CAEjDlD,gBAAgB,CAACD,aAAa,CAAC,CACjC,CAEA;AACA,GAAI,CACF,KAAM,CAAA6D,aAAa,CAAG,KAAM,CAAApU,KAAK,CAAC4J,GAAG,CAAC,oBAAoB,CAAE,CAAEmJ,MAAO,CAAC,CAAC,CACvE,GAAIqB,aAAa,CAACrH,IAAI,CAACqG,OAAO,CAAE,CAC9B1D,aAAa,CAAC2E,SAAS,GAAK,CAC1B,GAAGA,SAAS,CACZvE,UAAU,CAAEsE,aAAa,CAACrH,IAAI,CAACuH,WAAW,EAAI,CAChD,CAAC,CAAC,CAAC,CAEH;AACA9D,gBAAgB,CAAC+D,QAAQ,GAAK,CAC5B,GAAGA,QAAQ,CACXxD,KAAK,CAAE,CACLN,KAAK,CAAE2D,aAAa,CAACrH,IAAI,CAACuH,WAAW,EAAI,CAAC,CAC1CtD,OAAO,CAAEoD,aAAa,CAACrH,IAAI,CAACyH,kBAAkB,EAAI,CAAC,CAAC,CACpD5D,MAAM,CAAEwD,aAAa,CAACrH,IAAI,CAAC0H,YAAY,EAAI,EAC7C,CACF,CAAC,CAAC,CAAC,CACL,CACF,CAAE,MAAOC,OAAO,CAAE,CAChB9S,OAAO,CAAC0C,KAAK,CAAC,2BAA2B,CAAEoQ,OAAO,CAAC,CACnD;AACF,CAEAxD,UAAU,CAAC,KAAK,CAAC,CACnB,CAAE,MAAOyD,GAAG,CAAE,CACZxD,QAAQ,CAAC,+BAA+B,CAAC,CACzCD,UAAU,CAAC,KAAK,CAAC,CACjBtP,OAAO,CAAC0C,KAAK,CAAC,sBAAsB,CAAEqQ,GAAG,CAAC,CAC5C,CACF,CAAC,CAED;AACA5U,SAAS,CAAC,IAAM,CACd,KAAM,CAAA6U,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACF,KAAM,CAAA7B,MAAM,CAAG,CAAC,CAAC,CACjB,GAAIvD,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEX,IAAI,CAAEkE,MAAM,CAACG,SAAS,CAAG1D,IAAI,CAACX,IAAI,CAE5C,KAAM,CAAA9J,QAAQ,CAAG,KAAM,CAAA/E,KAAK,CAAC4J,GAAG,CAAC,gBAAgB,CAAE,CAAEmJ,MAAO,CAAC,CAAC,CAC9D,GAAIhO,QAAQ,CAACgI,IAAI,CAACqG,OAAO,CAAE,CACzB;AACAzB,YAAY,CAAC5M,QAAQ,CAACgI,IAAI,CAACgE,KAAK,CAAC,CACnC,CACF,CAAE,MAAOzM,KAAK,CAAE,CACd1C,OAAO,CAAC0C,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC/C,CACF,CAAC,CAEDsQ,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,CAACpF,IAAI,CAAC,CAAC,CAEV;AACA,KAAM,CAAAqF,qBAAqB,CAAGA,CAAA,GAAM,CAClC,GAAIzD,SAAS,EAAIE,OAAO,CAAE,CACxBuB,SAAS,CAAC,CACRzB,SAAS,CACTE,OAAO,CACPxJ,QAAQ,CAAE8J,YAAY,EAAIjQ,SAC5B,CAAC,CAAC,CACF8P,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CACF,CAAC,CAED;AACA,KAAM,CAAAqD,qBAAqB,CAAGA,CAAA,GAAM,CAClC,KAAM,CAAAxC,WAAW,CAAG,GAAI,CAAAtK,IAAI,CAAC,CAAC,CAC9B,KAAM,CAAAuK,QAAQ,CAAG,GAAI,CAAAvK,IAAI,CAAC,CAAC,CAC3BuK,QAAQ,CAACC,OAAO,CAACF,WAAW,CAACG,OAAO,CAAC,CAAC,CAAG,CAAC,CAAC,CAAE;AAE7C,KAAM,CAAAsC,UAAU,CAAGzC,WAAW,CAACK,WAAW,CAAC,CAAC,CAAC3Q,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAC1D,KAAM,CAAAgT,YAAY,CAAGzC,QAAQ,CAACI,WAAW,CAAC,CAAC,CAAC3Q,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAEzDuP,UAAU,CAACwD,UAAU,CAAC,CACtB1D,YAAY,CAAC2D,YAAY,CAAC,CAC1BnC,SAAS,CAAC,CACRzB,SAAS,CAAE4D,YAAY,CACvB1D,OAAO,CAAEyD,UAAU,CACnBjN,QAAQ,CAAE8J,YAAY,EAAIjQ,SAC5B,CAAC,CAAC,CACF8P,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CAAC,CAED;AACA,KAAM,CAAAwD,qBAAqB,CAAGA,CAAA,GAAM,CAClCpC,SAAS,CAAC,CACRzB,SAAS,CACTE,OAAO,CACPxJ,QAAQ,CAAE8J,YAAY,EAAIjQ,SAC5B,CAAC,CAAC,CACFoQ,oBAAoB,CAAC,CAAC,CAACH,YAAY,CAAC,CACtC,CAAC,CAED;AACA,KAAM,CAAAsD,qBAAqB,CAAGA,CAAA,GAAM,CAClCrD,eAAe,CAAC,EAAE,CAAC,CACnBgB,SAAS,CAAC,CACRzB,SAAS,CACTE,OACF,CAAC,CAAC,CACFS,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CAAC,CAED;AACA,KAAM,CAAAoD,gBAAgB,CAAI/G,CAAC,EAAK,CAC9ByD,eAAe,CAACzD,CAAC,CAAC7J,MAAM,CAAC8F,KAAK,CAAC,CACjC,CAAC,CAED;AACAtK,SAAS,CAAC,IAAM,CACd,GAAIoQ,YAAY,CAACE,KAAK,CAAC3O,MAAM,CAAG,CAAC,CAAE,CACjC,KAAM,CAAA0T,aAAa,CAAG,EAAE,CACxB,KAAM,CAAAC,cAAc,CAAG,EAAE,CAEzBlF,YAAY,CAACE,KAAK,CAAC0D,OAAO,CAAC,CAAClJ,IAAI,CAAEyK,KAAK,GAAK,CAC1C,GACGzK,IAAI,CAACzI,QAAQ,CAAC,SAAS,CAAC,EAAI4P,aAAa,CAACxD,QAAQ,EAClD3D,IAAI,CAACzI,QAAQ,CAAC,OAAO,CAAC,EAAI4P,aAAa,CAACvD,MAAO,EAC/C5D,IAAI,CAACzI,QAAQ,CAAC,MAAM,CAAC,EAAI4P,aAAa,CAACtD,KAAM,CAC9C,CACA0G,aAAa,CAAC7K,IAAI,CAACM,IAAI,CAAC,CACxBwK,cAAc,CAAC9K,IAAI,CAAC4F,YAAY,CAACG,MAAM,CAACgF,KAAK,CAAC,CAAC,CACjD,CACF,CAAC,CAAC,CAEFnD,uBAAuB,CAAC,CACtB9B,KAAK,CAAE+E,aAAa,CACpB9E,MAAM,CAAE+E,cACV,CAAC,CAAC,CACJ,CACF,CAAC,CAAE,CAAClF,YAAY,CAAE6B,aAAa,CAAC,CAAC,CAEjC;AACA,KAAM,CAAAuD,wBAAwB,CAAIrP,UAAU,EAAK,CAC/C+L,gBAAgB,CAAC/I,IAAI,GAAK,CACxB,GAAGA,IAAI,CACP,CAAChD,UAAU,EAAG,CAACgD,IAAI,CAAChD,UAAU,CAChC,CAAC,CAAC,CAAC,CACL,CAAC,CAED;AACA,KAAM,CAAAsP,iBAAiB,CAAGA,CAAA,GAAM,KAAAC,oBAAA,CAC9B,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAA9U,KAAK,CAAC,CAAC,CACvB,GAAI,CAAA+U,SAAS,CAAG,EAAE,CAElB;AACAD,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC,CACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCH,GAAG,CAACI,IAAI,CAAC,+CAA+C,CAAE,GAAG,CAAEH,SAAS,CAAE,CAAEI,KAAK,CAAE,QAAS,CAAC,CAAC,CAC9FJ,SAAS,EAAI,EAAE,CAEfD,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC,CACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,CAAE,QAAQ,CAAC,CAClCH,GAAG,CAACI,IAAI,CAAC,iBAAiB,GAAI,CAAA9N,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,OAAO,CAAE,CAAEC,QAAQ,CAAE,cAAe,CAAC,CAAC,EAAE,CAAE,GAAG,CAAEyN,SAAS,CAAE,CAAEI,KAAK,CAAE,QAAS,CAAC,CAAC,CAClIJ,SAAS,EAAI,EAAE,CAEf;AACA,GAAInE,iBAAiB,CAAE,CACrBkE,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC,CACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCH,GAAG,CAACI,IAAI,CAAC,oBAAoB,CAAE,EAAE,CAAEH,SAAS,CAAC,CAC7CA,SAAS,EAAI,CAAC,CACdD,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC,CACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,CAAE,QAAQ,CAAC,CAClCH,GAAG,CAACI,IAAI,CAAC,SAAS,GAAI,CAAA9N,IAAI,CAACoJ,SAAS,CAAC,CAAC4E,kBAAkB,CAAC,CAAC,QAAQ,GAAI,CAAAhO,IAAI,CAACsJ,OAAO,CAAC,CAAC0E,kBAAkB,CAAC,CAAC,EAAE,CAAE,EAAE,CAAEL,SAAS,CAAC,CAC1HA,SAAS,EAAI,EAAE,CACjB,CAEA;AACAD,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC,CACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCH,GAAG,CAACI,IAAI,CAAC,oBAAoB,CAAE,EAAE,CAAEH,SAAS,CAAC,CAC7CA,SAAS,EAAI,EAAE,CAEfD,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC,CACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,CAAE,QAAQ,CAAC,CAClCH,GAAG,CAACI,IAAI,CAAC,4BAA4BrG,UAAU,CAACE,gBAAgB,EAAE,CAAE,EAAE,CAAEgG,SAAS,CAAC,CAClFA,SAAS,EAAI,CAAC,CACdD,GAAG,CAACI,IAAI,CAAC,0BAA0BrG,UAAU,CAACG,cAAc,EAAE,CAAE,EAAE,CAAE+F,SAAS,CAAC,CAC9EA,SAAS,EAAI,CAAC,CACdD,GAAG,CAACI,IAAI,CAAC,yBAAyBrG,UAAU,CAACI,aAAa,EAAE,CAAE,EAAE,CAAE8F,SAAS,CAAC,CAC5EA,SAAS,EAAI,CAAC,CACdD,GAAG,CAACI,IAAI,CAAC,gBAAgBrG,UAAU,CAACK,UAAU,EAAE,CAAE,EAAE,CAAE6F,SAAS,CAAC,CAChEA,SAAS,EAAI,EAAE,CAEf;AACAD,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC,CACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCH,GAAG,CAACI,IAAI,CAAC,6BAA6B,CAAE,EAAE,CAAEH,SAAS,CAAC,CACtDA,SAAS,EAAI,EAAE,CAEf,KAAM,CAAAM,WAAW,CAAGxG,UAAU,CAACE,gBAAgB,CAAGF,UAAU,CAACG,cAAc,CAAGH,UAAU,CAACI,aAAa,CACtG,GAAIoG,WAAW,CAAG,CAAC,CAAE,CACnB,KAAM,CAAAC,cAAc,CAAG,CAAEzG,UAAU,CAACE,gBAAgB,CAAGsG,WAAW,CAAI,GAAG,EAAEzO,OAAO,CAAC,CAAC,CAAC,CACrF,KAAM,CAAA2O,YAAY,CAAG,CAAE1G,UAAU,CAACG,cAAc,CAAGqG,WAAW,CAAI,GAAG,EAAEzO,OAAO,CAAC,CAAC,CAAC,CACjF,KAAM,CAAA4O,WAAW,CAAG,CAAE3G,UAAU,CAACI,aAAa,CAAGoG,WAAW,CAAI,GAAG,EAAEzO,OAAO,CAAC,CAAC,CAAC,CAE/EkO,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC,CACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,CAAE,QAAQ,CAAC,CAClCH,GAAG,CAACI,IAAI,CAAC,aAAarG,UAAU,CAACE,gBAAgB,KAAKuG,cAAc,IAAI,CAAE,EAAE,CAAEP,SAAS,CAAC,CACxFA,SAAS,EAAI,CAAC,CACdD,GAAG,CAACI,IAAI,CAAC,WAAWrG,UAAU,CAACG,cAAc,KAAKuG,YAAY,IAAI,CAAE,EAAE,CAAER,SAAS,CAAC,CAClFA,SAAS,EAAI,CAAC,CACdD,GAAG,CAACI,IAAI,CAAC,UAAUrG,UAAU,CAACI,aAAa,KAAKuG,WAAW,IAAI,CAAE,EAAE,CAAET,SAAS,CAAC,CAC/EA,SAAS,EAAI,EAAE,CACjB,CAEA;AACA,GAAI,CAAAF,oBAAA,CAAAlF,aAAa,CAACQ,KAAK,UAAA0E,oBAAA,WAAnBA,oBAAA,CAAqB7E,MAAM,EAAIL,aAAa,CAACQ,KAAK,CAACH,MAAM,CAAClP,MAAM,CAAG,CAAC,CAAE,CACxEgU,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC,CACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCH,GAAG,CAACI,IAAI,CAAC,cAAc,CAAE,EAAE,CAAEH,SAAS,CAAC,CACvCA,SAAS,EAAI,EAAE,CAEf,KAAM,CAAAU,aAAa,CAAG9F,aAAa,CAACQ,KAAK,CAACH,MAAM,CAAChO,GAAG,CAAC,CAAC4M,IAAI,CAAE8G,GAAG,GAAK,CAClEA,GAAG,CAAG,CAAC,CACP9G,IAAI,CAAC1H,QAAQ,CACb0H,IAAI,CAACX,IAAI,CACT,GAAI,CAAA7G,IAAI,CAACwH,IAAI,CAAC+G,UAAU,CAAC,CAACtO,cAAc,CAAC,OAAO,CAAE,CAAEC,QAAQ,CAAE,cAAe,CAAC,CAAC,CAChF,CAAC,CAEFrH,SAAS,CAAC6U,GAAG,CAAE,CACbc,IAAI,CAAE,CAAC,CAAC,GAAG,CAAE,UAAU,CAAE,MAAM,CAAE,YAAY,CAAC,CAAC,CAC/CvM,IAAI,CAAEoM,aAAa,CACnBI,MAAM,CAAEd,SAAS,CACjBe,MAAM,CAAE,CAAEC,GAAG,CAAE,EAAG,CAAC,CACnBC,MAAM,CAAE,CAAEC,QAAQ,CAAE,EAAG,CAAC,CACxBC,UAAU,CAAE,CAAEC,SAAS,CAAE,CAAC,CAAC,CAAE,GAAG,CAAE,GAAG,CAAE,CACzC,CAAC,CAAC,CAEFpB,SAAS,CAAGD,GAAG,CAACsB,aAAa,CAACC,MAAM,CAAG,EAAE,CAC3C,CAEA;AACA,GAAI1G,aAAa,CAAC/B,QAAQ,CAACoC,MAAM,EAAIL,aAAa,CAAC/B,QAAQ,CAACoC,MAAM,CAAClP,MAAM,CAAG,CAAC,CAAE,CAC7EgU,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC,CACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCH,GAAG,CAACI,IAAI,CAAC,mBAAmB,CAAE,EAAE,CAAEH,SAAS,CAAC,CAC5CA,SAAS,EAAI,EAAE,CAEf,KAAM,CAAAuB,gBAAgB,CAAG3G,aAAa,CAAC/B,QAAQ,CAACoC,MAAM,CAAChO,GAAG,CAAC,CAACqD,MAAM,CAAEqQ,GAAG,GAAK,CAC1EA,GAAG,CAAG,CAAC,CACPrQ,MAAM,CAACsB,QAAQ,CAAGtB,MAAM,CAACsB,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,CAAG,MAAM,CAAG,KAAK,CAC7DvB,MAAM,CAACwB,QAAQ,CAAGxB,MAAM,CAACwB,QAAQ,CAACD,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAAG,KAAK,CAC5DvB,MAAM,CAACyB,MAAM,CAAGzB,MAAM,CAACyB,MAAM,CAACF,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAChDvB,MAAM,CAAC6B,QAAQ,EAAI,SAAS,CAC5B,GAAI,CAAAE,IAAI,CAAC/B,MAAM,CAAC8B,SAAS,CAAC,CAACE,cAAc,CAAC,OAAO,CAAE,CAAEC,QAAQ,CAAE,cAAe,CAAC,CAAC,CACjF,CAAC,CAEFrH,SAAS,CAAC6U,GAAG,CAAE,CACbc,IAAI,CAAE,CAAC,CAAC,GAAG,CAAE,MAAM,CAAE,OAAO,CAAE,QAAQ,CAAE,aAAa,CAAE,WAAW,CAAC,CAAC,CACpEvM,IAAI,CAAEiN,gBAAgB,CACtBT,MAAM,CAAEd,SAAS,CACjBe,MAAM,CAAE,CAAEC,GAAG,CAAE,EAAG,CAAC,CACnBC,MAAM,CAAE,CAAEC,QAAQ,CAAE,CAAE,CAAC,CACvBC,UAAU,CAAE,CAAEC,SAAS,CAAE,CAAC,GAAG,CAAE,EAAE,CAAE,EAAE,CAAE,CACzC,CAAC,CAAC,CAEFpB,SAAS,CAAGD,GAAG,CAACsB,aAAa,CAACC,MAAM,CAAG,EAAE,CAC3C,CAEA;AACA,GAAI1G,aAAa,CAAC9B,MAAM,CAACmC,MAAM,EAAIL,aAAa,CAAC9B,MAAM,CAACmC,MAAM,CAAClP,MAAM,CAAG,CAAC,CAAE,CACzEgU,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC,CACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCH,GAAG,CAACI,IAAI,CAAC,iBAAiB,CAAE,EAAE,CAAEH,SAAS,CAAC,CAC1CA,SAAS,EAAI,EAAE,CAEf,KAAM,CAAAwB,cAAc,CAAG5G,aAAa,CAAC9B,MAAM,CAACmC,MAAM,CAAChO,GAAG,CAAC,CAACqD,MAAM,CAAEqQ,GAAG,GAAK,CACtEA,GAAG,CAAG,CAAC,CACPrQ,MAAM,CAACY,UAAU,EAAI,SAAS,CAC9BZ,MAAM,CAACsB,QAAQ,CAAGtB,MAAM,CAACsB,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,CAAG,MAAM,CAAG,KAAK,CAC7DvB,MAAM,CAAC0B,UAAU,EAAI,KAAK,CAC1B1B,MAAM,CAAC6B,QAAQ,EAAI,SAAS,CAC5B,GAAI,CAAAE,IAAI,CAAC/B,MAAM,CAAC8B,SAAS,CAAC,CAACE,cAAc,CAAC,OAAO,CAAE,CAAEC,QAAQ,CAAE,cAAe,CAAC,CAAC,CACjF,CAAC,CAEFrH,SAAS,CAAC6U,GAAG,CAAE,CACbc,IAAI,CAAE,CAAC,CAAC,GAAG,CAAE,MAAM,CAAE,MAAM,CAAE,OAAO,CAAE,aAAa,CAAE,WAAW,CAAC,CAAC,CAClEvM,IAAI,CAAEkN,cAAc,CACpBV,MAAM,CAAEd,SAAS,CACjBe,MAAM,CAAE,CAAEC,GAAG,CAAE,EAAG,CAAC,CACnBC,MAAM,CAAE,CAAEC,QAAQ,CAAE,CAAE,CAAC,CACvBC,UAAU,CAAE,CAAEC,SAAS,CAAE,CAAC,EAAE,CAAE,GAAG,CAAE,EAAE,CAAE,CACzC,CAAC,CAAC,CAEFpB,SAAS,CAAGD,GAAG,CAACsB,aAAa,CAACC,MAAM,CAAG,EAAE,CAC3C,CAEA;AACA,GAAI1G,aAAa,CAAC7B,KAAK,CAACkC,MAAM,EAAIL,aAAa,CAAC7B,KAAK,CAACkC,MAAM,CAAClP,MAAM,CAAG,CAAC,CAAE,CACvEgU,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC,CACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCH,GAAG,CAACI,IAAI,CAAC,gBAAgB,CAAE,EAAE,CAAEH,SAAS,CAAC,CACzCA,SAAS,EAAI,EAAE,CAEf,KAAM,CAAAyB,aAAa,CAAG7G,aAAa,CAAC7B,KAAK,CAACkC,MAAM,CAAChO,GAAG,CAAC,CAACqD,MAAM,CAAEqQ,GAAG,GAAK,CACpEA,GAAG,CAAG,CAAC,CACPrQ,MAAM,CAAC2B,SAAS,EAAI,SAAS,CAC7B3B,MAAM,CAAC4B,QAAQ,CAAG5B,MAAM,CAAC4B,QAAQ,CAACL,OAAO,CAAC,CAAC,CAAC,CAAG,IAAI,CAAG,KAAK,CAC3DvB,MAAM,CAACc,SAAS,EAAI,SAAS,CAC7Bd,MAAM,CAAC6B,QAAQ,EAAI,SAAS,CAC5B,GAAI,CAAAE,IAAI,CAAC/B,MAAM,CAAC8B,SAAS,CAAC,CAACE,cAAc,CAAC,OAAO,CAAE,CAAEC,QAAQ,CAAE,cAAe,CAAC,CAAC,CACjF,CAAC,CAEFrH,SAAS,CAAC6U,GAAG,CAAE,CACbc,IAAI,CAAE,CAAC,CAAC,GAAG,CAAE,MAAM,CAAE,QAAQ,CAAE,WAAW,CAAE,aAAa,CAAE,WAAW,CAAC,CAAC,CACxEvM,IAAI,CAAEmN,aAAa,CACnBX,MAAM,CAAEd,SAAS,CACjBe,MAAM,CAAE,CAAEC,GAAG,CAAE,EAAG,CAAC,CACnBC,MAAM,CAAE,CAAEC,QAAQ,CAAE,CAAE,CAAC,CACvBC,UAAU,CAAE,CAAEC,SAAS,CAAE,CAAC,CAAC,CAAE,GAAG,CAAE,GAAG,CAAE,CACzC,CAAC,CAAC,CACJ,CAEA;AACA,KAAM,CAAAM,SAAS,CAAG3B,GAAG,CAAC4B,QAAQ,CAACC,gBAAgB,CAAC,CAAC,CACjD,IAAK,GAAI,CAAAlK,CAAC,CAAG,CAAC,CAAEA,CAAC,EAAIgK,SAAS,CAAEhK,CAAC,EAAE,CAAE,CACnCqI,GAAG,CAAC8B,OAAO,CAACnK,CAAC,CAAC,CACdqI,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC,CACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,CAAE,QAAQ,CAAC,CAClCH,GAAG,CAACI,IAAI,CAAC,QAAQzI,CAAC,OAAOgK,SAAS,EAAE,CAAE,GAAG,CAAE3B,GAAG,CAAC4B,QAAQ,CAACG,QAAQ,CAACpJ,MAAM,CAAG,EAAE,CAAE,CAAE0H,KAAK,CAAE,QAAS,CAAC,CAAC,CACpG,CAEAL,GAAG,CAACgC,IAAI,CAAC,sBAAsB,CAAC,CAClC,CAAC,CAED,KAAM,CAAAC,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,MAAM,CAAG,CACb,CAAC,GAAG,CAAE,YAAY,CAAE,YAAY,CAAE,QAAQ,CAAE,aAAa,CAAE,WAAW,CAAC,CACvE,GAAG,CAACrH,aAAa,CAAC/B,QAAQ,CAACoC,MAAM,EAAI,EAAE,EAAEhO,GAAG,CAAC,CAACqD,MAAM,CAAEqQ,GAAG,GAAK,CAC5DA,GAAG,CAAG,CAAC,CACPrQ,MAAM,CAAC4R,IAAI,CACX5R,MAAM,CAAC6R,KAAK,CACZ7R,MAAM,CAACyB,MAAM,CACbzB,MAAM,CAAC6B,QAAQ,CACf,GAAI,CAAAE,IAAI,CAAC/B,MAAM,CAAC8B,SAAS,CAAC,CAACE,cAAc,CAAC,OAAO,CAAE,CAAEC,QAAQ,CAAE,cAAe,CAAC,CAAC,CACjF,CAAC,CACH,CACD,KAAM,CAAA6P,EAAE,CAAGjX,IAAI,CAACkX,KAAK,CAACC,YAAY,CAACL,MAAM,CAAC,CAC1C,KAAM,CAAAM,EAAE,CAAGpX,IAAI,CAACkX,KAAK,CAACG,QAAQ,CAAC,CAAC,CAChCrX,IAAI,CAACkX,KAAK,CAACI,iBAAiB,CAACF,EAAE,CAAEH,EAAE,CAAE,kBAAkB,CAAC,CACxD,KAAM,CAAAM,KAAK,CAAGvX,IAAI,CAACwX,KAAK,CAACJ,EAAE,CAAE,CAAEK,QAAQ,CAAE,MAAM,CAAE1N,IAAI,CAAE,OAAQ,CAAC,CAAC,CACjE9J,MAAM,CAAC,GAAI,CAAA4J,IAAI,CAAC,CAAC0N,KAAK,CAAC,CAAE,CAAExN,IAAI,CAAE,0BAA2B,CAAC,CAAC,CAAE,uBAAuB,CAAC,CAC1F,CAAC,CAED,mBACE1J,KAAA,CAAClB,SAAS,EAACuY,KAAK,MAACtV,SAAS,CAAC,qBAAqB,CAAA0C,QAAA,eAE9CzE,KAAA,CAACf,IAAI,EAAC8C,SAAS,CAAC,4CAA4C,CAAA0C,QAAA,eAC1D3E,IAAA,CAACb,IAAI,CAACwG,MAAM,EAAC1D,SAAS,CAAC,4BAA4B,CAAA0C,QAAA,cACjD3E,IAAA,OAAIiC,SAAS,CAAC,MAAM,CAAA0C,QAAA,CAAC,SAAO,CAAI,CAAC,CACtB,CAAC,cACd3E,IAAA,CAACb,IAAI,CAACgH,IAAI,EAAClE,SAAS,CAAC,MAAM,CAAA0C,QAAA,cACzBzE,KAAA,CAACjB,GAAG,EAACgD,SAAS,CAAC,KAAK,CAAA0C,QAAA,eAClB3E,IAAA,CAACd,GAAG,EAACsY,EAAE,CAAE,CAAE,CAAA7S,QAAA,cACTzE,KAAA,QAAK+B,SAAS,CAAC,gBAAgB,CAAA0C,QAAA,eAC7B3E,IAAA,OAAIiC,SAAS,CAAC,MAAM,CAAA0C,QAAA,CAAC,YAAU,CAAI,CAAC,cACpCzE,KAAA,QAAK+B,SAAS,CAAC,iBAAiB,CAAA0C,QAAA,eAC9B3E,IAAA,QAAKiC,SAAS,CAAC,cAAc,CAAA0C,QAAA,cAC3BzE,KAAA,CAACZ,IAAI,CAACmY,KAAK,EAAA9S,QAAA,eACT3E,IAAA,CAACV,IAAI,CAACoY,KAAK,EAACzV,SAAS,CAAC,YAAY,CAAA0C,QAAA,CAAC,YAAU,CAAY,CAAC,cAC1D3E,IAAA,CAACV,IAAI,CAACqY,OAAO,EACX/N,IAAI,CAAC,MAAM,CACXR,KAAK,CAAE+G,SAAU,CACjByH,QAAQ,CAAGzK,CAAC,EAAKiD,YAAY,CAACjD,CAAC,CAAC7J,MAAM,CAAC8F,KAAK,CAAE,CAC9CjC,IAAI,CAAC,IAAI,CACV,CAAC,EACQ,CAAC,CACV,CAAC,cACNnH,IAAA,QAAKiC,SAAS,CAAC,cAAc,CAAA0C,QAAA,cAC3BzE,KAAA,CAACZ,IAAI,CAACmY,KAAK,EAAA9S,QAAA,eACT3E,IAAA,CAACV,IAAI,CAACoY,KAAK,EAACzV,SAAS,CAAC,YAAY,CAAA0C,QAAA,CAAC,UAAQ,CAAY,CAAC,cACxD3E,IAAA,CAACV,IAAI,CAACqY,OAAO,EACX/N,IAAI,CAAC,MAAM,CACXR,KAAK,CAAEiH,OAAQ,CACfuH,QAAQ,CAAGzK,CAAC,EAAKmD,UAAU,CAACnD,CAAC,CAAC7J,MAAM,CAAC8F,KAAK,CAAE,CAC5CjC,IAAI,CAAC,IAAI,CACV,CAAC,EACQ,CAAC,CACV,CAAC,cACNjH,KAAA,QAAK+B,SAAS,CAAC,gBAAgB,CAAA0C,QAAA,eAC7B3E,IAAA,CAACT,MAAM,EACL4H,IAAI,CAAC,IAAI,CACTD,OAAO,CAAC,SAAS,CACjBE,OAAO,CAAEwM,qBAAsB,CAC/BlG,QAAQ,CAAE,CAACyC,SAAS,EAAI,CAACE,OAAQ,CAAA1L,QAAA,CAClC,OAED,CAAQ,CAAC,cACT3E,IAAA,CAACT,MAAM,EACL4H,IAAI,CAAC,IAAI,CACTD,OAAO,CAAC,mBAAmB,CAC3BE,OAAO,CAAEyM,qBAAsB,CAC/BnG,QAAQ,CAAE,CAAC6C,iBAAkB,CAAA5L,QAAA,CAC9B,OAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACL4L,iBAAiB,eAChBvQ,IAAA,QAAKiC,SAAS,CAAC,qCAAqC,CAAA0C,QAAA,cAClDzE,KAAA,UAAAyE,QAAA,EAAO,oBAAkB,CAAC,GAAI,CAAAoC,IAAI,CAACoJ,SAAS,CAAC,CAAC4E,kBAAkB,CAAC,CAAC,CAAC,MAAI,CAAC,GAAI,CAAAhO,IAAI,CAACsJ,OAAO,CAAC,CAAC0E,kBAAkB,CAAC,CAAC,EAAQ,CAAC,CACpH,CACN,EACE,CAAC,CACH,CAAC,cACN/U,IAAA,CAACd,GAAG,EAACsY,EAAE,CAAE,CAAE,CAAA7S,QAAA,cACTzE,KAAA,QAAK+B,SAAS,CAAC,gBAAgB,CAAA0C,QAAA,eAC7B3E,IAAA,OAAIiC,SAAS,CAAC,MAAM,CAAA0C,QAAA,CAAC,aAAW,CAAI,CAAC,cACrCzE,KAAA,QAAK+B,SAAS,CAAC,iBAAiB,CAAA0C,QAAA,eAC9B3E,IAAA,QAAKiC,SAAS,CAAC,cAAc,CAAA0C,QAAA,cAC3BzE,KAAA,CAACZ,IAAI,CAACmY,KAAK,EAAA9S,QAAA,eACT3E,IAAA,CAACV,IAAI,CAACoY,KAAK,EAACzV,SAAS,CAAC,YAAY,CAAA0C,QAAA,CAAC,aAAW,CAAY,CAAC,cAC3DzE,KAAA,CAACZ,IAAI,CAACuY,MAAM,EACVzO,KAAK,CAAEuH,YAAa,CACpBiH,QAAQ,CAAE1D,gBAAiB,CAC3B/M,IAAI,CAAC,IAAI,CAAAxC,QAAA,eAET3E,IAAA,WAAQoJ,KAAK,CAAC,EAAE,CAAAzE,QAAA,CAAC,WAAS,CAAQ,CAAC,CAClC8L,SAAS,CAAC9O,GAAG,CAAC,CAAC4M,IAAI,CAAE8F,KAAK,gBACzBnU,KAAA,WAAoBkJ,KAAK,CAAEmF,IAAI,CAAC1H,QAAS,CAAAlC,QAAA,EACtC4J,IAAI,CAAC1H,QAAQ,CAAC,IAAE,CAAC0H,IAAI,CAACX,IAAI,CAAC,GAC9B,GAFayG,KAEL,CACT,CAAC,EACS,CAAC,EACJ,CAAC,CACV,CAAC,cACNnU,KAAA,QAAK+B,SAAS,CAAC,gBAAgB,CAAA0C,QAAA,eAC7B3E,IAAA,CAACT,MAAM,EACL4H,IAAI,CAAC,IAAI,CACTD,OAAO,CAAC,SAAS,CACjBE,OAAO,CAAE4M,qBAAsB,CAAArP,QAAA,CAChC,OAED,CAAQ,CAAC,cACT3E,IAAA,CAACT,MAAM,EACL4H,IAAI,CAAC,IAAI,CACTD,OAAO,CAAC,mBAAmB,CAC3BE,OAAO,CAAE6M,qBAAsB,CAC/BvG,QAAQ,CAAE,CAACmD,iBAAkB,CAAAlM,QAAA,CAC9B,OAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACLkM,iBAAiB,eAChB7Q,IAAA,QAAKiC,SAAS,CAAC,qCAAqC,CAAA0C,QAAA,cAClDzE,KAAA,UAAAyE,QAAA,EAAO,yBAAuB,CAACgM,YAAY,EAAQ,CAAC,CACjD,CACN,EACE,CAAC,CACH,CAAC,EACH,CAAC,CACG,CAAC,EACR,CAAC,CAEN/L,OAAO,cACN5E,IAAA,QAAKiC,SAAS,CAAC,aAAa,CAAA0C,QAAA,cAC1B3E,IAAA,QAAKiC,SAAS,CAAC,6BAA6B,CAAC2L,IAAI,CAAC,QAAQ,CAAAjJ,QAAA,cACxD3E,IAAA,SAAMiC,SAAS,CAAC,iBAAiB,CAAA0C,QAAA,CAAC,YAAU,CAAM,CAAC,CAChD,CAAC,CACH,CAAC,CACJtB,KAAK,cACPrD,IAAA,QAAKiC,SAAS,CAAC,wBAAwB,CAAA0C,QAAA,CAAEtB,KAAK,CAAM,CAAC,cAErDnD,KAAA,CAAAE,SAAA,EAAAuE,QAAA,eAEE3E,IAAA,CAACb,IAAI,EAAC8C,SAAS,CAAC,+BAA+B,CAAA0C,QAAA,cAC7C3E,IAAA,CAACb,IAAI,CAACgH,IAAI,EAAClE,SAAS,CAAC,KAAK,CAAA0C,QAAA,cACxBzE,KAAA,CAACd,IAAI,EACH0Y,SAAS,CAAE3G,SAAU,CACrB4G,QAAQ,CAAGC,CAAC,EAAK5G,YAAY,CAAC4G,CAAC,CAAE,CACjC/V,SAAS,CAAC,gBAAgB,CAAA0C,QAAA,eAG1B3E,IAAA,CAACX,GAAG,EAAC4Y,QAAQ,CAAC,WAAW,CAAC5M,KAAK,CAAC,gBAAgB,CAAA1G,QAAA,cAC9CzE,KAAA,QAAK+B,SAAS,CAAC,KAAK,CAAA0C,QAAA,eAElBzE,KAAA,CAACjB,GAAG,EAACgD,SAAS,CAAC,UAAU,CAAA0C,QAAA,eACvB3E,IAAA,CAACd,GAAG,EAACwG,EAAE,CAAE,CAAE,CAAAf,QAAA,cACT3E,IAAA,CAACb,IAAI,EAAC8C,SAAS,CAAC,2CAA2C,CAAA0C,QAAA,cACzDzE,KAAA,CAACf,IAAI,CAACgH,IAAI,EAAClE,SAAS,CAAC,kBAAkB,CAAA0C,QAAA,eACrC3E,IAAA,OAAIiC,SAAS,CAAC,iBAAiB,CAAA0C,QAAA,CAAC,UAAQ,CAAI,CAAC,cAC7C3E,IAAA,OAAIiC,SAAS,CAAC,mBAAmB,CAAA0C,QAAA,CAAE6J,UAAU,CAACE,gBAAgB,CAAK,CAAC,EAC3D,CAAC,CACR,CAAC,CACJ,CAAC,cACN1O,IAAA,CAACd,GAAG,EAACwG,EAAE,CAAE,CAAE,CAAAf,QAAA,cACT3E,IAAA,CAACb,IAAI,EAAC8C,SAAS,CAAC,2CAA2C,CAAA0C,QAAA,cACzDzE,KAAA,CAACf,IAAI,CAACgH,IAAI,EAAClE,SAAS,CAAC,kBAAkB,CAAA0C,QAAA,eACrC3E,IAAA,OAAIiC,SAAS,CAAC,iBAAiB,CAAA0C,QAAA,CAAC,QAAM,CAAI,CAAC,cAC3C3E,IAAA,OAAIiC,SAAS,CAAC,mBAAmB,CAAA0C,QAAA,CAAE6J,UAAU,CAACG,cAAc,CAAK,CAAC,EACzD,CAAC,CACR,CAAC,CACJ,CAAC,cACN3O,IAAA,CAACd,GAAG,EAACwG,EAAE,CAAE,CAAE,CAAAf,QAAA,cACT3E,IAAA,CAACb,IAAI,EAAC8C,SAAS,CAAC,2CAA2C,CAAA0C,QAAA,cACzDzE,KAAA,CAACf,IAAI,CAACgH,IAAI,EAAClE,SAAS,CAAC,kBAAkB,CAAA0C,QAAA,eACrC3E,IAAA,OAAIiC,SAAS,CAAC,iBAAiB,CAAA0C,QAAA,CAAC,OAAK,CAAI,CAAC,cAC1C3E,IAAA,OAAIiC,SAAS,CAAC,mBAAmB,CAAA0C,QAAA,CAAE6J,UAAU,CAACI,aAAa,CAAK,CAAC,EACxD,CAAC,CACR,CAAC,CACJ,CAAC,cACN5O,IAAA,CAACd,GAAG,EAACwG,EAAE,CAAE,CAAE,CAAAf,QAAA,cACT3E,IAAA,CAACb,IAAI,EAAC8C,SAAS,CAAC,2CAA2C,CAAA0C,QAAA,cACzDzE,KAAA,CAACf,IAAI,CAACgH,IAAI,EAAClE,SAAS,CAAC,kBAAkB,CAAA0C,QAAA,eACrC3E,IAAA,OAAIiC,SAAS,CAAC,iBAAiB,CAAA0C,QAAA,CAAC,OAAK,CAAI,CAAC,cAC1C3E,IAAA,OAAIiC,SAAS,CAAC,mBAAmB,CAAA0C,QAAA,CAAE6J,UAAU,CAACK,UAAU,CAAK,CAAC,EACrD,CAAC,CACR,CAAC,CACJ,CAAC,EACH,CAAC,cAGN3O,KAAA,CAACjB,GAAG,EAACgD,SAAS,CAAC,UAAU,CAAA0C,QAAA,eACvB3E,IAAA,CAACd,GAAG,EAACwG,EAAE,CAAE,CAAE,CAAAf,QAAA,cACTzE,KAAA,CAACf,IAAI,EAAC8C,SAAS,CAAC,0BAA0B,CAAA0C,QAAA,eACxC3E,IAAA,CAACb,IAAI,CAACwG,MAAM,EAAC1D,SAAS,CAAC,4BAA4B,CAAA0C,QAAA,cACjD3E,IAAA,OAAIiC,SAAS,CAAC,MAAM,CAAA0C,QAAA,CAAC,wBAAsB,CAAI,CAAC,CACrC,CAAC,cACd3E,IAAA,CAACb,IAAI,CAACgH,IAAI,EAAClE,SAAS,CAAC,MAAM,CAAA0C,QAAA,cACzB3E,IAAA,CAACP,cAAc,EACbqM,IAAI,CAAE,CACJ,CACEoM,CAAC,CAAEpJ,UAAU,CAACE,IAAI,CAClBmJ,CAAC,CAAErJ,UAAU,CAACG,MAAM,CACpBrF,IAAI,CAAE,SAAS,CACfwO,IAAI,CAAE,eAAe,CACrBC,MAAM,CAAE,CAAEC,KAAK,CAAE,SAAU,CAC7B,CAAC,CACD,CACFC,MAAM,CAAE,CACNC,KAAK,CAAE,CAAEnN,KAAK,CAAE,KAAM,CAAC,CACvBoN,KAAK,CAAE,CAAEpN,KAAK,CAAE,iBAAkB,CACpC,CAAE,CACH,CAAC,CACO,CAAC,EACR,CAAC,CACJ,CAAC,cAENrL,IAAA,CAACd,GAAG,EAACwG,EAAE,CAAE,CAAE,CAAAf,QAAA,cACTzE,KAAA,CAACf,IAAI,EAAC8C,SAAS,CAAC,0BAA0B,CAAA0C,QAAA,eACxC3E,IAAA,CAACb,IAAI,CAACwG,MAAM,EAAC1D,SAAS,CAAC,4BAA4B,CAAA0C,QAAA,cACjD3E,IAAA,OAAIiC,SAAS,CAAC,MAAM,CAAA0C,QAAA,CAAC,gBAAc,CAAI,CAAC,CAC7B,CAAC,cACd3E,IAAA,CAACb,IAAI,CAACgH,IAAI,EAAClE,SAAS,CAAC,MAAM,CAAA0C,QAAA,cACzB3E,IAAA,CAACP,cAAc,EACbqM,IAAI,CAAE,CACJ,CACElC,IAAI,CAAE,KAAK,CACXsO,CAAC,CAAEjH,oBAAoB,CAAC7B,KAAK,CAC7B+I,CAAC,CAAElH,oBAAoB,CAAC5B,MAAM,CAC9BgJ,MAAM,CAAE,CACNC,KAAK,CAAErH,oBAAoB,CAAC7B,KAAK,CAACzN,GAAG,CAACiI,IAAI,EAAI,CAC5C,GAAIA,IAAI,CAACzI,QAAQ,CAAC,SAAS,CAAC,CAAE,MAAO,SAAS,CAC9C,GAAIyI,IAAI,CAACzI,QAAQ,CAAC,OAAO,CAAC,CAAE,MAAO,SAAS,CAC5C,GAAIyI,IAAI,CAACzI,QAAQ,CAAC,MAAM,CAAC,CAAE,MAAO,SAAS,CAC3C,MAAO,SAAS,CAClB,CAAC,CACH,CACF,CAAC,CACD,CACFoX,MAAM,CAAE,CACNC,KAAK,CAAE,CACLnN,KAAK,CAAE,YAAY,CACnBqN,SAAS,CAAE,CAAC,EAAE,CACdC,UAAU,CAAE,IACd,CAAC,CACDF,KAAK,CAAE,CAAEpN,KAAK,CAAE,OAAQ,CAAC,CACzBoK,MAAM,CAAE,CAAEmD,CAAC,CAAE,EAAE,CAAEC,CAAC,CAAE,EAAE,CAAEC,CAAC,CAAE,EAAE,CAAEC,CAAC,CAAE,EAAG,CACvC,CAAE,CACFC,UAAU,CAAE,IAAK,CACjBC,WAAW,CAAE,CACX,CACEC,KAAK,CAAE,UAAU,CACjBZ,KAAK,CAAE,SAAS,CAChBa,OAAO,CAAEpI,aAAa,CAACxD,QAAQ,CAC/BqK,QAAQ,CAAEA,CAAA,GAAMtD,wBAAwB,CAAC,UAAU,CACrD,CAAC,CACD,CACE4E,KAAK,CAAE,QAAQ,CACfZ,KAAK,CAAE,SAAS,CAChBa,OAAO,CAAEpI,aAAa,CAACvD,MAAM,CAC7BoK,QAAQ,CAAEA,CAAA,GAAMtD,wBAAwB,CAAC,QAAQ,CACnD,CAAC,CACD,CACE4E,KAAK,CAAE,OAAO,CACdZ,KAAK,CAAE,SAAS,CAChBa,OAAO,CAAEpI,aAAa,CAACtD,KAAK,CAC5BmK,QAAQ,CAAEA,CAAA,GAAMtD,wBAAwB,CAAC,OAAO,CAClD,CAAC,CACD,CACFrS,SAAS,CAAC,gBAAgB,CAC3B,CAAC,CACO,CAAC,EACR,CAAC,CACJ,CAAC,EACH,CAAC,cAGNjC,IAAA,CAACf,GAAG,EAACgD,SAAS,CAAC,MAAM,CAAA0C,QAAA,cACnB3E,IAAA,CAACd,GAAG,EAACwG,EAAE,CAAE,EAAG,CAAAf,QAAA,cACVzE,KAAA,CAACf,IAAI,EAAC8C,SAAS,CAAC,0BAA0B,CAAA0C,QAAA,eACxC3E,IAAA,CAACb,IAAI,CAACwG,MAAM,EAAC1D,SAAS,CAAC,4BAA4B,CAAA0C,QAAA,cACjD3E,IAAA,OAAIiC,SAAS,CAAC,MAAM,CAAA0C,QAAA,CAAC,6BAA2B,CAAI,CAAC,CAC1C,CAAC,cACd3E,IAAA,CAACb,IAAI,CAACgH,IAAI,EAAClE,SAAS,CAAC,MAAM,CAAA0C,QAAA,cACzB3E,IAAA,CAACP,cAAc,EACbqM,IAAI,CAAE,CACJ,CACElC,IAAI,CAAE,KAAK,CACXwP,MAAM,CAAE,CAAC,UAAU,CAAE,QAAQ,CAAE,OAAO,CAAC,CACvCC,MAAM,CAAE,CACN7K,UAAU,CAACE,gBAAgB,CAC3BF,UAAU,CAACG,cAAc,CACzBH,UAAU,CAACI,aAAa,CACzB,CACDyJ,MAAM,CAAE,CACNiB,MAAM,CAAE,CAAC,SAAS,CAAE,SAAS,CAAE,SAAS,CAC1C,CAAC,CACDC,QAAQ,CAAE,eAAe,CACzBC,qBAAqB,CAAE,QACzB,CAAC,CACD,CACFjB,MAAM,CAAE,CACNnL,MAAM,CAAE,GACV,CAAE,CACFqM,UAAU,CAAE,IAAK,CAClB,CAAC,CACO,CAAC,EACR,CAAC,CACJ,CAAC,CACH,CAAC,cAGNzZ,IAAA,CAACf,GAAG,EAACgD,SAAS,CAAC,MAAM,CAAA0C,QAAA,cACnB3E,IAAA,CAACd,GAAG,EAACwG,EAAE,CAAE,EAAG,CAAAf,QAAA,cACVzE,KAAA,CAACf,IAAI,EAAC8C,SAAS,CAAC,0BAA0B,CAAA0C,QAAA,eACxC3E,IAAA,CAACb,IAAI,CAACwG,MAAM,EAAC1D,SAAS,CAAC,4BAA4B,CAAA0C,QAAA,cACjD3E,IAAA,OAAIiC,SAAS,CAAC,MAAM,CAAA0C,QAAA,CAAC,qBAAmB,CAAI,CAAC,CAClC,CAAC,cACd3E,IAAA,CAACb,IAAI,CAACgH,IAAI,EAAClE,SAAS,CAAC,MAAM,CAAA0C,QAAA,cACzBzE,KAAA,CAACd,IAAI,EAACsa,gBAAgB,CAAC,UAAU,CAACzX,SAAS,CAAC,MAAM,CAAA0C,QAAA,eAChD3E,IAAA,CAACX,GAAG,EAAC4Y,QAAQ,CAAC,UAAU,CAAC5M,KAAK,CAAE,aAAaiE,aAAa,CAAC/B,QAAQ,CAACoC,MAAM,CAAClP,MAAM,GAAI,CAAAkE,QAAA,cACnF3E,IAAA,QAAKkC,KAAK,CAAE,CAAEkE,SAAS,CAAE,OAAO,CAAEuT,SAAS,CAAE,MAAM,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAjV,QAAA,cAC1E3E,IAAA,CAACf,GAAG,EAAA0F,QAAA,CACD2K,aAAa,CAAC/B,QAAQ,CAACoC,MAAM,CAAChO,GAAG,CAAC,CAACkY,OAAO,CAAExF,KAAK,gBAChDrU,IAAA,CAAC8E,SAAS,EAERE,MAAM,CAAE6U,OAAQ,CAChB5U,UAAU,CAAC,UAAU,CACrBC,WAAW,CAAC,YAAY,EAHnB,WAAW2U,OAAO,CAAC7T,UAAU,EAAI6T,OAAO,CAACxS,QAAQ,EAAIgN,KAAK,EAIhE,CACF,CAAC,CACC,CAAC,CACH,CAAC,CACH,CAAC,cACNrU,IAAA,CAACX,GAAG,EAAC4Y,QAAQ,CAAC,QAAQ,CAAC5M,KAAK,CAAE,WAAWiE,aAAa,CAAC9B,MAAM,CAACmC,MAAM,CAAClP,MAAM,GAAI,CAAAkE,QAAA,cAC7E3E,IAAA,QAAKkC,KAAK,CAAE,CAAEkE,SAAS,CAAE,OAAO,CAAEuT,SAAS,CAAE,MAAM,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAjV,QAAA,cAC1E3E,IAAA,CAACf,GAAG,EAAA0F,QAAA,CACD2K,aAAa,CAAC9B,MAAM,CAACmC,MAAM,CAAChO,GAAG,CAAC,CAACmY,KAAK,CAAEzF,KAAK,gBAC5CrU,IAAA,CAAC8E,SAAS,EAERE,MAAM,CAAE8U,KAAM,CACd7U,UAAU,CAAC,QAAQ,CACnBC,WAAW,CAAC,UAAU,EAHjB,SAAS4U,KAAK,CAACjU,QAAQ,EAAIiU,KAAK,CAACzS,QAAQ,EAAIgN,KAAK,EAIxD,CACF,CAAC,CACC,CAAC,CACH,CAAC,CACH,CAAC,cACNrU,IAAA,CAACX,GAAG,EAAC4Y,QAAQ,CAAC,OAAO,CAAC5M,KAAK,CAAE,UAAUiE,aAAa,CAAC7B,KAAK,CAACkC,MAAM,CAAClP,MAAM,GAAI,CAAAkE,QAAA,cAC1E3E,IAAA,QAAKkC,KAAK,CAAE,CAAEkE,SAAS,CAAE,OAAO,CAAEuT,SAAS,CAAE,MAAM,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAjV,QAAA,cAC1E3E,IAAA,CAACf,GAAG,EAAA0F,QAAA,CACD2K,aAAa,CAAC7B,KAAK,EAAI6B,aAAa,CAAC7B,KAAK,CAACkC,MAAM,EAAIL,aAAa,CAAC7B,KAAK,CAACkC,MAAM,CAAClP,MAAM,CAAG,CAAC,CACzF6O,aAAa,CAAC7B,KAAK,CAACkC,MAAM,CAAChO,GAAG,CAAC,CAACoY,IAAI,CAAE1F,KAAK,gBACzCrU,IAAA,CAAC8E,SAAS,EAERE,MAAM,CAAE+U,IAAK,CACb9U,UAAU,CAAC,OAAO,CAClBC,WAAW,CAAC,SAAS,EAHhB,QAAQ6U,IAAI,CAAChU,OAAO,EAAIgU,IAAI,CAAC1S,QAAQ,EAAIgN,KAAK,EAIpD,CACF,CAAC,cAEFrU,IAAA,CAACd,GAAG,EAAAyF,QAAA,cACF3E,IAAA,QAAKiC,SAAS,CAAC,sBAAsB,CAAA0C,QAAA,CAAC,yFAEtC,CAAK,CAAC,CACH,CACN,CACE,CAAC,CACH,CAAC,CACH,CAAC,EACF,CAAC,CACE,CAAC,EACR,CAAC,CACJ,CAAC,CACH,CAAC,cAGN3E,IAAA,CAACf,GAAG,EAACgD,SAAS,CAAC,MAAM,CAAA0C,QAAA,cACnB3E,IAAA,CAACd,GAAG,EAACwG,EAAE,CAAE,EAAG,CAAAf,QAAA,cACVzE,KAAA,CAACf,IAAI,EAAC8C,SAAS,CAAC,0BAA0B,CAAA0C,QAAA,eACxC3E,IAAA,CAACb,IAAI,CAACwG,MAAM,EAAC1D,SAAS,CAAC,yBAAyB,CAAA0C,QAAA,cAC9CzE,KAAA,OAAI+B,SAAS,CAAC,MAAM,CAAA0C,QAAA,EAAC,wBAAsB,CAAC,EAAAsJ,qBAAA,CAAAqB,aAAa,CAACU,MAAM,UAAA/B,qBAAA,kBAAAC,sBAAA,CAApBD,qBAAA,CAAsB0B,MAAM,UAAAzB,sBAAA,iBAA5BA,sBAAA,CAA8BzN,MAAM,GAAI,CAAC,CAAC,GAAC,EAAI,CAAC,CACjF,CAAC,cACdT,IAAA,CAACb,IAAI,CAACgH,IAAI,EAAClE,SAAS,CAAC,MAAM,CAAA0C,QAAA,CACxB,CAAAwJ,sBAAA,CAAAmB,aAAa,CAACU,MAAM,UAAA7B,sBAAA,WAApBA,sBAAA,CAAsBwB,MAAM,EAAIL,aAAa,CAACU,MAAM,CAACL,MAAM,CAAClP,MAAM,CAAG,CAAC,cACrET,IAAA,QAAKkC,KAAK,CAAE,CAAEkE,SAAS,CAAE,OAAO,CAAEuT,SAAS,CAAE,MAAM,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAjV,QAAA,cAC1E3E,IAAA,CAACf,GAAG,EAAA0F,QAAA,CACD2K,aAAa,CAACU,MAAM,CAACL,MAAM,CAAChO,GAAG,CAAC,CAAC+F,KAAK,CAAE2M,KAAK,gBAC5CrU,IAAA,CAACwH,SAAS,EAERE,KAAK,CAAEA,KAAM,EADR,SAASA,KAAK,CAACU,QAAQ,EAAIiM,KAAK,EAEtC,CACF,CAAC,CACC,CAAC,CACH,CAAC,cAENrU,IAAA,QAAKiC,SAAS,CAAC,sBAAsB,CAAA0C,QAAA,CAAC,0GAEtC,CAAK,CACN,CACQ,CAAC,EACR,CAAC,CACJ,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,cAGN3E,IAAA,CAACX,GAAG,EAAC4Y,QAAQ,CAAC,KAAK,CAAC5M,KAAK,CAAC,iBAAiB,CAAA1G,QAAA,cACzC3E,IAAA,QAAKiC,SAAS,CAAC,KAAK,CAAA0C,QAAA,cAClB3E,IAAA,CAACN,SAAS,EAAC6O,IAAI,CAAEA,IAAK,CAAE,CAAC,CACtB,CAAC,CACH,CAAC,cAGNvO,IAAA,CAACX,GAAG,EAAC4Y,QAAQ,CAAC,OAAO,CAAC5M,KAAK,CAAC,gBAAgB,CAAA1G,QAAA,cAC1C3E,IAAA,QAAKiC,SAAS,CAAC,KAAK,CAAA0C,QAAA,cAClBzE,KAAA,CAACjB,GAAG,EAACgD,SAAS,CAAC,KAAK,CAAA0C,QAAA,eAClB3E,IAAA,CAACd,GAAG,EAACwG,EAAE,CAAE,CAAE,CAAAf,QAAA,cACTzE,KAAA,CAACf,IAAI,EAAC8C,SAAS,CAAC,0BAA0B,CAAA0C,QAAA,eACxC3E,IAAA,CAACb,IAAI,CAACwG,MAAM,EAAC1D,SAAS,CAAC,4BAA4B,CAAA0C,QAAA,cACjD3E,IAAA,OAAIiC,SAAS,CAAC,MAAM,CAAA0C,QAAA,CAAC,2BAAyB,CAAI,CAAC,CACxC,CAAC,cACd3E,IAAA,CAACb,IAAI,CAACgH,IAAI,EAAClE,SAAS,CAAC,MAAM,CAAA0C,QAAA,cACzB3E,IAAA,CAACP,cAAc,EACbqM,IAAI,CAAE,CACJ,CACElC,IAAI,CAAE,KAAK,CACXwP,MAAM,CAAElV,MAAM,CAAC8V,IAAI,CAAC,EAAA5L,qBAAA,CAAAkB,aAAa,CAACQ,KAAK,UAAA1B,qBAAA,iBAAnBA,qBAAA,CAAqB2B,OAAO,GAAI,CAAC,CAAC,CAAC,CACvDsJ,MAAM,CAAEnV,MAAM,CAACmV,MAAM,CAAC,EAAAhL,qBAAA,CAAAiB,aAAa,CAACQ,KAAK,UAAAzB,qBAAA,iBAAnBA,qBAAA,CAAqB0B,OAAO,GAAI,CAAC,CAAC,CAAC,CACzDsI,MAAM,CAAE,CACNiB,MAAM,CAAE,CAAC,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CACrD,CAAC,CACDC,QAAQ,CAAE,eAAe,CACzBC,qBAAqB,CAAE,QACzB,CAAC,CACD,CACFC,UAAU,CAAE,IAAK,CAClB,CAAC,CACO,CAAC,EACR,CAAC,CACJ,CAAC,cACNzZ,IAAA,CAACd,GAAG,EAACwG,EAAE,CAAE,CAAE,CAAAf,QAAA,cACTzE,KAAA,CAACf,IAAI,EAAC8C,SAAS,CAAC,0BAA0B,CAAA0C,QAAA,eACxC3E,IAAA,CAACb,IAAI,CAACwG,MAAM,EAAC1D,SAAS,CAAC,4BAA4B,CAAA0C,QAAA,cACjD3E,IAAA,OAAIiC,SAAS,CAAC,MAAM,CAAA0C,QAAA,CAAC,cAAY,CAAI,CAAC,CAC3B,CAAC,cACd3E,IAAA,CAACb,IAAI,CAACgH,IAAI,EAAClE,SAAS,CAAC,MAAM,CAAA0C,QAAA,cACzB3E,IAAA,QAAKiC,SAAS,CAAC,kBAAkB,CAAA0C,QAAA,cAC/BzE,KAAA,UAAO+B,SAAS,CAAC,4BAA4B,CAAA0C,QAAA,eAC3C3E,IAAA,UAAA2E,QAAA,cACEzE,KAAA,OAAAyE,QAAA,eACE3E,IAAA,OAAA2E,QAAA,CAAI,UAAQ,CAAI,CAAC,cACjB3E,IAAA,OAAA2E,QAAA,CAAI,MAAI,CAAI,CAAC,cACb3E,IAAA,OAAA2E,QAAA,CAAI,YAAU,CAAI,CAAC,EACjB,CAAC,CACA,CAAC,cACR3E,IAAA,UAAA2E,QAAA,CACG,CAAA2J,qBAAA,CAAAgB,aAAa,CAACQ,KAAK,UAAAxB,qBAAA,WAAnBA,qBAAA,CAAqBqB,MAAM,EAAIL,aAAa,CAACQ,KAAK,CAACH,MAAM,CAAClP,MAAM,CAAG,CAAC,CACnE6O,aAAa,CAACQ,KAAK,CAACH,MAAM,CAAChO,GAAG,CAAC,CAAC4M,IAAI,CAAE8F,KAAK,gBACzCnU,KAAA,OAAAyE,QAAA,eACE3E,IAAA,OAAA2E,QAAA,CAAK4J,IAAI,CAAC1H,QAAQ,CAAK,CAAC,cACxB7G,IAAA,OAAA2E,QAAA,cACE3E,IAAA,SAAMiC,SAAS,CAAE,YACfsM,IAAI,CAACX,IAAI,GAAK,OAAO,CAAG,QAAQ,CAChCW,IAAI,CAACX,IAAI,GAAK,SAAS,CAAG,SAAS,CACnC,SAAS,EACR,CAAAjJ,QAAA,CACA4J,IAAI,CAACX,IAAI,CACN,CAAC,CACL,CAAC,cACL5N,IAAA,OAAA2E,QAAA,CAAK,GAAI,CAAAoC,IAAI,CAACwH,IAAI,CAAC+G,UAAU,CAAC,CAACtO,cAAc,CAAC,OAAO,CAAE,CAAEC,QAAQ,CAAE,cAAe,CAAC,CAAC,CAAK,CAAC,GAXnF,QAAQoN,KAAK,EAYlB,CACL,CAAC,cAEFrU,IAAA,OAAA2E,QAAA,cACE3E,IAAA,OAAIia,OAAO,CAAC,GAAG,CAAChY,SAAS,CAAC,aAAa,CAAA0C,QAAA,CAAC,yBAAuB,CAAI,CAAC,CAClE,CACL,CACI,CAAC,EACH,CAAC,CACL,CAAC,CACG,CAAC,EACR,CAAC,CACJ,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,EACF,CAAC,CACE,CAAC,CACR,CAAC,cAGPzE,KAAA,QAAK+B,SAAS,CAAC,iCAAiC,CAAA0C,QAAA,eAC9CzE,KAAA,CAACX,MAAM,EAAC2H,OAAO,CAAC,gBAAgB,CAACC,IAAI,CAAC,IAAI,CAAClF,SAAS,CAAC,MAAM,CAACmF,OAAO,CAAEmN,iBAAkB,CAAA5P,QAAA,eACrF3E,IAAA,MAAGiC,SAAS,CAAC,sBAAsB,CAAI,CAAC,eAC1C,EAAQ,CAAC,cACT/B,KAAA,CAACX,MAAM,EAAC2H,OAAO,CAAC,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAACC,OAAO,CAAEsP,mBAAoB,CAAA/R,QAAA,eACvE3E,IAAA,MAAGiC,SAAS,CAAC,wBAAwB,CAAI,CAAC,iBAC5C,EAAQ,CAAC,EACN,CAAC,EACN,CACH,EACQ,CAAC,CAEhB,CAEA,cAAe,CAAA8L,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}