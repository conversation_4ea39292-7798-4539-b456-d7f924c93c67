[{"C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\App.js": "3", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Login.js": "4", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Home.js": "5", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\RoadInfrastructure.js": "6", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Recommendation.js": "7", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Pavement.js": "8", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\DefectDetail.js": "9", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Dashboard.js": "10", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\Header.js": "11", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\Sidebar.js": "12", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\ResponsiveCheckboxList.js": "13", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\ChartContainer.js": "14", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\VideoDefectDetection.js": "15", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\DefectMap.js": "16", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\hooks\\useResponsive.js": "17", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\utils\\fileValidation.js": "18"}, {"size": 633, "mtime": 1753938470359, "results": "19", "hashOfConfig": "20"}, {"size": 376, "mtime": 1753938470372, "results": "21", "hashOfConfig": "20"}, {"size": 3053, "mtime": 1757765143404, "results": "22", "hashOfConfig": "20"}, {"size": 4500, "mtime": 1757765294622, "results": "23", "hashOfConfig": "20"}, {"size": 2994, "mtime": 1753938470363, "results": "24", "hashOfConfig": "20"}, {"size": 38727, "mtime": 1757912487000, "results": "25", "hashOfConfig": "20"}, {"size": 27373, "mtime": 1753938470368, "results": "26", "hashOfConfig": "20"}, {"size": 94726, "mtime": 1758085056883, "results": "27", "hashOfConfig": "20"}, {"size": 16654, "mtime": 1757414574475, "results": "28", "hashOfConfig": "20"}, {"size": 68552, "mtime": 1758087770598, "results": "29", "hashOfConfig": "20"}, {"size": 659, "mtime": 1753938470351, "results": "30", "hashOfConfig": "20"}, {"size": 4656, "mtime": 1755329373591, "results": "31", "hashOfConfig": "20"}, {"size": 4005, "mtime": 1753938470352, "results": "32", "hashOfConfig": "20"}, {"size": 3414, "mtime": 1753938470350, "results": "33", "hashOfConfig": "20"}, {"size": 38428, "mtime": 1757912431458, "results": "34", "hashOfConfig": "20"}, {"size": 14715, "mtime": 1756545876481, "results": "35", "hashOfConfig": "20"}, {"size": 1018, "mtime": 1753938470357, "results": "36", "hashOfConfig": "20"}, {"size": 8812, "mtime": 1757912331715, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1djdb2s", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Login.js", ["92", "93", "94", "95"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\RoadInfrastructure.js", ["96", "97", "98", "99", "100", "101", "102"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Recommendation.js", ["103", "104", "105", "106", "107", "108", "109", "110", "111", "112"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Pavement.js", ["113", "114", "115", "116", "117", "118", "119", "120", "121", "122"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\DefectDetail.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Dashboard.js", ["123", "124"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\Header.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\Sidebar.js", ["125", "126"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\ResponsiveCheckboxList.js", ["127"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\ChartContainer.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\VideoDefectDetection.js", ["128", "129", "130", "131", "132", "133", "134", "135", "136"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\DefectMap.js", ["137", "138", "139"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\hooks\\useResponsive.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\utils\\fileValidation.js", [], [], {"ruleId": "140", "severity": 1, "message": "141", "line": 3, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 3, "endColumn": 19}, {"ruleId": "140", "severity": 1, "message": "144", "line": 3, "column": 21, "nodeType": "142", "messageId": "143", "endLine": 3, "endColumn": 24}, {"ruleId": "140", "severity": 1, "message": "145", "line": 3, "column": 26, "nodeType": "142", "messageId": "143", "endLine": 3, "endColumn": 29}, {"ruleId": "146", "severity": 1, "message": "147", "line": 52, "column": 29, "nodeType": "148", "messageId": "149", "endLine": 52, "endColumn": 78}, {"ruleId": "140", "severity": 1, "message": "150", "line": 4, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 4, "endColumn": 22}, {"ruleId": "140", "severity": 1, "message": "151", "line": 4, "column": 24, "nodeType": "142", "messageId": "143", "endLine": 4, "endColumn": 33}, {"ruleId": "140", "severity": 1, "message": "152", "line": 4, "column": 35, "nodeType": "142", "messageId": "143", "endLine": 4, "endColumn": 41}, {"ruleId": "140", "severity": 1, "message": "153", "line": 4, "column": 43, "nodeType": "142", "messageId": "143", "endLine": 4, "endColumn": 48}, {"ruleId": "140", "severity": 1, "message": "154", "line": 24, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 24, "endColumn": 19}, {"ruleId": "140", "severity": 1, "message": "155", "line": 71, "column": 9, "nodeType": "142", "messageId": "143", "endLine": 71, "endColumn": 25}, {"ruleId": "156", "severity": 1, "message": "157", "line": 555, "column": 19, "nodeType": "158", "endLine": 559, "endColumn": 21}, {"ruleId": "140", "severity": 1, "message": "159", "line": 3, "column": 58, "nodeType": "142", "messageId": "143", "endLine": 3, "endColumn": 63}, {"ruleId": "140", "severity": 1, "message": "160", "line": 3, "column": 65, "nodeType": "142", "messageId": "143", "endLine": 3, "endColumn": 69}, {"ruleId": "140", "severity": 1, "message": "161", "line": 3, "column": 71, "nodeType": "142", "messageId": "143", "endLine": 3, "endColumn": 74}, {"ruleId": "140", "severity": 1, "message": "162", "line": 4, "column": 8, "nodeType": "142", "messageId": "143", "endLine": 4, "endColumn": 12}, {"ruleId": "140", "severity": 1, "message": "163", "line": 76, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 76, "endColumn": 25}, {"ruleId": "140", "severity": 1, "message": "164", "line": 79, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 79, "endColumn": 19}, {"ruleId": "140", "severity": 1, "message": "165", "line": 277, "column": 9, "nodeType": "142", "messageId": "143", "endLine": 277, "endColumn": 22}, {"ruleId": "140", "severity": 1, "message": "166", "line": 291, "column": 9, "nodeType": "142", "messageId": "143", "endLine": 291, "endColumn": 21}, {"ruleId": "140", "severity": 1, "message": "167", "line": 305, "column": 9, "nodeType": "142", "messageId": "143", "endLine": 305, "endColumn": 30}, {"ruleId": "140", "severity": 1, "message": "168", "line": 318, "column": 9, "nodeType": "142", "messageId": "143", "endLine": 318, "endColumn": 28}, {"ruleId": "140", "severity": 1, "message": "169", "line": 19, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 19, "endColumn": 24}, {"ruleId": "140", "severity": 1, "message": "170", "line": 20, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 20, "endColumn": 17}, {"ruleId": "140", "severity": 1, "message": "171", "line": 39, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 39, "endColumn": 33}, {"ruleId": "140", "severity": 1, "message": "172", "line": 40, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 40, "endColumn": 29}, {"ruleId": "173", "severity": 1, "message": "174", "line": 886, "column": 6, "nodeType": "175", "endLine": 886, "endColumn": 33, "suggestions": "176"}, {"ruleId": "173", "severity": 1, "message": "177", "line": 895, "column": 6, "nodeType": "175", "endLine": 895, "endColumn": 20, "suggestions": "178"}, {"ruleId": "173", "severity": 1, "message": "179", "line": 932, "column": 6, "nodeType": "175", "endLine": 932, "endColumn": 33, "suggestions": "180"}, {"ruleId": "156", "severity": 1, "message": "157", "line": 978, "column": 21, "nodeType": "158", "endLine": 982, "endColumn": 23}, {"ruleId": "156", "severity": 1, "message": "157", "line": 2113, "column": 17, "nodeType": "158", "endLine": 2123, "endColumn": 19}, {"ruleId": "156", "severity": 1, "message": "157", "line": 2131, "column": 19, "nodeType": "158", "endLine": 2141, "endColumn": 21}, {"ruleId": "140", "severity": 1, "message": "162", "line": 4, "column": 8, "nodeType": "142", "messageId": "143", "endLine": 4, "endColumn": 12}, {"ruleId": "173", "severity": 1, "message": "181", "line": 773, "column": 6, "nodeType": "175", "endLine": 773, "endColumn": 8, "suggestions": "182"}, {"ruleId": "140", "severity": 1, "message": "183", "line": 18, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 18, "endColumn": 20}, {"ruleId": "184", "severity": 1, "message": "185", "line": 93, "column": 11, "nodeType": "158", "endLine": 93, "endColumn": 15}, {"ruleId": "140", "severity": 1, "message": "186", "line": 35, "column": 11, "nodeType": "142", "messageId": "143", "endLine": 35, "endColumn": 16}, {"ruleId": "140", "severity": 1, "message": "187", "line": 13, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 13, "endColumn": 24}, {"ruleId": "140", "severity": 1, "message": "188", "line": 17, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 17, "endColumn": 20}, {"ruleId": "140", "severity": 1, "message": "189", "line": 24, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 24, "endColumn": 24}, {"ruleId": "140", "severity": 1, "message": "190", "line": 37, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 37, "endColumn": 27}, {"ruleId": "140", "severity": 1, "message": "191", "line": 47, "column": 9, "nodeType": "142", "messageId": "143", "endLine": 47, "endColumn": 20}, {"ruleId": "173", "severity": 1, "message": "192", "line": 104, "column": 6, "nodeType": "175", "endLine": 104, "endColumn": 19, "suggestions": "193"}, {"ruleId": "140", "severity": 1, "message": "194", "line": 559, "column": 9, "nodeType": "142", "messageId": "143", "endLine": 559, "endColumn": 24}, {"ruleId": "140", "severity": 1, "message": "195", "line": 560, "column": 9, "nodeType": "142", "messageId": "143", "endLine": 560, "endColumn": 21}, {"ruleId": "140", "severity": 1, "message": "196", "line": 561, "column": 9, "nodeType": "142", "messageId": "143", "endLine": 561, "endColumn": 22}, {"ruleId": "140", "severity": 1, "message": "197", "line": 42, "column": 18, "nodeType": "142", "messageId": "143", "endLine": 42, "endColumn": 27}, {"ruleId": "140", "severity": 1, "message": "198", "line": 43, "column": 16, "nodeType": "142", "messageId": "143", "endLine": 43, "endColumn": 23}, {"ruleId": "173", "severity": 1, "message": "199", "line": 141, "column": 6, "nodeType": "175", "endLine": 141, "endColumn": 12, "suggestions": "200"}, "no-unused-vars", "'Container' is defined but never used.", "Identifier", "unusedVar", "'Row' is defined but never used.", "'Col' is defined but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'retryCount'.", "ArrowFunctionExpression", "unsafeRefs", "'MapContainer' is defined but never used.", "'TileLayer' is defined but never used.", "'Marker' is defined but never used.", "'Popup' is defined but never used.", "'imageFile' is assigned a value but never used.", "'roadInfraClasses' is assigned a value but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'Badge' is defined but never used.", "'Tabs' is defined but never used.", "'Tab' is defined but never used.", "'Plot' is defined but never used.", "'recommendations' is assigned a value but never used.", "'chartData' is assigned a value but never used.", "'handleApprove' is assigned a value but never used.", "'handleReject' is assigned a value but never used.", "'getPriorityBadgeClass' is assigned a value but never used.", "'getStatusBadgeClass' is assigned a value but never used.", "'processedImage' is assigned a value but never used.", "'results' is assigned a value but never used.", "'showClassificationModal' is assigned a value but never used.", "'classificationError' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'batchResults.length'. Either include it or remove the dependency array.", "ArrayExpression", ["201"], "React Hook useEffect has missing dependencies: 'handleLocationRequest' and 'locationPermission'. Either include them or remove the dependency array.", ["202"], "React Hook useEffect has a missing dependency: 'handleLocationRequest'. Either include it or remove the dependency array.", ["203"], "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["204"], "'activePage' is assigned a value but never used.", "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "'value' is assigned a value but never used.", "'processedVideo' is assigned a value but never used.", "'shouldStop' is assigned a value but never used.", "'recordedChunks' is assigned a value but never used.", "'currentDetections' is assigned a value but never used.", "'BUFFER_SIZE' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleStopRecording'. Either include it or remove the dependency array.", ["205"], "'handlePlayPause' is assigned a value but never used.", "'handleRewind' is assigned a value but never used.", "'handleForward' is assigned a value but never used.", "'setCenter' is assigned a value but never used.", "'setZoom' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchDefectData' and 'fetchUsers'. Either include them or remove the dependency array.", ["206"], {"desc": "207", "fix": "208"}, {"desc": "209", "fix": "210"}, {"desc": "211", "fix": "212"}, {"desc": "213", "fix": "214"}, {"desc": "215", "fix": "216"}, {"desc": "217", "fix": "218"}, "Update the dependencies array to be: [batchResults.length, roadClassificationEnabled]", {"range": "219", "text": "220"}, "Update the dependencies array to be: [cameraActive, handleLocationRequest, locationPermission]", {"range": "221", "text": "222"}, "Update the dependencies array to be: [cameraActive, coordinates, handleLocationRequest]", {"range": "223", "text": "224"}, "Update the dependencies array to be: [fetchData]", {"range": "225", "text": "226"}, "Update the dependencies array to be: [handleStopRecording, isRecording]", {"range": "227", "text": "228"}, "Update the dependencies array to be: [fetchDefectData, fetchUsers, user]", {"range": "229", "text": "230"}, [31063, 31090], "[batchResults.length, roadClassificationEnabled]", [31326, 31340], "[cameraActive, handleLocationRequest, locationPermission]", [32547, 32574], "[cameraActive, coordinates, handleLocationRequest]", [29534, 29536], "[fetchData]", [3981, 3994], "[handleStopRecording, isRecording]", [5311, 5317], "[fetchDefectData, fetchUsers, user]"]