{"ast": null, "code": "/**\n * Client-side File Validation Utility\n * \n * This module provides comprehensive file validation for image and video uploads\n * with user-friendly error messages that match the backend validation.\n */// Allowed file types configuration\nconst ALLOWED_IMAGE_EXTENSIONS=['.jpg','.jpeg','.png','.gif','.bmp','.tiff','.tif','.webp','.avif'];const ALLOWED_VIDEO_EXTENSIONS=['.mp4','.avi','.mov','.mkv','.wmv','.flv','.webm','.m4v','.3gp','.ogv'];const ALLOWED_IMAGE_MIMES=['image/jpeg','image/jpg','image/png','image/gif','image/bmp','image/tiff','image/tif','image/webp','image/avif'];const ALLOWED_VIDEO_MIMES=['video/mp4','video/avi','video/quicktime','video/x-msvideo','video/x-matroska','video/x-ms-wmv','video/x-flv','video/webm','video/x-m4v','video/3gpp','video/ogg'];// File size limits (in bytes)\nconst MAX_IMAGE_SIZE=50*1024*1024;// 50MB\nconst MAX_VIDEO_SIZE=500*1024*1024;// 500MB\n/**\n * Get file extension from filename in lowercase\n * @param {string} filename - The filename\n * @returns {string} File extension in lowercase (including the dot)\n */export const getFileExtension=filename=>{if(!filename)return\"\";const lastDotIndex=filename.lastIndexOf('.');return lastDotIndex!==-1?filename.slice(lastDotIndex).toLowerCase():\"\";};/**\n * Validate image file\n * @param {File} file - The file object to validate\n * @param {string} context - Additional context for error messages\n * @returns {Object} {isValid: boolean, errorMessage: string}\n */export const validateImageFile=function(file){let context=arguments.length>1&&arguments[1]!==undefined?arguments[1]:\"\";try{if(!file){return{isValid:false,errorMessage:\"No image file provided.\"};}// Check file extension\nconst fileExtension=getFileExtension(file.name);if(!ALLOWED_IMAGE_EXTENSIONS.includes(fileExtension)){return{isValid:false,errorMessage:`Invalid file format. Please upload only image files (.jpg, .jpeg, .png, .gif, .bmp, .tiff, .webp, .avif). Received: ${fileExtension||'unknown'}`};}// Check MIME type\nif(file.type&&!ALLOWED_IMAGE_MIMES.includes(file.type.toLowerCase())){return{isValid:false,errorMessage:`Invalid file format. Please upload only image files. The file appears to be: ${file.type}`};}// Check file size\nif(file.size>MAX_IMAGE_SIZE){return{isValid:false,errorMessage:`Image file size (${(file.size/(1024*1024)).toFixed(1)}MB) exceeds maximum allowed size (${MAX_IMAGE_SIZE/(1024*1024)}MB). Please compress your image or choose a smaller file.`};}return{isValid:true,errorMessage:\"\"};}catch(error){console.error(\"Error during image validation:\",error);return{isValid:false,errorMessage:\"An error occurred while validating the image file. Please try again with a different file.\"};}};/**\n * Validate video file\n * @param {File} file - The file object to validate\n * @param {string} context - Additional context for error messages\n * @returns {Object} {isValid: boolean, errorMessage: string}\n */export const validateVideoFile=function(file){let context=arguments.length>1&&arguments[1]!==undefined?arguments[1]:\"\";try{if(!file){return{isValid:false,errorMessage:\"No video file provided.\"};}// Check file extension\nconst fileExtension=getFileExtension(file.name);if(!ALLOWED_VIDEO_EXTENSIONS.includes(fileExtension)){return{isValid:false,errorMessage:`Invalid file format. Please upload only video files (.mp4, .avi, .mov, .mkv, .wmv, .flv, .webm, .m4v, .3gp, .ogv). Received: ${fileExtension||'unknown'}`};}// Check MIME type\nif(file.type&&!ALLOWED_VIDEO_MIMES.includes(file.type.toLowerCase())){return{isValid:false,errorMessage:`Invalid file format. Please upload only video files. The file appears to be: ${file.type}`};}// Check file size\nif(file.size>MAX_VIDEO_SIZE){return{isValid:false,errorMessage:`Video file size (${(file.size/(1024*1024)).toFixed(1)}MB) exceeds maximum allowed size (${MAX_VIDEO_SIZE/(1024*1024)}MB). Please compress your video or choose a smaller file.`};}return{isValid:true,errorMessage:\"\"};}catch(error){console.error(\"Error during video validation:\",error);return{isValid:false,errorMessage:\"An error occurred while validating the video file. Please try again with a different file.\"};}};/**\n * Main validation function for uploaded files\n * @param {File} file - The file object to validate\n * @param {string} expectedType - Expected file type ('image' or 'video')\n * @param {string} context - Additional context for error messages\n * @returns {Object} {isValid: boolean, errorMessage: string}\n */export const validateUploadFile=function(file,expectedType){let context=arguments.length>2&&arguments[2]!==undefined?arguments[2]:\"\";try{if(expectedType==='image'){return validateImageFile(file,context);}else if(expectedType==='video'){return validateVideoFile(file,context);}else{return{isValid:false,errorMessage:`Invalid expected file type: ${expectedType}. Must be 'image' or 'video'.`};}}catch(error){console.error(\"Error during file validation:\",error);return{isValid:false,errorMessage:\"An error occurred while validating the file. Please try again.\"};}};/**\n * Validate multiple files\n * @param {FileList|Array} files - The files to validate\n * @param {string} expectedType - Expected file type ('image' or 'video')\n * @param {string} context - Additional context for error messages\n * @returns {Object} {isValid: boolean, errorMessage: string, invalidFiles: Array}\n */export const validateMultipleFiles=function(files,expectedType){let context=arguments.length>2&&arguments[2]!==undefined?arguments[2]:\"\";const filesArray=Array.from(files);const invalidFiles=[];for(let i=0;i<filesArray.length;i++){const file=filesArray[i];const validation=validateUploadFile(file,expectedType,context);if(!validation.isValid){invalidFiles.push({file:file,error:validation.errorMessage});}}if(invalidFiles.length>0){const firstError=invalidFiles[0].error;const errorMessage=invalidFiles.length===1?`File \"${invalidFiles[0].file.name}\": ${firstError}`:`${invalidFiles.length} file(s) have validation errors. First error - \"${invalidFiles[0].file.name}\": ${firstError}`;return{isValid:false,errorMessage:errorMessage,invalidFiles:invalidFiles};}return{isValid:true,errorMessage:\"\",invalidFiles:[]};};/**\n * Get context-specific error message for file validation\n * @param {string} expectedType - Expected file type ('image' or 'video')\n * @param {string} context - Additional context\n * @returns {string} Context-specific error message\n */export const getContextSpecificErrorMessage=function(expectedType){let context=arguments.length>1&&arguments[1]!==undefined?arguments[1]:\"\";if(expectedType==='image'){if(context.toLowerCase().includes('pothole')){return\"Invalid file format. Please upload only image files for pothole detection (.jpg, .jpeg, .png, .gif, .bmp, .tiff, .webp, .avif).\";}else if(context.toLowerCase().includes('crack')){return\"Invalid file format. Please upload only image files for crack detection (.jpg, .jpeg, .png, .gif, .bmp, .tiff, .webp, .avif).\";}else if(context.toLowerCase().includes('kerb')){return\"Invalid file format. Please upload only image files for kerb detection (.jpg, .jpeg, .png, .gif, .bmp, .tiff, .webp, .avif).\";}else{return\"Invalid file format. Please upload only image files (.jpg, .jpeg, .png, .gif, .bmp, .tiff, .webp, .avif).\";}}else if(expectedType==='video'){return\"Invalid file format. Please upload only video files (.mp4, .avi, .mov, .mkv, .wmv, .flv, .webm, .m4v, .3gp, .ogv).\";}else{return\"Invalid file format. Please upload only image or video files.\";}};/**\n * Show user-friendly file validation error\n * @param {string} errorMessage - The error message to display\n * @param {Function} setErrorCallback - Callback function to set error state\n */export const showFileValidationError=(errorMessage,setErrorCallback)=>{if(setErrorCallback){setErrorCallback(errorMessage);}else{// Fallback to alert if no callback provided\nalert(errorMessage);}};// Export constants for use in components\nexport{ALLOWED_IMAGE_EXTENSIONS,ALLOWED_VIDEO_EXTENSIONS,ALLOWED_IMAGE_MIMES,ALLOWED_VIDEO_MIMES,MAX_IMAGE_SIZE,MAX_VIDEO_SIZE};", "map": {"version": 3, "names": ["ALLOWED_IMAGE_EXTENSIONS", "ALLOWED_VIDEO_EXTENSIONS", "ALLOWED_IMAGE_MIMES", "ALLOWED_VIDEO_MIMES", "MAX_IMAGE_SIZE", "MAX_VIDEO_SIZE", "getFileExtension", "filename", "lastDotIndex", "lastIndexOf", "slice", "toLowerCase", "validateImageFile", "file", "context", "arguments", "length", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "errorMessage", "fileExtension", "name", "includes", "type", "size", "toFixed", "error", "console", "validateVideoFile", "validateUploadFile", "expectedType", "validateMultipleFiles", "files", "filesArray", "Array", "from", "invalidFiles", "i", "validation", "push", "firstError", "getContextSpecificErrorMessage", "showFileValidationError", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alert"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/utils/fileValidation.js"], "sourcesContent": ["/**\n * Client-side File Validation Utility\n * \n * This module provides comprehensive file validation for image and video uploads\n * with user-friendly error messages that match the backend validation.\n */\n\n// Allowed file types configuration\nconst ALLOWED_IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp', '.avif'];\nconst ALLOWED_VIDEO_EXTENSIONS = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.ogv'];\n\nconst ALLOWED_IMAGE_MIMES = [\n  'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', \n  'image/tiff', 'image/tif', 'image/webp', 'image/avif'\n];\n\nconst ALLOWED_VIDEO_MIMES = [\n  'video/mp4', 'video/avi', 'video/quicktime', 'video/x-msvideo', \n  'video/x-matroska', 'video/x-ms-wmv', 'video/x-flv', 'video/webm',\n  'video/x-m4v', 'video/3gpp', 'video/ogg'\n];\n\n// File size limits (in bytes)\nconst MAX_IMAGE_SIZE = 50 * 1024 * 1024; // 50MB\nconst MAX_VIDEO_SIZE = 500 * 1024 * 1024; // 500MB\n\n/**\n * Get file extension from filename in lowercase\n * @param {string} filename - The filename\n * @returns {string} File extension in lowercase (including the dot)\n */\nexport const getFileExtension = (filename) => {\n  if (!filename) return \"\";\n  const lastDotIndex = filename.lastIndexOf('.');\n  return lastDotIndex !== -1 ? filename.slice(lastDotIndex).toLowerCase() : \"\";\n};\n\n/**\n * Validate image file\n * @param {File} file - The file object to validate\n * @param {string} context - Additional context for error messages\n * @returns {Object} {isValid: boolean, errorMessage: string}\n */\nexport const validateImageFile = (file, context = \"\") => {\n  try {\n    if (!file) {\n      return { isValid: false, errorMessage: \"No image file provided.\" };\n    }\n\n    // Check file extension\n    const fileExtension = getFileExtension(file.name);\n    if (!ALLOWED_IMAGE_EXTENSIONS.includes(fileExtension)) {\n      return {\n        isValid: false,\n        errorMessage: `Invalid file format. Please upload only image files (.jpg, .jpeg, .png, .gif, .bmp, .tiff, .webp, .avif). Received: ${fileExtension || 'unknown'}`\n      };\n    }\n\n    // Check MIME type\n    if (file.type && !ALLOWED_IMAGE_MIMES.includes(file.type.toLowerCase())) {\n      return {\n        isValid: false,\n        errorMessage: `Invalid file format. Please upload only image files. The file appears to be: ${file.type}`\n      };\n    }\n\n    // Check file size\n    if (file.size > MAX_IMAGE_SIZE) {\n      return {\n        isValid: false,\n        errorMessage: `Image file size (${(file.size / (1024*1024)).toFixed(1)}MB) exceeds maximum allowed size (${MAX_IMAGE_SIZE / (1024*1024)}MB). Please compress your image or choose a smaller file.`\n      };\n    }\n\n    return { isValid: true, errorMessage: \"\" };\n\n  } catch (error) {\n    console.error(\"Error during image validation:\", error);\n    return {\n      isValid: false,\n      errorMessage: \"An error occurred while validating the image file. Please try again with a different file.\"\n    };\n  }\n};\n\n/**\n * Validate video file\n * @param {File} file - The file object to validate\n * @param {string} context - Additional context for error messages\n * @returns {Object} {isValid: boolean, errorMessage: string}\n */\nexport const validateVideoFile = (file, context = \"\") => {\n  try {\n    if (!file) {\n      return { isValid: false, errorMessage: \"No video file provided.\" };\n    }\n\n    // Check file extension\n    const fileExtension = getFileExtension(file.name);\n    if (!ALLOWED_VIDEO_EXTENSIONS.includes(fileExtension)) {\n      return {\n        isValid: false,\n        errorMessage: `Invalid file format. Please upload only video files (.mp4, .avi, .mov, .mkv, .wmv, .flv, .webm, .m4v, .3gp, .ogv). Received: ${fileExtension || 'unknown'}`\n      };\n    }\n\n    // Check MIME type\n    if (file.type && !ALLOWED_VIDEO_MIMES.includes(file.type.toLowerCase())) {\n      return {\n        isValid: false,\n        errorMessage: `Invalid file format. Please upload only video files. The file appears to be: ${file.type}`\n      };\n    }\n\n    // Check file size\n    if (file.size > MAX_VIDEO_SIZE) {\n      return {\n        isValid: false,\n        errorMessage: `Video file size (${(file.size / (1024*1024)).toFixed(1)}MB) exceeds maximum allowed size (${MAX_VIDEO_SIZE / (1024*1024)}MB). Please compress your video or choose a smaller file.`\n      };\n    }\n\n    return { isValid: true, errorMessage: \"\" };\n\n  } catch (error) {\n    console.error(\"Error during video validation:\", error);\n    return {\n      isValid: false,\n      errorMessage: \"An error occurred while validating the video file. Please try again with a different file.\"\n    };\n  }\n};\n\n/**\n * Main validation function for uploaded files\n * @param {File} file - The file object to validate\n * @param {string} expectedType - Expected file type ('image' or 'video')\n * @param {string} context - Additional context for error messages\n * @returns {Object} {isValid: boolean, errorMessage: string}\n */\nexport const validateUploadFile = (file, expectedType, context = \"\") => {\n  try {\n    if (expectedType === 'image') {\n      return validateImageFile(file, context);\n    } else if (expectedType === 'video') {\n      return validateVideoFile(file, context);\n    } else {\n      return {\n        isValid: false,\n        errorMessage: `Invalid expected file type: ${expectedType}. Must be 'image' or 'video'.`\n      };\n    }\n  } catch (error) {\n    console.error(\"Error during file validation:\", error);\n    return {\n      isValid: false,\n      errorMessage: \"An error occurred while validating the file. Please try again.\"\n    };\n  }\n};\n\n/**\n * Validate multiple files\n * @param {FileList|Array} files - The files to validate\n * @param {string} expectedType - Expected file type ('image' or 'video')\n * @param {string} context - Additional context for error messages\n * @returns {Object} {isValid: boolean, errorMessage: string, invalidFiles: Array}\n */\nexport const validateMultipleFiles = (files, expectedType, context = \"\") => {\n  const filesArray = Array.from(files);\n  const invalidFiles = [];\n  \n  for (let i = 0; i < filesArray.length; i++) {\n    const file = filesArray[i];\n    const validation = validateUploadFile(file, expectedType, context);\n    \n    if (!validation.isValid) {\n      invalidFiles.push({\n        file: file,\n        error: validation.errorMessage\n      });\n    }\n  }\n  \n  if (invalidFiles.length > 0) {\n    const firstError = invalidFiles[0].error;\n    const errorMessage = invalidFiles.length === 1 \n      ? `File \"${invalidFiles[0].file.name}\": ${firstError}`\n      : `${invalidFiles.length} file(s) have validation errors. First error - \"${invalidFiles[0].file.name}\": ${firstError}`;\n    \n    return {\n      isValid: false,\n      errorMessage: errorMessage,\n      invalidFiles: invalidFiles\n    };\n  }\n  \n  return {\n    isValid: true,\n    errorMessage: \"\",\n    invalidFiles: []\n  };\n};\n\n/**\n * Get context-specific error message for file validation\n * @param {string} expectedType - Expected file type ('image' or 'video')\n * @param {string} context - Additional context\n * @returns {string} Context-specific error message\n */\nexport const getContextSpecificErrorMessage = (expectedType, context = \"\") => {\n  if (expectedType === 'image') {\n    if (context.toLowerCase().includes('pothole')) {\n      return \"Invalid file format. Please upload only image files for pothole detection (.jpg, .jpeg, .png, .gif, .bmp, .tiff, .webp, .avif).\";\n    } else if (context.toLowerCase().includes('crack')) {\n      return \"Invalid file format. Please upload only image files for crack detection (.jpg, .jpeg, .png, .gif, .bmp, .tiff, .webp, .avif).\";\n    } else if (context.toLowerCase().includes('kerb')) {\n      return \"Invalid file format. Please upload only image files for kerb detection (.jpg, .jpeg, .png, .gif, .bmp, .tiff, .webp, .avif).\";\n    } else {\n      return \"Invalid file format. Please upload only image files (.jpg, .jpeg, .png, .gif, .bmp, .tiff, .webp, .avif).\";\n    }\n  } else if (expectedType === 'video') {\n    return \"Invalid file format. Please upload only video files (.mp4, .avi, .mov, .mkv, .wmv, .flv, .webm, .m4v, .3gp, .ogv).\";\n  } else {\n    return \"Invalid file format. Please upload only image or video files.\";\n  }\n};\n\n/**\n * Show user-friendly file validation error\n * @param {string} errorMessage - The error message to display\n * @param {Function} setErrorCallback - Callback function to set error state\n */\nexport const showFileValidationError = (errorMessage, setErrorCallback) => {\n  if (setErrorCallback) {\n    setErrorCallback(errorMessage);\n  } else {\n    // Fallback to alert if no callback provided\n    alert(errorMessage);\n  }\n};\n\n// Export constants for use in components\nexport {\n  ALLOWED_IMAGE_EXTENSIONS,\n  ALLOWED_VIDEO_EXTENSIONS,\n  ALLOWED_IMAGE_MIMES,\n  ALLOWED_VIDEO_MIMES,\n  MAX_IMAGE_SIZE,\n  MAX_VIDEO_SIZE\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,GAEA;AACA,KAAM,CAAAA,wBAAwB,CAAG,CAAC,MAAM,CAAE,OAAO,CAAE,MAAM,CAAE,MAAM,CAAE,MAAM,CAAE,OAAO,CAAE,MAAM,CAAE,OAAO,CAAE,OAAO,CAAC,CAC7G,KAAM,CAAAC,wBAAwB,CAAG,CAAC,MAAM,CAAE,MAAM,CAAE,MAAM,CAAE,MAAM,CAAE,MAAM,CAAE,MAAM,CAAE,OAAO,CAAE,MAAM,CAAE,MAAM,CAAE,MAAM,CAAC,CAElH,KAAM,CAAAC,mBAAmB,CAAG,CAC1B,YAAY,CAAE,WAAW,CAAE,WAAW,CAAE,WAAW,CAAE,WAAW,CAChE,YAAY,CAAE,WAAW,CAAE,YAAY,CAAE,YAAY,CACtD,CAED,KAAM,CAAAC,mBAAmB,CAAG,CAC1B,WAAW,CAAE,WAAW,CAAE,iBAAiB,CAAE,iBAAiB,CAC9D,kBAAkB,CAAE,gBAAgB,CAAE,aAAa,CAAE,YAAY,CACjE,aAAa,CAAE,YAAY,CAAE,WAAW,CACzC,CAED;AACA,KAAM,CAAAC,cAAc,CAAG,EAAE,CAAG,IAAI,CAAG,IAAI,CAAE;AACzC,KAAM,CAAAC,cAAc,CAAG,GAAG,CAAG,IAAI,CAAG,IAAI,CAAE;AAE1C;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,gBAAgB,CAAIC,QAAQ,EAAK,CAC5C,GAAI,CAACA,QAAQ,CAAE,MAAO,EAAE,CACxB,KAAM,CAAAC,YAAY,CAAGD,QAAQ,CAACE,WAAW,CAAC,GAAG,CAAC,CAC9C,MAAO,CAAAD,YAAY,GAAK,CAAC,CAAC,CAAGD,QAAQ,CAACG,KAAK,CAACF,YAAY,CAAC,CAACG,WAAW,CAAC,CAAC,CAAG,EAAE,CAC9E,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,iBAAiB,CAAG,QAAAA,CAACC,IAAI,CAAmB,IAAjB,CAAAC,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CAClD,GAAI,CACF,GAAI,CAACF,IAAI,CAAE,CACT,MAAO,CAAEK,OAAO,CAAE,KAAK,CAAEC,YAAY,CAAE,yBAA0B,CAAC,CACpE,CAEA;AACA,KAAM,CAAAC,aAAa,CAAGd,gBAAgB,CAACO,IAAI,CAACQ,IAAI,CAAC,CACjD,GAAI,CAACrB,wBAAwB,CAACsB,QAAQ,CAACF,aAAa,CAAC,CAAE,CACrD,MAAO,CACLF,OAAO,CAAE,KAAK,CACdC,YAAY,CAAE,uHAAuHC,aAAa,EAAI,SAAS,EACjK,CAAC,CACH,CAEA;AACA,GAAIP,IAAI,CAACU,IAAI,EAAI,CAACrB,mBAAmB,CAACoB,QAAQ,CAACT,IAAI,CAACU,IAAI,CAACZ,WAAW,CAAC,CAAC,CAAC,CAAE,CACvE,MAAO,CACLO,OAAO,CAAE,KAAK,CACdC,YAAY,CAAE,gFAAgFN,IAAI,CAACU,IAAI,EACzG,CAAC,CACH,CAEA;AACA,GAAIV,IAAI,CAACW,IAAI,CAAGpB,cAAc,CAAE,CAC9B,MAAO,CACLc,OAAO,CAAE,KAAK,CACdC,YAAY,CAAE,oBAAoB,CAACN,IAAI,CAACW,IAAI,EAAI,IAAI,CAAC,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,qCAAqCrB,cAAc,EAAI,IAAI,CAAC,IAAI,CAAC,2DACzI,CAAC,CACH,CAEA,MAAO,CAAEc,OAAO,CAAE,IAAI,CAAEC,YAAY,CAAE,EAAG,CAAC,CAE5C,CAAE,MAAOO,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtD,MAAO,CACLR,OAAO,CAAE,KAAK,CACdC,YAAY,CAAE,4FAChB,CAAC,CACH,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAS,iBAAiB,CAAG,QAAAA,CAACf,IAAI,CAAmB,IAAjB,CAAAC,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CAClD,GAAI,CACF,GAAI,CAACF,IAAI,CAAE,CACT,MAAO,CAAEK,OAAO,CAAE,KAAK,CAAEC,YAAY,CAAE,yBAA0B,CAAC,CACpE,CAEA;AACA,KAAM,CAAAC,aAAa,CAAGd,gBAAgB,CAACO,IAAI,CAACQ,IAAI,CAAC,CACjD,GAAI,CAACpB,wBAAwB,CAACqB,QAAQ,CAACF,aAAa,CAAC,CAAE,CACrD,MAAO,CACLF,OAAO,CAAE,KAAK,CACdC,YAAY,CAAE,gIAAgIC,aAAa,EAAI,SAAS,EAC1K,CAAC,CACH,CAEA;AACA,GAAIP,IAAI,CAACU,IAAI,EAAI,CAACpB,mBAAmB,CAACmB,QAAQ,CAACT,IAAI,CAACU,IAAI,CAACZ,WAAW,CAAC,CAAC,CAAC,CAAE,CACvE,MAAO,CACLO,OAAO,CAAE,KAAK,CACdC,YAAY,CAAE,gFAAgFN,IAAI,CAACU,IAAI,EACzG,CAAC,CACH,CAEA;AACA,GAAIV,IAAI,CAACW,IAAI,CAAGnB,cAAc,CAAE,CAC9B,MAAO,CACLa,OAAO,CAAE,KAAK,CACdC,YAAY,CAAE,oBAAoB,CAACN,IAAI,CAACW,IAAI,EAAI,IAAI,CAAC,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,qCAAqCpB,cAAc,EAAI,IAAI,CAAC,IAAI,CAAC,2DACzI,CAAC,CACH,CAEA,MAAO,CAAEa,OAAO,CAAE,IAAI,CAAEC,YAAY,CAAE,EAAG,CAAC,CAE5C,CAAE,MAAOO,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtD,MAAO,CACLR,OAAO,CAAE,KAAK,CACdC,YAAY,CAAE,4FAChB,CAAC,CACH,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAU,kBAAkB,CAAG,QAAAA,CAAChB,IAAI,CAAEiB,YAAY,CAAmB,IAAjB,CAAAhB,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CACjE,GAAI,CACF,GAAIe,YAAY,GAAK,OAAO,CAAE,CAC5B,MAAO,CAAAlB,iBAAiB,CAACC,IAAI,CAAEC,OAAO,CAAC,CACzC,CAAC,IAAM,IAAIgB,YAAY,GAAK,OAAO,CAAE,CACnC,MAAO,CAAAF,iBAAiB,CAACf,IAAI,CAAEC,OAAO,CAAC,CACzC,CAAC,IAAM,CACL,MAAO,CACLI,OAAO,CAAE,KAAK,CACdC,YAAY,CAAE,+BAA+BW,YAAY,+BAC3D,CAAC,CACH,CACF,CAAE,MAAOJ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD,MAAO,CACLR,OAAO,CAAE,KAAK,CACdC,YAAY,CAAE,gEAChB,CAAC,CACH,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAY,qBAAqB,CAAG,QAAAA,CAACC,KAAK,CAAEF,YAAY,CAAmB,IAAjB,CAAAhB,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CACrE,KAAM,CAAAkB,UAAU,CAAGC,KAAK,CAACC,IAAI,CAACH,KAAK,CAAC,CACpC,KAAM,CAAAI,YAAY,CAAG,EAAE,CAEvB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGJ,UAAU,CAACjB,MAAM,CAAEqB,CAAC,EAAE,CAAE,CAC1C,KAAM,CAAAxB,IAAI,CAAGoB,UAAU,CAACI,CAAC,CAAC,CAC1B,KAAM,CAAAC,UAAU,CAAGT,kBAAkB,CAAChB,IAAI,CAAEiB,YAAY,CAAEhB,OAAO,CAAC,CAElE,GAAI,CAACwB,UAAU,CAACpB,OAAO,CAAE,CACvBkB,YAAY,CAACG,IAAI,CAAC,CAChB1B,IAAI,CAAEA,IAAI,CACVa,KAAK,CAAEY,UAAU,CAACnB,YACpB,CAAC,CAAC,CACJ,CACF,CAEA,GAAIiB,YAAY,CAACpB,MAAM,CAAG,CAAC,CAAE,CAC3B,KAAM,CAAAwB,UAAU,CAAGJ,YAAY,CAAC,CAAC,CAAC,CAACV,KAAK,CACxC,KAAM,CAAAP,YAAY,CAAGiB,YAAY,CAACpB,MAAM,GAAK,CAAC,CAC1C,SAASoB,YAAY,CAAC,CAAC,CAAC,CAACvB,IAAI,CAACQ,IAAI,MAAMmB,UAAU,EAAE,CACpD,GAAGJ,YAAY,CAACpB,MAAM,mDAAmDoB,YAAY,CAAC,CAAC,CAAC,CAACvB,IAAI,CAACQ,IAAI,MAAMmB,UAAU,EAAE,CAExH,MAAO,CACLtB,OAAO,CAAE,KAAK,CACdC,YAAY,CAAEA,YAAY,CAC1BiB,YAAY,CAAEA,YAChB,CAAC,CACH,CAEA,MAAO,CACLlB,OAAO,CAAE,IAAI,CACbC,YAAY,CAAE,EAAE,CAChBiB,YAAY,CAAE,EAChB,CAAC,CACH,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAK,8BAA8B,CAAG,QAAAA,CAACX,YAAY,CAAmB,IAAjB,CAAAhB,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CACvE,GAAIe,YAAY,GAAK,OAAO,CAAE,CAC5B,GAAIhB,OAAO,CAACH,WAAW,CAAC,CAAC,CAACW,QAAQ,CAAC,SAAS,CAAC,CAAE,CAC7C,MAAO,iIAAiI,CAC1I,CAAC,IAAM,IAAIR,OAAO,CAACH,WAAW,CAAC,CAAC,CAACW,QAAQ,CAAC,OAAO,CAAC,CAAE,CAClD,MAAO,+HAA+H,CACxI,CAAC,IAAM,IAAIR,OAAO,CAACH,WAAW,CAAC,CAAC,CAACW,QAAQ,CAAC,MAAM,CAAC,CAAE,CACjD,MAAO,8HAA8H,CACvI,CAAC,IAAM,CACL,MAAO,2GAA2G,CACpH,CACF,CAAC,IAAM,IAAIQ,YAAY,GAAK,OAAO,CAAE,CACnC,MAAO,oHAAoH,CAC7H,CAAC,IAAM,CACL,MAAO,+DAA+D,CACxE,CACF,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAY,uBAAuB,CAAGA,CAACvB,YAAY,CAAEwB,gBAAgB,GAAK,CACzE,GAAIA,gBAAgB,CAAE,CACpBA,gBAAgB,CAACxB,YAAY,CAAC,CAChC,CAAC,IAAM,CACL;AACAyB,KAAK,CAACzB,YAAY,CAAC,CACrB,CACF,CAAC,CAED;AACA,OACEnB,wBAAwB,CACxBC,wBAAwB,CACxBC,mBAAmB,CACnBC,mBAAmB,CACnBC,cAAc,CACdC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}