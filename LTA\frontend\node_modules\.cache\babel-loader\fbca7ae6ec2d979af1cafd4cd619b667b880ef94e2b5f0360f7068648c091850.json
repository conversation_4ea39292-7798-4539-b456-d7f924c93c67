{"ast": null, "code": "import React,{useState,useEffect}from'react';import{use<PERSON><PERSON><PERSON>,<PERSON>}from'react-router-dom';import{Contain<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>ge}from'react-bootstrap';import axios from'axios';import'./dashboard.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";function DefectDetail(){const{imageId}=useParams();const[defectData,setDefectData]=useState(null);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[imageType,setImageType]=useState('processed');// 'original' or 'processed'\nuseEffect(()=>{const fetchDefectDetail=async()=>{try{setLoading(true);const response=await axios.get(`/api/pavement/images/${imageId}`);if(response.data.success){setDefectData(response.data);}else{setError('Failed to load defect details');}setLoading(false);}catch(err){console.error('Error fetching defect details:',err);setError(`Error loading defect details: ${err.message}`);setLoading(false);}};if(imageId){fetchDefectDetail();}},[imageId]);const toggleImageType=()=>{setImageType(prev=>prev==='original'?'processed':'original');};const getDefectTypeLabel=type=>{switch(type){case'pothole':return/*#__PURE__*/_jsx(Badge,{bg:\"danger\",children:\"Pothole\"});case'crack':return/*#__PURE__*/_jsx(Badge,{bg:\"warning\",text:\"dark\",children:\"Crack\"});case'kerb':return/*#__PURE__*/_jsx(Badge,{bg:\"primary\",children:\"Kerb\"});default:return/*#__PURE__*/_jsx(Badge,{bg:\"secondary\",children:type});}};const formatDate=dateString=>{if(!dateString)return'N/A';return new Date(dateString).toLocaleString();};return/*#__PURE__*/_jsxs(Container,{className:\"py-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center mb-4\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Defect Detail\"}),/*#__PURE__*/_jsx(Link,{to:\"/dashboard\",className:\"btn btn-outline-primary\",children:\"Back to Dashboard\"})]}),loading?/*#__PURE__*/_jsx(\"div\",{className:\"text-center py-5\",children:/*#__PURE__*/_jsx(Spinner,{animation:\"border\",role:\"status\",variant:\"primary\",children:/*#__PURE__*/_jsx(\"span\",{className:\"visually-hidden\",children:\"Loading...\"})})}):error?/*#__PURE__*/_jsx(Alert,{variant:\"danger\",children:error}):defectData?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Card,{className:\"shadow-sm mb-4\",children:[/*#__PURE__*/_jsx(Card.Header,{className:\"bg-primary text-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center\",children:[/*#__PURE__*/_jsxs(\"h5\",{className:\"mb-0\",children:[getDefectTypeLabel(defectData.type),\" - ID: \",imageId]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Button,{variant:imageType==='original'?'light':'outline-light',size:\"sm\",className:\"me-2\",onClick:toggleImageType,children:\"Original\"}),/*#__PURE__*/_jsx(Button,{variant:imageType==='processed'?'light':'outline-light',size:\"sm\",onClick:toggleImageType,children:\"Processed\"})]})]})}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:6,className:\"text-center mb-4\",children:defectData.image&&(()=>{// Check if S3 URLs are available (new format)\nconst s3Url=imageType==='original'?defectData.image.original_image_full_url||defectData.image.original_image_s3_url:defectData.image.processed_image_full_url||defectData.image.processed_image_s3_url;const gridfsId=imageType==='original'?defectData.image.original_image_id:defectData.image.processed_image_id;// Use S3 URL if available, otherwise fall back to GridFS\nconst imageSrc=s3Url||(gridfsId?`/api/pavement/get-image/${gridfsId}`:null);return imageSrc?/*#__PURE__*/_jsx(\"div\",{className:\"defect-image-container\",children:/*#__PURE__*/_jsx(\"img\",{src:imageSrc,alt:`${defectData.type} defect`,className:\"img-fluid border rounded shadow-sm\",style:{maxHeight:'400px'},onError:e=>{// If S3 image fails to load and we have GridFS ID, try GridFS as fallback\nif(s3Url&&gridfsId){e.target.src=`/api/pavement/get-image/${gridfsId}`;}}})}):/*#__PURE__*/_jsx(\"div\",{className:\"text-muted\",children:\"No image available\"});})()}),/*#__PURE__*/_jsxs(Col,{md:6,children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Basic Information\"}),/*#__PURE__*/_jsx(\"table\",{className:\"table table-bordered\",children:/*#__PURE__*/_jsxs(\"tbody\",{children:[/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{width:\"40%\",children:\"Type\"}),/*#__PURE__*/_jsx(\"td\",{children:defectData.type})]}),/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Defect Count\"}),/*#__PURE__*/_jsx(\"td\",{children:defectData.image.pothole_count||defectData.image.crack_count||defectData.image.kerb_count||'N/A'})]}),/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Date Detected\"}),/*#__PURE__*/_jsx(\"td\",{children:formatDate(defectData.image.timestamp)})]}),/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Reported By\"}),/*#__PURE__*/_jsx(\"td\",{children:defectData.image.username||'N/A'})]}),/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Role\"}),/*#__PURE__*/_jsx(\"td\",{children:defectData.image.role||'N/A'})]}),/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Coordinates\"}),/*#__PURE__*/_jsx(\"td\",{children:defectData.image.coordinates||'Not Available'})]})]})})]})]}),defectData.type==='pothole'&&defectData.image.potholes&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4\",children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Pothole Details\"}),/*#__PURE__*/_jsx(\"div\",{className:\"table-responsive\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"table table-striped table-bordered\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"table-primary\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Area (cm\\xB2)\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Depth (cm)\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Volume (cm\\xB3)\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Severity\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:defectData.image.potholes.map((pothole,index)=>{var _pothole$area_cm,_pothole$depth_cm,_pothole$volume;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:pothole.pothole_id}),/*#__PURE__*/_jsx(\"td\",{children:((_pothole$area_cm=pothole.area_cm2)===null||_pothole$area_cm===void 0?void 0:_pothole$area_cm.toFixed(2))||'N/A'}),/*#__PURE__*/_jsx(\"td\",{children:((_pothole$depth_cm=pothole.depth_cm)===null||_pothole$depth_cm===void 0?void 0:_pothole$depth_cm.toFixed(2))||'N/A'}),/*#__PURE__*/_jsx(\"td\",{children:((_pothole$volume=pothole.volume)===null||_pothole$volume===void 0?void 0:_pothole$volume.toFixed(2))||'N/A'}),/*#__PURE__*/_jsx(\"td\",{children:pothole.area_cm2>1000?/*#__PURE__*/_jsx(Badge,{bg:\"danger\",children:\"High\"}):pothole.area_cm2>500?/*#__PURE__*/_jsx(Badge,{bg:\"warning\",text:\"dark\",children:\"Medium\"}):/*#__PURE__*/_jsx(Badge,{bg:\"success\",children:\"Low\"})})]},index);})})]})})]}),defectData.type==='crack'&&defectData.image.cracks&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4\",children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Crack Details\"}),/*#__PURE__*/_jsx(\"div\",{className:\"table-responsive\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"table table-striped table-bordered\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"table-primary\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Type\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Area (cm\\xB2)\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Confidence\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:defectData.image.cracks.map((crack,index)=>{var _crack$area_cm;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:crack.crack_id}),/*#__PURE__*/_jsx(\"td\",{children:crack.crack_type}),/*#__PURE__*/_jsx(\"td\",{children:((_crack$area_cm=crack.area_cm2)===null||_crack$area_cm===void 0?void 0:_crack$area_cm.toFixed(2))||'N/A'}),/*#__PURE__*/_jsxs(\"td\",{children:[(crack.confidence*100).toFixed(1),\"%\"]})]},index);})})]})}),defectData.image.type_counts&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3\",children:[/*#__PURE__*/_jsx(\"h6\",{children:\"Crack Type Distribution\"}),/*#__PURE__*/_jsx(\"div\",{className:\"d-flex flex-wrap\",children:Object.entries(defectData.image.type_counts).map(_ref=>{let[type,count]=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:\"me-3 mb-2\",children:[/*#__PURE__*/_jsx(Badge,{bg:\"info\",className:\"me-1\",children:count}),\" \",type]},type);})})]})]}),defectData.type==='kerb'&&defectData.image.kerbs&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4\",children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Kerb Details\"}),/*#__PURE__*/_jsx(\"div\",{className:\"table-responsive\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"table table-striped table-bordered\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"table-primary\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Type\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Condition\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Length (m)\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:defectData.image.kerbs.map((kerb,index)=>{var _kerb$length_m;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:kerb.kerb_id}),/*#__PURE__*/_jsx(\"td\",{children:kerb.kerb_type}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(Badge,{bg:kerb.condition==='Good'?'success':kerb.condition==='Fair'?'warning':'danger',text:kerb.condition==='Fair'?'dark':undefined,children:kerb.condition})}),/*#__PURE__*/_jsx(\"td\",{children:((_kerb$length_m=kerb.length_m)===null||_kerb$length_m===void 0?void 0:_kerb$length_m.toFixed(2))||'N/A'})]},index);})})]})}),defectData.image.condition_counts&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3\",children:[/*#__PURE__*/_jsx(\"h6\",{children:\"Condition Distribution\"}),/*#__PURE__*/_jsx(\"div\",{className:\"d-flex flex-wrap\",children:Object.entries(defectData.image.condition_counts).map(_ref2=>{let[condition,count]=_ref2;return/*#__PURE__*/_jsxs(\"div\",{className:\"me-3 mb-2\",children:[/*#__PURE__*/_jsx(Badge,{bg:condition==='Good'?'success':condition==='Fair'?'warning':'danger',text:condition==='Fair'?'dark':undefined,className:\"me-1\",children:count}),\" \",condition]},condition);})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-4\",children:/*#__PURE__*/_jsxs(Card,{className:\"bg-light\",children:[/*#__PURE__*/_jsx(Card.Header,{children:/*#__PURE__*/_jsx(\"h5\",{className:\"mb-0\",children:\"Recommended Action\"})}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"p\",{children:\"Based on the defect analysis, the following action is recommended:\"}),defectData.type==='pothole'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"Clean out loose material\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Apply tack coat\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Fill with hot mix asphalt\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Compact thoroughly\"})]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Priority:\"}),\" \",defectData.image.potholes&&defectData.image.potholes.length>0&&defectData.image.potholes.some(p=>p.area_cm2>1000)?'High':defectData.image.potholes&&defectData.image.potholes.length>0&&defectData.image.potholes.some(p=>p.area_cm2>500)?'Medium':'Low']})]}),defectData.type==='crack'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"Clean cracks with compressed air\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Apply appropriate crack sealant\"}),defectData.image.type_counts&&defectData.image.type_counts['Alligator Crack']>0&&/*#__PURE__*/_jsx(\"li\",{children:\"Consider section replacement for alligator crack areas\"})]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Priority:\"}),\" \",defectData.image.type_counts&&defectData.image.type_counts['Alligator Crack']>0?'High':'Medium']})]}),defectData.type==='kerb'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"Repair damaged sections\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Realign displaced kerbs\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Replace severely damaged kerbs\"})]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Priority:\"}),\" \",defectData.image.condition_counts&&defectData.image.condition_counts['Poor']>0?'High':defectData.image.condition_counts['Fair']>0?'Medium':'Low']})]})]})]})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"d-flex justify-content-end mt-4\",children:/*#__PURE__*/_jsx(Button,{variant:\"primary\",children:\"Generate Report\"})})]}):/*#__PURE__*/_jsxs(Alert,{variant:\"warning\",children:[\"No defect data found for ID: \",imageId]})]});}export default DefectDetail;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Link", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Spinner", "<PERSON><PERSON>", "Badge", "axios", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "DefectDetail", "imageId", "defectData", "setDefectData", "loading", "setLoading", "error", "setError", "imageType", "setImageType", "fetchDefectDetail", "response", "get", "data", "success", "err", "console", "message", "toggleImageType", "prev", "getDefectTypeLabel", "type", "bg", "children", "text", "formatDate", "dateString", "Date", "toLocaleString", "className", "to", "animation", "role", "variant", "Header", "size", "onClick", "Body", "md", "image", "s3Url", "original_image_full_url", "original_image_s3_url", "processed_image_full_url", "processed_image_s3_url", "gridfsId", "original_image_id", "processed_image_id", "imageSrc", "src", "alt", "style", "maxHeight", "onError", "e", "target", "width", "pothole_count", "crack_count", "kerb_count", "timestamp", "username", "coordinates", "potholes", "map", "pothole", "index", "_pothole$area_cm", "_pothole$depth_cm", "_pothole$volume", "pothole_id", "area_cm2", "toFixed", "depth_cm", "volume", "cracks", "crack", "_crack$area_cm", "crack_id", "crack_type", "confidence", "type_counts", "Object", "entries", "_ref", "count", "kerbs", "kerb", "_kerb$length_m", "kerb_id", "kerb_type", "condition", "undefined", "length_m", "condition_counts", "_ref2", "length", "some", "p"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/pages/DefectDetail.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';\r\nimport { Contain<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Spin<PERSON>, <PERSON><PERSON>, <PERSON>ge } from 'react-bootstrap';\r\nimport axios from 'axios';\r\nimport './dashboard.css';\r\n\r\nfunction DefectDetail() {\r\n  const { imageId } = useParams();\r\n  const [defectData, setDefectData] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [imageType, setImageType] = useState('processed'); // 'original' or 'processed'\r\n\r\n  useEffect(() => {\r\n    const fetchDefectDetail = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await axios.get(`/api/pavement/images/${imageId}`);\r\n        \r\n        if (response.data.success) {\r\n          setDefectData(response.data);\r\n        } else {\r\n          setError('Failed to load defect details');\r\n        }\r\n        setLoading(false);\r\n      } catch (err) {\r\n        console.error('Error fetching defect details:', err);\r\n        setError(`Error loading defect details: ${err.message}`);\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    if (imageId) {\r\n      fetchDefectDetail();\r\n    }\r\n  }, [imageId]);\r\n\r\n  const toggleImageType = () => {\r\n    setImageType(prev => prev === 'original' ? 'processed' : 'original');\r\n  };\r\n\r\n  const getDefectTypeLabel = (type) => {\r\n    switch (type) {\r\n      case 'pothole':\r\n        return <Badge bg=\"danger\">Pothole</Badge>;\r\n      case 'crack':\r\n        return <Badge bg=\"warning\" text=\"dark\">Crack</Badge>;\r\n      case 'kerb':\r\n        return <Badge bg=\"primary\">Kerb</Badge>;\r\n      default:\r\n        return <Badge bg=\"secondary\">{type}</Badge>;\r\n    }\r\n  };\r\n\r\n  const formatDate = (dateString) => {\r\n    if (!dateString) return 'N/A';\r\n    return new Date(dateString).toLocaleString();\r\n  };\r\n\r\n  return (\r\n    <Container className=\"py-4\">\r\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n        <h2>Defect Detail</h2>\r\n        <Link to=\"/dashboard\" className=\"btn btn-outline-primary\">\r\n          Back to Dashboard\r\n        </Link>\r\n      </div>\r\n\r\n      {loading ? (\r\n        <div className=\"text-center py-5\">\r\n          <Spinner animation=\"border\" role=\"status\" variant=\"primary\">\r\n            <span className=\"visually-hidden\">Loading...</span>\r\n          </Spinner>\r\n        </div>\r\n      ) : error ? (\r\n        <Alert variant=\"danger\">{error}</Alert>\r\n      ) : defectData ? (\r\n        <>\r\n          <Card className=\"shadow-sm mb-4\">\r\n            <Card.Header className=\"bg-primary text-white\">\r\n              <div className=\"d-flex justify-content-between align-items-center\">\r\n                <h5 className=\"mb-0\">\r\n                  {getDefectTypeLabel(defectData.type)} - ID: {imageId}\r\n                </h5>\r\n                <div>\r\n                  <Button \r\n                    variant={imageType === 'original' ? 'light' : 'outline-light'} \r\n                    size=\"sm\" \r\n                    className=\"me-2\"\r\n                    onClick={toggleImageType}\r\n                  >\r\n                    Original\r\n                  </Button>\r\n                  <Button \r\n                    variant={imageType === 'processed' ? 'light' : 'outline-light'} \r\n                    size=\"sm\"\r\n                    onClick={toggleImageType}\r\n                  >\r\n                    Processed\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            </Card.Header>\r\n            <Card.Body>\r\n              <Row>\r\n                <Col md={6} className=\"text-center mb-4\">\r\n                  {defectData.image && (() => {\r\n                    // Check if S3 URLs are available (new format)\r\n                    const s3Url = imageType === 'original'\r\n                      ? (defectData.image.original_image_full_url || defectData.image.original_image_s3_url)\r\n                      : (defectData.image.processed_image_full_url || defectData.image.processed_image_s3_url);\r\n\r\n                    const gridfsId = imageType === 'original'\r\n                      ? defectData.image.original_image_id\r\n                      : defectData.image.processed_image_id;\r\n\r\n                    // Use S3 URL if available, otherwise fall back to GridFS\r\n                    const imageSrc = s3Url || (gridfsId ? `/api/pavement/get-image/${gridfsId}` : null);\r\n\r\n                    return imageSrc ? (\r\n                      <div className=\"defect-image-container\">\r\n                        <img\r\n                          src={imageSrc}\r\n                          alt={`${defectData.type} defect`}\r\n                          className=\"img-fluid border rounded shadow-sm\"\r\n                          style={{ maxHeight: '400px' }}\r\n                          onError={(e) => {\r\n                            // If S3 image fails to load and we have GridFS ID, try GridFS as fallback\r\n                            if (s3Url && gridfsId) {\r\n                              e.target.src = `/api/pavement/get-image/${gridfsId}`;\r\n                            }\r\n                          }}\r\n                        />\r\n                      </div>\r\n                    ) : (\r\n                      <div className=\"text-muted\">No image available</div>\r\n                    );\r\n                  })()}\r\n                </Col>\r\n                <Col md={6}>\r\n                  <h5>Basic Information</h5>\r\n                  <table className=\"table table-bordered\">\r\n                    <tbody>\r\n                      <tr>\r\n                        <th width=\"40%\">Type</th>\r\n                        <td>{defectData.type}</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th>Defect Count</th>\r\n                        <td>\r\n                          {defectData.image.pothole_count || \r\n                           defectData.image.crack_count || \r\n                           defectData.image.kerb_count || 'N/A'}\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th>Date Detected</th>\r\n                        <td>{formatDate(defectData.image.timestamp)}</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th>Reported By</th>\r\n                        <td>{defectData.image.username || 'N/A'}</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th>Role</th>\r\n                        <td>{defectData.image.role || 'N/A'}</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th>Coordinates</th>\r\n                        <td>{defectData.image.coordinates || 'Not Available'}</td>\r\n                      </tr>\r\n                    </tbody>\r\n                  </table>\r\n                </Col>\r\n              </Row>\r\n\r\n              {defectData.type === 'pothole' && defectData.image.potholes && (\r\n                <div className=\"mt-4\">\r\n                  <h5>Pothole Details</h5>\r\n                  <div className=\"table-responsive\">\r\n                    <table className=\"table table-striped table-bordered\">\r\n                      <thead className=\"table-primary\">\r\n                        <tr>\r\n                          <th>ID</th>\r\n                          <th>Area (cm²)</th>\r\n                          <th>Depth (cm)</th>\r\n                          <th>Volume (cm³)</th>\r\n                          <th>Severity</th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody>\r\n                        {defectData.image.potholes.map((pothole, index) => (\r\n                          <tr key={index}>\r\n                            <td>{pothole.pothole_id}</td>\r\n                            <td>{pothole.area_cm2?.toFixed(2) || 'N/A'}</td>\r\n                            <td>{pothole.depth_cm?.toFixed(2) || 'N/A'}</td>\r\n                            <td>{pothole.volume?.toFixed(2) || 'N/A'}</td>\r\n                            <td>\r\n                              {pothole.area_cm2 > 1000 ? (\r\n                                <Badge bg=\"danger\">High</Badge>\r\n                              ) : pothole.area_cm2 > 500 ? (\r\n                                <Badge bg=\"warning\" text=\"dark\">Medium</Badge>\r\n                              ) : (\r\n                                <Badge bg=\"success\">Low</Badge>\r\n                              )}\r\n                            </td>\r\n                          </tr>\r\n                        ))}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {defectData.type === 'crack' && defectData.image.cracks && (\r\n                <div className=\"mt-4\">\r\n                  <h5>Crack Details</h5>\r\n                  <div className=\"table-responsive\">\r\n                    <table className=\"table table-striped table-bordered\">\r\n                      <thead className=\"table-primary\">\r\n                        <tr>\r\n                          <th>ID</th>\r\n                          <th>Type</th>\r\n                          <th>Area (cm²)</th>\r\n                          <th>Confidence</th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody>\r\n                        {defectData.image.cracks.map((crack, index) => (\r\n                          <tr key={index}>\r\n                            <td>{crack.crack_id}</td>\r\n                            <td>{crack.crack_type}</td>\r\n                            <td>{crack.area_cm2?.toFixed(2) || 'N/A'}</td>\r\n                            <td>{(crack.confidence * 100).toFixed(1)}%</td>\r\n                          </tr>\r\n                        ))}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                  \r\n                  {defectData.image.type_counts && (\r\n                    <div className=\"mt-3\">\r\n                      <h6>Crack Type Distribution</h6>\r\n                      <div className=\"d-flex flex-wrap\">\r\n                        {Object.entries(defectData.image.type_counts).map(([type, count]) => (\r\n                          <div key={type} className=\"me-3 mb-2\">\r\n                            <Badge bg=\"info\" className=\"me-1\">{count}</Badge> {type}\r\n                          </div>\r\n                        ))}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {defectData.type === 'kerb' && defectData.image.kerbs && (\r\n                <div className=\"mt-4\">\r\n                  <h5>Kerb Details</h5>\r\n                  <div className=\"table-responsive\">\r\n                    <table className=\"table table-striped table-bordered\">\r\n                      <thead className=\"table-primary\">\r\n                        <tr>\r\n                          <th>ID</th>\r\n                          <th>Type</th>\r\n                          <th>Condition</th>\r\n                          <th>Length (m)</th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody>\r\n                        {defectData.image.kerbs.map((kerb, index) => (\r\n                          <tr key={index}>\r\n                            <td>{kerb.kerb_id}</td>\r\n                            <td>{kerb.kerb_type}</td>\r\n                            <td>\r\n                              <Badge \r\n                                bg={\r\n                                  kerb.condition === 'Good' ? 'success' :\r\n                                  kerb.condition === 'Fair' ? 'warning' : 'danger'\r\n                                }\r\n                                text={kerb.condition === 'Fair' ? 'dark' : undefined}\r\n                              >\r\n                                {kerb.condition}\r\n                              </Badge>\r\n                            </td>\r\n                            <td>{kerb.length_m?.toFixed(2) || 'N/A'}</td>\r\n                          </tr>\r\n                        ))}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                  \r\n                  {defectData.image.condition_counts && (\r\n                    <div className=\"mt-3\">\r\n                      <h6>Condition Distribution</h6>\r\n                      <div className=\"d-flex flex-wrap\">\r\n                        {Object.entries(defectData.image.condition_counts).map(([condition, count]) => (\r\n                          <div key={condition} className=\"me-3 mb-2\">\r\n                            <Badge \r\n                              bg={\r\n                                condition === 'Good' ? 'success' :\r\n                                condition === 'Fair' ? 'warning' : 'danger'\r\n                              }\r\n                              text={condition === 'Fair' ? 'dark' : undefined}\r\n                              className=\"me-1\"\r\n                            >\r\n                              {count}\r\n                            </Badge> {condition}\r\n                          </div>\r\n                        ))}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {/* Recommendation section - if available */}\r\n              <div className=\"mt-4\">\r\n                <Card className=\"bg-light\">\r\n                  <Card.Header>\r\n                    <h5 className=\"mb-0\">Recommended Action</h5>\r\n                  </Card.Header>\r\n                  <Card.Body>\r\n                    <p>Based on the defect analysis, the following action is recommended:</p>\r\n                    {defectData.type === 'pothole' && (\r\n                      <div>\r\n                        <ul>\r\n                          <li>Clean out loose material</li>\r\n                          <li>Apply tack coat</li>\r\n                          <li>Fill with hot mix asphalt</li>\r\n                          <li>Compact thoroughly</li>\r\n                        </ul>\r\n                        <p><strong>Priority:</strong> {\r\n                          defectData.image.potholes && defectData.image.potholes.length > 0 && \r\n                          defectData.image.potholes.some(p => p.area_cm2 > 1000) ? \r\n                          'High' : defectData.image.potholes && defectData.image.potholes.length > 0 && \r\n                          defectData.image.potholes.some(p => p.area_cm2 > 500) ? \r\n                          'Medium' : 'Low'\r\n                        }</p>\r\n                      </div>\r\n                    )}\r\n                    \r\n                    {defectData.type === 'crack' && (\r\n                      <div>\r\n                        <ul>\r\n                          <li>Clean cracks with compressed air</li>\r\n                          <li>Apply appropriate crack sealant</li>\r\n                          {defectData.image.type_counts && \r\n                           defectData.image.type_counts['Alligator Crack'] > 0 && (\r\n                            <li>Consider section replacement for alligator crack areas</li>\r\n                          )}\r\n                        </ul>\r\n                        <p><strong>Priority:</strong> {\r\n                          defectData.image.type_counts && \r\n                          defectData.image.type_counts['Alligator Crack'] > 0 ? \r\n                          'High' : 'Medium'\r\n                        }</p>\r\n                      </div>\r\n                    )}\r\n                    \r\n                    {defectData.type === 'kerb' && (\r\n                      <div>\r\n                        <ul>\r\n                          <li>Repair damaged sections</li>\r\n                          <li>Realign displaced kerbs</li>\r\n                          <li>Replace severely damaged kerbs</li>\r\n                        </ul>\r\n                        <p><strong>Priority:</strong> {\r\n                          defectData.image.condition_counts && \r\n                          defectData.image.condition_counts['Poor'] > 0 ? \r\n                          'High' : defectData.image.condition_counts['Fair'] > 0 ? \r\n                          'Medium' : 'Low'\r\n                        }</p>\r\n                      </div>\r\n                    )}\r\n                  </Card.Body>\r\n                </Card>\r\n              </div>\r\n            </Card.Body>\r\n          </Card>\r\n\r\n          <div className=\"d-flex justify-content-end mt-4\">\r\n            <Button variant=\"primary\">\r\n              Generate Report\r\n            </Button>\r\n          </div>\r\n        </>\r\n      ) : (\r\n        <Alert variant=\"warning\">No defect data found for ID: {imageId}</Alert>\r\n      )}\r\n    </Container>\r\n  );\r\n}\r\n\r\nexport default DefectDetail; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,IAAI,KAAQ,kBAAkB,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,MAAM,CAAEC,OAAO,CAAEC,KAAK,CAAEC,KAAK,KAAQ,iBAAiB,CAC1F,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEzB,QAAS,CAAAC,YAAYA,CAAA,CAAG,CACtB,KAAM,CAAEC,OAAQ,CAAC,CAAGlB,SAAS,CAAC,CAAC,CAC/B,KAAM,CAACmB,UAAU,CAAEC,aAAa,CAAC,CAAGtB,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAACuB,OAAO,CAAEC,UAAU,CAAC,CAAGxB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACyB,KAAK,CAAEC,QAAQ,CAAC,CAAG1B,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAAC2B,SAAS,CAAEC,YAAY,CAAC,CAAG5B,QAAQ,CAAC,WAAW,CAAC,CAAE;AAEzDC,SAAS,CAAC,IAAM,CACd,KAAM,CAAA4B,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACFL,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAM,QAAQ,CAAG,KAAM,CAAAlB,KAAK,CAACmB,GAAG,CAAC,wBAAwBX,OAAO,EAAE,CAAC,CAEnE,GAAIU,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAE,CACzBX,aAAa,CAACQ,QAAQ,CAACE,IAAI,CAAC,CAC9B,CAAC,IAAM,CACLN,QAAQ,CAAC,+BAA+B,CAAC,CAC3C,CACAF,UAAU,CAAC,KAAK,CAAC,CACnB,CAAE,MAAOU,GAAG,CAAE,CACZC,OAAO,CAACV,KAAK,CAAC,gCAAgC,CAAES,GAAG,CAAC,CACpDR,QAAQ,CAAC,iCAAiCQ,GAAG,CAACE,OAAO,EAAE,CAAC,CACxDZ,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,GAAIJ,OAAO,CAAE,CACXS,iBAAiB,CAAC,CAAC,CACrB,CACF,CAAC,CAAE,CAACT,OAAO,CAAC,CAAC,CAEb,KAAM,CAAAiB,eAAe,CAAGA,CAAA,GAAM,CAC5BT,YAAY,CAACU,IAAI,EAAIA,IAAI,GAAK,UAAU,CAAG,WAAW,CAAG,UAAU,CAAC,CACtE,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAIC,IAAI,EAAK,CACnC,OAAQA,IAAI,EACV,IAAK,SAAS,CACZ,mBAAO1B,IAAA,CAACH,KAAK,EAAC8B,EAAE,CAAC,QAAQ,CAAAC,QAAA,CAAC,SAAO,CAAO,CAAC,CAC3C,IAAK,OAAO,CACV,mBAAO5B,IAAA,CAACH,KAAK,EAAC8B,EAAE,CAAC,SAAS,CAACE,IAAI,CAAC,MAAM,CAAAD,QAAA,CAAC,OAAK,CAAO,CAAC,CACtD,IAAK,MAAM,CACT,mBAAO5B,IAAA,CAACH,KAAK,EAAC8B,EAAE,CAAC,SAAS,CAAAC,QAAA,CAAC,MAAI,CAAO,CAAC,CACzC,QACE,mBAAO5B,IAAA,CAACH,KAAK,EAAC8B,EAAE,CAAC,WAAW,CAAAC,QAAA,CAAEF,IAAI,CAAQ,CAAC,CAC/C,CACF,CAAC,CAED,KAAM,CAAAI,UAAU,CAAIC,UAAU,EAAK,CACjC,GAAI,CAACA,UAAU,CAAE,MAAO,KAAK,CAC7B,MAAO,IAAI,CAAAC,IAAI,CAACD,UAAU,CAAC,CAACE,cAAc,CAAC,CAAC,CAC9C,CAAC,CAED,mBACE/B,KAAA,CAACZ,SAAS,EAAC4C,SAAS,CAAC,MAAM,CAAAN,QAAA,eACzB1B,KAAA,QAAKgC,SAAS,CAAC,wDAAwD,CAAAN,QAAA,eACrE5B,IAAA,OAAA4B,QAAA,CAAI,eAAa,CAAI,CAAC,cACtB5B,IAAA,CAACX,IAAI,EAAC8C,EAAE,CAAC,YAAY,CAACD,SAAS,CAAC,yBAAyB,CAAAN,QAAA,CAAC,mBAE1D,CAAM,CAAC,EACJ,CAAC,CAELnB,OAAO,cACNT,IAAA,QAAKkC,SAAS,CAAC,kBAAkB,CAAAN,QAAA,cAC/B5B,IAAA,CAACL,OAAO,EAACyC,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,QAAQ,CAACC,OAAO,CAAC,SAAS,CAAAV,QAAA,cACzD5B,IAAA,SAAMkC,SAAS,CAAC,iBAAiB,CAAAN,QAAA,CAAC,YAAU,CAAM,CAAC,CAC5C,CAAC,CACP,CAAC,CACJjB,KAAK,cACPX,IAAA,CAACJ,KAAK,EAAC0C,OAAO,CAAC,QAAQ,CAAAV,QAAA,CAAEjB,KAAK,CAAQ,CAAC,CACrCJ,UAAU,cACZL,KAAA,CAAAE,SAAA,EAAAwB,QAAA,eACE1B,KAAA,CAACT,IAAI,EAACyC,SAAS,CAAC,gBAAgB,CAAAN,QAAA,eAC9B5B,IAAA,CAACP,IAAI,CAAC8C,MAAM,EAACL,SAAS,CAAC,uBAAuB,CAAAN,QAAA,cAC5C1B,KAAA,QAAKgC,SAAS,CAAC,mDAAmD,CAAAN,QAAA,eAChE1B,KAAA,OAAIgC,SAAS,CAAC,MAAM,CAAAN,QAAA,EACjBH,kBAAkB,CAAClB,UAAU,CAACmB,IAAI,CAAC,CAAC,SAAO,CAACpB,OAAO,EAClD,CAAC,cACLJ,KAAA,QAAA0B,QAAA,eACE5B,IAAA,CAACN,MAAM,EACL4C,OAAO,CAAEzB,SAAS,GAAK,UAAU,CAAG,OAAO,CAAG,eAAgB,CAC9D2B,IAAI,CAAC,IAAI,CACTN,SAAS,CAAC,MAAM,CAChBO,OAAO,CAAElB,eAAgB,CAAAK,QAAA,CAC1B,UAED,CAAQ,CAAC,cACT5B,IAAA,CAACN,MAAM,EACL4C,OAAO,CAAEzB,SAAS,GAAK,WAAW,CAAG,OAAO,CAAG,eAAgB,CAC/D2B,IAAI,CAAC,IAAI,CACTC,OAAO,CAAElB,eAAgB,CAAAK,QAAA,CAC1B,WAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACK,CAAC,cACd1B,KAAA,CAACT,IAAI,CAACiD,IAAI,EAAAd,QAAA,eACR1B,KAAA,CAACX,GAAG,EAAAqC,QAAA,eACF5B,IAAA,CAACR,GAAG,EAACmD,EAAE,CAAE,CAAE,CAACT,SAAS,CAAC,kBAAkB,CAAAN,QAAA,CACrCrB,UAAU,CAACqC,KAAK,EAAI,CAAC,IAAM,CAC1B;AACA,KAAM,CAAAC,KAAK,CAAGhC,SAAS,GAAK,UAAU,CACjCN,UAAU,CAACqC,KAAK,CAACE,uBAAuB,EAAIvC,UAAU,CAACqC,KAAK,CAACG,qBAAqB,CAClFxC,UAAU,CAACqC,KAAK,CAACI,wBAAwB,EAAIzC,UAAU,CAACqC,KAAK,CAACK,sBAAuB,CAE1F,KAAM,CAAAC,QAAQ,CAAGrC,SAAS,GAAK,UAAU,CACrCN,UAAU,CAACqC,KAAK,CAACO,iBAAiB,CAClC5C,UAAU,CAACqC,KAAK,CAACQ,kBAAkB,CAEvC;AACA,KAAM,CAAAC,QAAQ,CAAGR,KAAK,GAAKK,QAAQ,CAAG,2BAA2BA,QAAQ,EAAE,CAAG,IAAI,CAAC,CAEnF,MAAO,CAAAG,QAAQ,cACbrD,IAAA,QAAKkC,SAAS,CAAC,wBAAwB,CAAAN,QAAA,cACrC5B,IAAA,QACEsD,GAAG,CAAED,QAAS,CACdE,GAAG,CAAE,GAAGhD,UAAU,CAACmB,IAAI,SAAU,CACjCQ,SAAS,CAAC,oCAAoC,CAC9CsB,KAAK,CAAE,CAAEC,SAAS,CAAE,OAAQ,CAAE,CAC9BC,OAAO,CAAGC,CAAC,EAAK,CACd;AACA,GAAId,KAAK,EAAIK,QAAQ,CAAE,CACrBS,CAAC,CAACC,MAAM,CAACN,GAAG,CAAG,2BAA2BJ,QAAQ,EAAE,CACtD,CACF,CAAE,CACH,CAAC,CACC,CAAC,cAENlD,IAAA,QAAKkC,SAAS,CAAC,YAAY,CAAAN,QAAA,CAAC,oBAAkB,CAAK,CACpD,CACH,CAAC,EAAE,CAAC,CACD,CAAC,cACN1B,KAAA,CAACV,GAAG,EAACmD,EAAE,CAAE,CAAE,CAAAf,QAAA,eACT5B,IAAA,OAAA4B,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAC1B5B,IAAA,UAAOkC,SAAS,CAAC,sBAAsB,CAAAN,QAAA,cACrC1B,KAAA,UAAA0B,QAAA,eACE1B,KAAA,OAAA0B,QAAA,eACE5B,IAAA,OAAI6D,KAAK,CAAC,KAAK,CAAAjC,QAAA,CAAC,MAAI,CAAI,CAAC,cACzB5B,IAAA,OAAA4B,QAAA,CAAKrB,UAAU,CAACmB,IAAI,CAAK,CAAC,EACxB,CAAC,cACLxB,KAAA,OAAA0B,QAAA,eACE5B,IAAA,OAAA4B,QAAA,CAAI,cAAY,CAAI,CAAC,cACrB5B,IAAA,OAAA4B,QAAA,CACGrB,UAAU,CAACqC,KAAK,CAACkB,aAAa,EAC9BvD,UAAU,CAACqC,KAAK,CAACmB,WAAW,EAC5BxD,UAAU,CAACqC,KAAK,CAACoB,UAAU,EAAI,KAAK,CACnC,CAAC,EACH,CAAC,cACL9D,KAAA,OAAA0B,QAAA,eACE5B,IAAA,OAAA4B,QAAA,CAAI,eAAa,CAAI,CAAC,cACtB5B,IAAA,OAAA4B,QAAA,CAAKE,UAAU,CAACvB,UAAU,CAACqC,KAAK,CAACqB,SAAS,CAAC,CAAK,CAAC,EAC/C,CAAC,cACL/D,KAAA,OAAA0B,QAAA,eACE5B,IAAA,OAAA4B,QAAA,CAAI,aAAW,CAAI,CAAC,cACpB5B,IAAA,OAAA4B,QAAA,CAAKrB,UAAU,CAACqC,KAAK,CAACsB,QAAQ,EAAI,KAAK,CAAK,CAAC,EAC3C,CAAC,cACLhE,KAAA,OAAA0B,QAAA,eACE5B,IAAA,OAAA4B,QAAA,CAAI,MAAI,CAAI,CAAC,cACb5B,IAAA,OAAA4B,QAAA,CAAKrB,UAAU,CAACqC,KAAK,CAACP,IAAI,EAAI,KAAK,CAAK,CAAC,EACvC,CAAC,cACLnC,KAAA,OAAA0B,QAAA,eACE5B,IAAA,OAAA4B,QAAA,CAAI,aAAW,CAAI,CAAC,cACpB5B,IAAA,OAAA4B,QAAA,CAAKrB,UAAU,CAACqC,KAAK,CAACuB,WAAW,EAAI,eAAe,CAAK,CAAC,EACxD,CAAC,EACA,CAAC,CACH,CAAC,EACL,CAAC,EACH,CAAC,CAEL5D,UAAU,CAACmB,IAAI,GAAK,SAAS,EAAInB,UAAU,CAACqC,KAAK,CAACwB,QAAQ,eACzDlE,KAAA,QAAKgC,SAAS,CAAC,MAAM,CAAAN,QAAA,eACnB5B,IAAA,OAAA4B,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxB5B,IAAA,QAAKkC,SAAS,CAAC,kBAAkB,CAAAN,QAAA,cAC/B1B,KAAA,UAAOgC,SAAS,CAAC,oCAAoC,CAAAN,QAAA,eACnD5B,IAAA,UAAOkC,SAAS,CAAC,eAAe,CAAAN,QAAA,cAC9B1B,KAAA,OAAA0B,QAAA,eACE5B,IAAA,OAAA4B,QAAA,CAAI,IAAE,CAAI,CAAC,cACX5B,IAAA,OAAA4B,QAAA,CAAI,eAAU,CAAI,CAAC,cACnB5B,IAAA,OAAA4B,QAAA,CAAI,YAAU,CAAI,CAAC,cACnB5B,IAAA,OAAA4B,QAAA,CAAI,iBAAY,CAAI,CAAC,cACrB5B,IAAA,OAAA4B,QAAA,CAAI,UAAQ,CAAI,CAAC,EACf,CAAC,CACA,CAAC,cACR5B,IAAA,UAAA4B,QAAA,CACGrB,UAAU,CAACqC,KAAK,CAACwB,QAAQ,CAACC,GAAG,CAAC,CAACC,OAAO,CAAEC,KAAK,QAAAC,gBAAA,CAAAC,iBAAA,CAAAC,eAAA,oBAC5CxE,KAAA,OAAA0B,QAAA,eACE5B,IAAA,OAAA4B,QAAA,CAAK0C,OAAO,CAACK,UAAU,CAAK,CAAC,cAC7B3E,IAAA,OAAA4B,QAAA,CAAK,EAAA4C,gBAAA,CAAAF,OAAO,CAACM,QAAQ,UAAAJ,gBAAA,iBAAhBA,gBAAA,CAAkBK,OAAO,CAAC,CAAC,CAAC,GAAI,KAAK,CAAK,CAAC,cAChD7E,IAAA,OAAA4B,QAAA,CAAK,EAAA6C,iBAAA,CAAAH,OAAO,CAACQ,QAAQ,UAAAL,iBAAA,iBAAhBA,iBAAA,CAAkBI,OAAO,CAAC,CAAC,CAAC,GAAI,KAAK,CAAK,CAAC,cAChD7E,IAAA,OAAA4B,QAAA,CAAK,EAAA8C,eAAA,CAAAJ,OAAO,CAACS,MAAM,UAAAL,eAAA,iBAAdA,eAAA,CAAgBG,OAAO,CAAC,CAAC,CAAC,GAAI,KAAK,CAAK,CAAC,cAC9C7E,IAAA,OAAA4B,QAAA,CACG0C,OAAO,CAACM,QAAQ,CAAG,IAAI,cACtB5E,IAAA,CAACH,KAAK,EAAC8B,EAAE,CAAC,QAAQ,CAAAC,QAAA,CAAC,MAAI,CAAO,CAAC,CAC7B0C,OAAO,CAACM,QAAQ,CAAG,GAAG,cACxB5E,IAAA,CAACH,KAAK,EAAC8B,EAAE,CAAC,SAAS,CAACE,IAAI,CAAC,MAAM,CAAAD,QAAA,CAAC,QAAM,CAAO,CAAC,cAE9C5B,IAAA,CAACH,KAAK,EAAC8B,EAAE,CAAC,SAAS,CAAAC,QAAA,CAAC,KAAG,CAAO,CAC/B,CACC,CAAC,GAbE2C,KAcL,CAAC,EACN,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,EACH,CACN,CAEAhE,UAAU,CAACmB,IAAI,GAAK,OAAO,EAAInB,UAAU,CAACqC,KAAK,CAACoC,MAAM,eACrD9E,KAAA,QAAKgC,SAAS,CAAC,MAAM,CAAAN,QAAA,eACnB5B,IAAA,OAAA4B,QAAA,CAAI,eAAa,CAAI,CAAC,cACtB5B,IAAA,QAAKkC,SAAS,CAAC,kBAAkB,CAAAN,QAAA,cAC/B1B,KAAA,UAAOgC,SAAS,CAAC,oCAAoC,CAAAN,QAAA,eACnD5B,IAAA,UAAOkC,SAAS,CAAC,eAAe,CAAAN,QAAA,cAC9B1B,KAAA,OAAA0B,QAAA,eACE5B,IAAA,OAAA4B,QAAA,CAAI,IAAE,CAAI,CAAC,cACX5B,IAAA,OAAA4B,QAAA,CAAI,MAAI,CAAI,CAAC,cACb5B,IAAA,OAAA4B,QAAA,CAAI,eAAU,CAAI,CAAC,cACnB5B,IAAA,OAAA4B,QAAA,CAAI,YAAU,CAAI,CAAC,EACjB,CAAC,CACA,CAAC,cACR5B,IAAA,UAAA4B,QAAA,CACGrB,UAAU,CAACqC,KAAK,CAACoC,MAAM,CAACX,GAAG,CAAC,CAACY,KAAK,CAAEV,KAAK,QAAAW,cAAA,oBACxChF,KAAA,OAAA0B,QAAA,eACE5B,IAAA,OAAA4B,QAAA,CAAKqD,KAAK,CAACE,QAAQ,CAAK,CAAC,cACzBnF,IAAA,OAAA4B,QAAA,CAAKqD,KAAK,CAACG,UAAU,CAAK,CAAC,cAC3BpF,IAAA,OAAA4B,QAAA,CAAK,EAAAsD,cAAA,CAAAD,KAAK,CAACL,QAAQ,UAAAM,cAAA,iBAAdA,cAAA,CAAgBL,OAAO,CAAC,CAAC,CAAC,GAAI,KAAK,CAAK,CAAC,cAC9C3E,KAAA,OAAA0B,QAAA,EAAK,CAACqD,KAAK,CAACI,UAAU,CAAG,GAAG,EAAER,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,EAAI,CAAC,GAJxCN,KAKL,CAAC,EACN,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,CAELhE,UAAU,CAACqC,KAAK,CAAC0C,WAAW,eAC3BpF,KAAA,QAAKgC,SAAS,CAAC,MAAM,CAAAN,QAAA,eACnB5B,IAAA,OAAA4B,QAAA,CAAI,yBAAuB,CAAI,CAAC,cAChC5B,IAAA,QAAKkC,SAAS,CAAC,kBAAkB,CAAAN,QAAA,CAC9B2D,MAAM,CAACC,OAAO,CAACjF,UAAU,CAACqC,KAAK,CAAC0C,WAAW,CAAC,CAACjB,GAAG,CAACoB,IAAA,MAAC,CAAC/D,IAAI,CAAEgE,KAAK,CAAC,CAAAD,IAAA,oBAC9DvF,KAAA,QAAgBgC,SAAS,CAAC,WAAW,CAAAN,QAAA,eACnC5B,IAAA,CAACH,KAAK,EAAC8B,EAAE,CAAC,MAAM,CAACO,SAAS,CAAC,MAAM,CAAAN,QAAA,CAAE8D,KAAK,CAAQ,CAAC,IAAC,CAAChE,IAAI,GAD/CA,IAEL,CAAC,EACP,CAAC,CACC,CAAC,EACH,CACN,EACE,CACN,CAEAnB,UAAU,CAACmB,IAAI,GAAK,MAAM,EAAInB,UAAU,CAACqC,KAAK,CAAC+C,KAAK,eACnDzF,KAAA,QAAKgC,SAAS,CAAC,MAAM,CAAAN,QAAA,eACnB5B,IAAA,OAAA4B,QAAA,CAAI,cAAY,CAAI,CAAC,cACrB5B,IAAA,QAAKkC,SAAS,CAAC,kBAAkB,CAAAN,QAAA,cAC/B1B,KAAA,UAAOgC,SAAS,CAAC,oCAAoC,CAAAN,QAAA,eACnD5B,IAAA,UAAOkC,SAAS,CAAC,eAAe,CAAAN,QAAA,cAC9B1B,KAAA,OAAA0B,QAAA,eACE5B,IAAA,OAAA4B,QAAA,CAAI,IAAE,CAAI,CAAC,cACX5B,IAAA,OAAA4B,QAAA,CAAI,MAAI,CAAI,CAAC,cACb5B,IAAA,OAAA4B,QAAA,CAAI,WAAS,CAAI,CAAC,cAClB5B,IAAA,OAAA4B,QAAA,CAAI,YAAU,CAAI,CAAC,EACjB,CAAC,CACA,CAAC,cACR5B,IAAA,UAAA4B,QAAA,CACGrB,UAAU,CAACqC,KAAK,CAAC+C,KAAK,CAACtB,GAAG,CAAC,CAACuB,IAAI,CAAErB,KAAK,QAAAsB,cAAA,oBACtC3F,KAAA,OAAA0B,QAAA,eACE5B,IAAA,OAAA4B,QAAA,CAAKgE,IAAI,CAACE,OAAO,CAAK,CAAC,cACvB9F,IAAA,OAAA4B,QAAA,CAAKgE,IAAI,CAACG,SAAS,CAAK,CAAC,cACzB/F,IAAA,OAAA4B,QAAA,cACE5B,IAAA,CAACH,KAAK,EACJ8B,EAAE,CACAiE,IAAI,CAACI,SAAS,GAAK,MAAM,CAAG,SAAS,CACrCJ,IAAI,CAACI,SAAS,GAAK,MAAM,CAAG,SAAS,CAAG,QACzC,CACDnE,IAAI,CAAE+D,IAAI,CAACI,SAAS,GAAK,MAAM,CAAG,MAAM,CAAGC,SAAU,CAAArE,QAAA,CAEpDgE,IAAI,CAACI,SAAS,CACV,CAAC,CACN,CAAC,cACLhG,IAAA,OAAA4B,QAAA,CAAK,EAAAiE,cAAA,CAAAD,IAAI,CAACM,QAAQ,UAAAL,cAAA,iBAAbA,cAAA,CAAehB,OAAO,CAAC,CAAC,CAAC,GAAI,KAAK,CAAK,CAAC,GAdtCN,KAeL,CAAC,EACN,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,CAELhE,UAAU,CAACqC,KAAK,CAACuD,gBAAgB,eAChCjG,KAAA,QAAKgC,SAAS,CAAC,MAAM,CAAAN,QAAA,eACnB5B,IAAA,OAAA4B,QAAA,CAAI,wBAAsB,CAAI,CAAC,cAC/B5B,IAAA,QAAKkC,SAAS,CAAC,kBAAkB,CAAAN,QAAA,CAC9B2D,MAAM,CAACC,OAAO,CAACjF,UAAU,CAACqC,KAAK,CAACuD,gBAAgB,CAAC,CAAC9B,GAAG,CAAC+B,KAAA,MAAC,CAACJ,SAAS,CAAEN,KAAK,CAAC,CAAAU,KAAA,oBACxElG,KAAA,QAAqBgC,SAAS,CAAC,WAAW,CAAAN,QAAA,eACxC5B,IAAA,CAACH,KAAK,EACJ8B,EAAE,CACAqE,SAAS,GAAK,MAAM,CAAG,SAAS,CAChCA,SAAS,GAAK,MAAM,CAAG,SAAS,CAAG,QACpC,CACDnE,IAAI,CAAEmE,SAAS,GAAK,MAAM,CAAG,MAAM,CAAGC,SAAU,CAChD/D,SAAS,CAAC,MAAM,CAAAN,QAAA,CAEf8D,KAAK,CACD,CAAC,IAAC,CAACM,SAAS,GAVXA,SAWL,CAAC,EACP,CAAC,CACC,CAAC,EACH,CACN,EACE,CACN,cAGDhG,IAAA,QAAKkC,SAAS,CAAC,MAAM,CAAAN,QAAA,cACnB1B,KAAA,CAACT,IAAI,EAACyC,SAAS,CAAC,UAAU,CAAAN,QAAA,eACxB5B,IAAA,CAACP,IAAI,CAAC8C,MAAM,EAAAX,QAAA,cACV5B,IAAA,OAAIkC,SAAS,CAAC,MAAM,CAAAN,QAAA,CAAC,oBAAkB,CAAI,CAAC,CACjC,CAAC,cACd1B,KAAA,CAACT,IAAI,CAACiD,IAAI,EAAAd,QAAA,eACR5B,IAAA,MAAA4B,QAAA,CAAG,oEAAkE,CAAG,CAAC,CACxErB,UAAU,CAACmB,IAAI,GAAK,SAAS,eAC5BxB,KAAA,QAAA0B,QAAA,eACE1B,KAAA,OAAA0B,QAAA,eACE5B,IAAA,OAAA4B,QAAA,CAAI,0BAAwB,CAAI,CAAC,cACjC5B,IAAA,OAAA4B,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxB5B,IAAA,OAAA4B,QAAA,CAAI,2BAAyB,CAAI,CAAC,cAClC5B,IAAA,OAAA4B,QAAA,CAAI,oBAAkB,CAAI,CAAC,EACzB,CAAC,cACL1B,KAAA,MAAA0B,QAAA,eAAG5B,IAAA,WAAA4B,QAAA,CAAQ,WAAS,CAAQ,CAAC,IAAC,CAC5BrB,UAAU,CAACqC,KAAK,CAACwB,QAAQ,EAAI7D,UAAU,CAACqC,KAAK,CAACwB,QAAQ,CAACiC,MAAM,CAAG,CAAC,EACjE9F,UAAU,CAACqC,KAAK,CAACwB,QAAQ,CAACkC,IAAI,CAACC,CAAC,EAAIA,CAAC,CAAC3B,QAAQ,CAAG,IAAI,CAAC,CACtD,MAAM,CAAGrE,UAAU,CAACqC,KAAK,CAACwB,QAAQ,EAAI7D,UAAU,CAACqC,KAAK,CAACwB,QAAQ,CAACiC,MAAM,CAAG,CAAC,EAC1E9F,UAAU,CAACqC,KAAK,CAACwB,QAAQ,CAACkC,IAAI,CAACC,CAAC,EAAIA,CAAC,CAAC3B,QAAQ,CAAG,GAAG,CAAC,CACrD,QAAQ,CAAG,KAAK,EACd,CAAC,EACF,CACN,CAEArE,UAAU,CAACmB,IAAI,GAAK,OAAO,eAC1BxB,KAAA,QAAA0B,QAAA,eACE1B,KAAA,OAAA0B,QAAA,eACE5B,IAAA,OAAA4B,QAAA,CAAI,kCAAgC,CAAI,CAAC,cACzC5B,IAAA,OAAA4B,QAAA,CAAI,iCAA+B,CAAI,CAAC,CACvCrB,UAAU,CAACqC,KAAK,CAAC0C,WAAW,EAC5B/E,UAAU,CAACqC,KAAK,CAAC0C,WAAW,CAAC,iBAAiB,CAAC,CAAG,CAAC,eAClDtF,IAAA,OAAA4B,QAAA,CAAI,wDAAsD,CAAI,CAC/D,EACC,CAAC,cACL1B,KAAA,MAAA0B,QAAA,eAAG5B,IAAA,WAAA4B,QAAA,CAAQ,WAAS,CAAQ,CAAC,IAAC,CAC5BrB,UAAU,CAACqC,KAAK,CAAC0C,WAAW,EAC5B/E,UAAU,CAACqC,KAAK,CAAC0C,WAAW,CAAC,iBAAiB,CAAC,CAAG,CAAC,CACnD,MAAM,CAAG,QAAQ,EACf,CAAC,EACF,CACN,CAEA/E,UAAU,CAACmB,IAAI,GAAK,MAAM,eACzBxB,KAAA,QAAA0B,QAAA,eACE1B,KAAA,OAAA0B,QAAA,eACE5B,IAAA,OAAA4B,QAAA,CAAI,yBAAuB,CAAI,CAAC,cAChC5B,IAAA,OAAA4B,QAAA,CAAI,yBAAuB,CAAI,CAAC,cAChC5B,IAAA,OAAA4B,QAAA,CAAI,gCAA8B,CAAI,CAAC,EACrC,CAAC,cACL1B,KAAA,MAAA0B,QAAA,eAAG5B,IAAA,WAAA4B,QAAA,CAAQ,WAAS,CAAQ,CAAC,IAAC,CAC5BrB,UAAU,CAACqC,KAAK,CAACuD,gBAAgB,EACjC5F,UAAU,CAACqC,KAAK,CAACuD,gBAAgB,CAAC,MAAM,CAAC,CAAG,CAAC,CAC7C,MAAM,CAAG5F,UAAU,CAACqC,KAAK,CAACuD,gBAAgB,CAAC,MAAM,CAAC,CAAG,CAAC,CACtD,QAAQ,CAAG,KAAK,EACd,CAAC,EACF,CACN,EACQ,CAAC,EACR,CAAC,CACJ,CAAC,EACG,CAAC,EACR,CAAC,cAEPnG,IAAA,QAAKkC,SAAS,CAAC,iCAAiC,CAAAN,QAAA,cAC9C5B,IAAA,CAACN,MAAM,EAAC4C,OAAO,CAAC,SAAS,CAAAV,QAAA,CAAC,iBAE1B,CAAQ,CAAC,CACN,CAAC,EACN,CAAC,cAEH1B,KAAA,CAACN,KAAK,EAAC0C,OAAO,CAAC,SAAS,CAAAV,QAAA,EAAC,+BAA6B,CAACtB,OAAO,EAAQ,CACvE,EACQ,CAAC,CAEhB,CAEA,cAAe,CAAAD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}