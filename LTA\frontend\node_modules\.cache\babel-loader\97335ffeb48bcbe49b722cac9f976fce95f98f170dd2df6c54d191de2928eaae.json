{"ast": null, "code": "import React,{useState,useRef,useEffect}from'react';import{<PERSON><PERSON><PERSON>,<PERSON>,<PERSON><PERSON>,Form,Tabs,Tab,<PERSON><PERSON>,Spinner,Overlay<PERSON><PERSON>ger,Popover,Modal}from'react-bootstrap';import axios from'axios';import Webcam from'react-webcam';import'./Pavement.css';import useResponsive from'../hooks/useResponsive';import VideoDefectDetection from'../components/VideoDefectDetection';import{validateMultipleFiles,showFileValidationError}from'../utils/fileValidation';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Pavement=()=>{const[activeTab,setActiveTab]=useState('detection');const[detectionType,setDetectionType]=useState('all');const[imageFiles,setImageFiles]=useState([]);const[imagePreviewsMap,setImagePreviewsMap]=useState({});const[imageLocationMap,setImageLocationMap]=useState({});const[currentImageIndex,setCurrentImageIndex]=useState(0);const[processedImage,setProcessedImage]=useState(null);const[results,setResults]=useState(null);const[loading,setLoading]=useState(false);const[error,setError]=useState('');const[cameraActive,setCameraActive]=useState(false);const[coordinates,setCoordinates]=useState('Not Available');const[cameraOrientation,setCameraOrientation]=useState('environment');const[locationPermission,setLocationPermission]=useState('unknown');const[locationError,setLocationError]=useState('');const[locationLoading,setLocationLoading]=useState(false);// Add state for batch processing results\nconst[batchResults,setBatchResults]=useState([]);const[batchProcessing,setBatchProcessing]=useState(false);const[processedCount,setProcessedCount]=useState(0);// Add state for storing processed images for results table\nconst[processedImagesData,setProcessedImagesData]=useState({});// Add state for classification error modal\nconst[showClassificationModal,setShowClassificationModal]=useState(false);const[classificationError,setClassificationError]=useState('');const[totalToProcess,setTotalToProcess]=useState(0);// Add state for image modal\nconst[showImageModal,setShowImageModal]=useState(false);const[selectedImageData,setSelectedImageData]=useState(null);// Add state for image status table filtering\nconst[imageFilter,setImageFilter]=useState('all');// 'all', 'road', 'non-road'\n// Add state for road classification toggle (default to false for better user experience)\nconst[roadClassificationEnabled,setRoadClassificationEnabled]=useState(false);// Add state for enhanced detection results table\nconst[detectionTableFilter,setDetectionTableFilter]=useState('all');// 'all', 'potholes', 'cracks', 'kerbs'\nconst[sortConfig,setSortConfig]=useState({key:null,direction:'asc'});// Auto-clear is always enabled - no toggle needed\nconst webcamRef=useRef(null);const fileInputRef=useRef(null);const{isMobile}=useResponsive();// Create the popover content\nconst reminderPopover=/*#__PURE__*/_jsxs(Popover,{id:\"reminder-popover\",style:{maxWidth:'300px'},children:[/*#__PURE__*/_jsx(Popover.Header,{as:\"h3\",children:\"\\uD83D\\uDCF8 Image Upload Guidelines\"}),/*#__PURE__*/_jsxs(Popover.Body,{children:[/*#__PURE__*/_jsx(\"p\",{style:{marginBottom:'10px'},children:\"Please ensure your uploaded images are:\"}),/*#__PURE__*/_jsxs(\"ul\",{style:{marginBottom:'0',paddingLeft:'20px'},children:[/*#__PURE__*/_jsx(\"li\",{children:\"Focused directly on the road surface\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Well-lit and clear\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Showing the entire area of concern\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Taken from a reasonable distance to capture context\"})]})]})]});// Safari-compatible geolocation permission check\nconst checkLocationPermission=async()=>{if(!navigator.permissions||!navigator.permissions.query){// Fallback for older browsers\nreturn'prompt';}try{const permission=await navigator.permissions.query({name:'geolocation'});return permission.state;}catch(err){console.warn('Permission API not supported or failed:',err);return'prompt';}};// Safari-compatible geolocation request\nconst requestLocation=()=>{return new Promise((resolve,reject)=>{// Check if geolocation is supported\nif(!navigator.geolocation){reject(new Error('Geolocation is not supported by this browser'));return;}// Check if we're in a secure context (HTTPS)\nif(!window.isSecureContext){reject(new Error('Geolocation requires a secure context (HTTPS)'));return;}const options={enableHighAccuracy:true,timeout:15000,// 15 seconds timeout\nmaximumAge:60000// Accept cached position up to 1 minute old\n};navigator.geolocation.getCurrentPosition(position=>{resolve(position);},error=>{let errorMessage='Unable to retrieve location';switch(error.code){case error.PERMISSION_DENIED:errorMessage='Location access denied. Please enable location permissions in your browser settings.';break;case error.POSITION_UNAVAILABLE:errorMessage='Location information is unavailable. Please try again.';break;case error.TIMEOUT:errorMessage='Location request timed out. Please try again.';break;default:errorMessage=`Location error: ${error.message}`;break;}reject(new Error(errorMessage));},options);});};// Enhanced location handler with Safari-specific fixes\nconst handleLocationRequest=async()=>{setLocationLoading(true);setLocationError('');try{// First check permission state\nconst permissionState=await checkLocationPermission();setLocationPermission(permissionState);// If permission is denied, provide user guidance\nif(permissionState==='denied'){const errorMsg='Location access denied. To enable location access:\\n'+'• Safari: Settings > Privacy & Security > Location Services\\n'+'• Chrome: Settings > Privacy > Location\\n'+'• Firefox: Settings > Privacy > Location\\n'+'Then refresh this page and try again.';setLocationError(errorMsg);setCoordinates('Permission Denied');return;}// Request location\nconst position=await requestLocation();const{latitude,longitude}=position.coords;// Format coordinates with better precision\nconst formattedCoords=`${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;setCoordinates(formattedCoords);setLocationPermission('granted');setLocationError('');console.log('Location acquired:',{latitude,longitude,accuracy:position.coords.accuracy});}catch(error){console.error('Location request failed:',error);setLocationError(error.message);setCoordinates('Location Error');// Update permission state based on error\nif(error.message.includes('denied')){setLocationPermission('denied');}}finally{setLocationLoading(false);}};// Handle multiple file input change\nconst handleFileChange=e=>{const files=Array.from(e.target.files);if(files.length>0){// Validate files before processing\nconst validation=validateMultipleFiles(files,'image','pavement_detection');if(!validation.isValid){showFileValidationError(validation.errorMessage,setError);// Clear the file input\nif(e.target){e.target.value='';}return;}// Clear any previous errors\nsetError('');setImageFiles([...imageFiles,...files]);// Create previews and location data for each file\nfiles.forEach(file=>{const reader=new FileReader();reader.onloadend=()=>{setImagePreviewsMap(prev=>({...prev,[file.name]:reader.result}));};reader.readAsDataURL(file);// Store location as \"Not Available\" for uploaded files\nsetImageLocationMap(prev=>({...prev,[file.name]:'Not Available'}));// Show AVIF conversion notice if applicable\nif(file.name.toLowerCase().endsWith('.avif')){console.log(`AVIF file detected: ${file.name} - will be automatically converted to JPG during processing`);}});// Reset results\nsetProcessedImage(null);setResults(null);setError('');}};// Handle camera capture with location validation\nconst handleCapture=async()=>{const imageSrc=webcamRef.current.getScreenshot();if(imageSrc){// If we don't have location data, try to get it before capturing\nif(coordinates==='Not Available'||coordinates==='Location Error'){await handleLocationRequest();}const timestamp=new Date().toISOString();const filename=`camera_capture_${timestamp}.jpg`;const captureCoordinates=coordinates;// Capture current coordinates\nsetImageFiles([...imageFiles,filename]);setImagePreviewsMap(prev=>({...prev,[filename]:imageSrc}));setImageLocationMap(prev=>({...prev,[filename]:captureCoordinates}));setCurrentImageIndex(imageFiles.length);setProcessedImage(null);setResults(null);setError('');// Log capture with current coordinates\nconsole.log('Photo captured with coordinates:',captureCoordinates);}};// Get location data for currently selected image\nconst getCurrentImageLocation=()=>{if(Object.keys(imagePreviewsMap).length===0){return coordinates;// Use current coordinates if no images\n}const currentFilename=Object.keys(imagePreviewsMap)[currentImageIndex];return imageLocationMap[currentFilename]||'Not Available';};// Toggle camera with improved location handling\nconst toggleCamera=async()=>{const newCameraState=!cameraActive;setCameraActive(newCameraState);if(newCameraState){// Get location when camera is activated\nawait handleLocationRequest();}else{// Only reset location if no images are captured\n// This preserves location data for captured images\nif(Object.keys(imagePreviewsMap).length===0){setCoordinates('Not Available');setLocationError('');setLocationPermission('unknown');}}};// Toggle camera orientation (front/back) for mobile devices\nconst toggleCameraOrientation=()=>{setCameraOrientation(prev=>prev==='environment'?'user':'environment');};// Helper function to handle classification errors\nconst handleClassificationError=errorMessage=>{setClassificationError(errorMessage);setShowClassificationModal(true);setError('');// Clear general error since we're showing specific modal\n};// Process image for detection\nconst handleProcess=async()=>{setLoading(true);setError('');try{// Get user info from session storage\nconst userString=sessionStorage.getItem('user');const user=userString?JSON.parse(userString):null;// Get the currently selected image\nconst currentImagePreview=Object.values(imagePreviewsMap)[currentImageIndex];if(!currentImagePreview){setError('No image selected for processing');setLoading(false);return;}// Get coordinates for the current image\nconst imageCoordinates=getCurrentImageLocation();// Get the current image filename\nconst filenames=Object.keys(imagePreviewsMap);const currentFilename=filenames[currentImageIndex];// Prepare request data\nconst requestData={image:currentImagePreview,coordinates:imageCoordinates,username:(user===null||user===void 0?void 0:user.username)||'Unknown',role:(user===null||user===void 0?void 0:user.role)||'Unknown',skip_road_classification:!roadClassificationEnabled};// Determine endpoint based on detection type\nlet endpoint;switch(detectionType){case'all':endpoint='/api/pavement/detect-all';break;case'potholes':endpoint='/api/pavement/detect-potholes';break;case'cracks':endpoint='/api/pavement/detect-cracks';break;case'kerbs':endpoint='/api/pavement/detect-kerbs';break;default:endpoint='/api/pavement/detect-all';}// Make API request\nconst response=await axios.post(endpoint,requestData);// Handle response\nif(response.data.success){var _response$data$classi;// Check if the image was actually processed (contains road) or just classified\nconst isProcessed=response.data.processed!==false;const isRoad=((_response$data$classi=response.data.classification)===null||_response$data$classi===void 0?void 0:_response$data$classi.is_road)||false;// Set the processed image and results for display\nsetProcessedImage(response.data.processed_image);setResults(response.data);// Extract detailed detection results for table display\nconst detectionResults={potholes:response.data.potholes||[],cracks:response.data.cracks||[],kerbs:response.data.kerbs||[]};// Create batch result entry for the status table\nconst batchResult={filename:currentFilename,success:true,processed:isProcessed,isRoad:isRoad,classification:response.data.classification,processedImage:response.data.processed_image,originalImage:currentImagePreview,// Add original image data\ndata:response.data,detectionResults:detectionResults,wasClassificationEnabled:roadClassificationEnabled,// ADD THIS LINE\ndetectionCounts:{potholes:detectionResults.potholes.length,cracks:detectionResults.cracks.length,kerbs:detectionResults.kerbs.length,total:detectionResults.potholes.length+detectionResults.cracks.length+detectionResults.kerbs.length}};// Update batch results to show the status table\nsetBatchResults([batchResult]);// Auto-clear uploaded image icons after successful single image processing\n// Store the processed image data before clearing (for both road and non-road)\nsetProcessedImagesData(prev=>({...prev,[currentFilename]:{originalImage:currentImagePreview,processedImage:isRoad?response.data.processed_image:null,results:response.data,isRoad:isRoad}}));// Clear image previews and files but keep results\nsetImageFiles([]);setImagePreviewsMap({});setImageLocationMap({});setCurrentImageIndex(0);// Reset coordinates when clearing all images\nsetCoordinates('Not Available');setLocationError('');setLocationPermission('unknown');if(fileInputRef.current){fileInputRef.current.value='';}}else{const errorMessage=response.data.message||'Detection failed';// Create batch result entry for failed processing\nconst batchResult={filename:currentFilename,success:false,processed:false,isRoad:false,error:errorMessage,isClassificationError:errorMessage.includes('No road detected')};// Update batch results to show the status table\nsetBatchResults([batchResult]);setError(errorMessage);}}catch(error){var _error$response,_error$response$data;const errorMessage=((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.message)||'An error occurred during detection. Please try again.';// Get the current image filename for batch results\nconst filenames=Object.keys(imagePreviewsMap);const currentFilename=filenames[currentImageIndex];// Create batch result entry for error case\nconst batchResult={filename:currentFilename,success:false,processed:false,isRoad:false,error:errorMessage,isClassificationError:errorMessage.includes('No road detected')};// Update batch results to show the status table\nsetBatchResults([batchResult]);// Check if this is a classification error (no road detected)\nif(errorMessage.includes('No road detected')){handleClassificationError(errorMessage);}else{setError(errorMessage);}}finally{setLoading(false);}};// Add a new function to process all images\nconst handleProcessAll=async()=>{if(Object.keys(imagePreviewsMap).length===0){setError('No images to process');return;}setBatchProcessing(true);setError('');setBatchResults([]);setProcessedCount(0);setTotalToProcess(Object.keys(imagePreviewsMap).length);// Get user info from session storage\nconst userString=sessionStorage.getItem('user');const user=userString?JSON.parse(userString):null;try{// Determine endpoint based on detection type\nlet endpoint;switch(detectionType){case'all':endpoint='/api/pavement/detect-all';break;case'potholes':endpoint='/api/pavement/detect-potholes';break;case'cracks':endpoint='/api/pavement/detect-cracks';break;case'kerbs':endpoint='/api/pavement/detect-kerbs';break;default:endpoint='/api/pavement/detect-all';}const results=[];const filenames=Object.keys(imagePreviewsMap);// Process each image sequentially and display immediately\nfor(let i=0;i<filenames.length;i++){const filename=filenames[i];const imageData=imagePreviewsMap[filename];try{// Update current image index to show which image is being processed\nsetCurrentImageIndex(i);// Get coordinates for this specific image\nconst imageCoordinates=imageLocationMap[filename]||'Not Available';// Prepare request data\nconst requestData={image:imageData,coordinates:imageCoordinates,username:(user===null||user===void 0?void 0:user.username)||'Unknown',role:(user===null||user===void 0?void 0:user.role)||'Unknown',skip_road_classification:!roadClassificationEnabled};// Make API request\nconst response=await axios.post(endpoint,requestData);if(response.data.success){var _response$data$classi2;// Check if the image was actually processed (contains road) or just classified\nconst isProcessed=response.data.processed!==false;const isRoad=((_response$data$classi2=response.data.classification)===null||_response$data$classi2===void 0?void 0:_response$data$classi2.is_road)||false;if(isProcessed&&isRoad){// Road image that was processed - display the results\nsetProcessedImage(response.data.processed_image);setResults(response.data);}// Extract detailed detection results for table display\nconst detectionResults={potholes:response.data.potholes||[],cracks:response.data.cracks||[],kerbs:response.data.kerbs||[]};results.push({filename,success:true,processed:isProcessed,isRoad:isRoad,classification:response.data.classification,processedImage:response.data.processed_image,originalImage:imageData,// Add original image data\ndata:response.data,detectionResults:detectionResults,wasClassificationEnabled:roadClassificationEnabled,// ADD THIS LINE\ndetectionCounts:{potholes:detectionResults.potholes.length,cracks:detectionResults.cracks.length,kerbs:detectionResults.kerbs.length,total:detectionResults.potholes.length+detectionResults.cracks.length+detectionResults.kerbs.length}});}else{const errorMessage=response.data.message||'Detection failed';results.push({filename,success:false,processed:false,isRoad:false,error:errorMessage,isClassificationError:errorMessage.includes('No road detected')});}}catch(error){var _error$response2,_error$response2$data;const errorMessage=((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.message)||'An error occurred during detection';results.push({filename,success:false,processed:false,isRoad:false,error:errorMessage,isClassificationError:errorMessage.includes('No road detected')});}// Update progress\nsetProcessedCount(prev=>prev+1);// Pause briefly to allow user to see the result before moving to next image\n// Only pause if not on the last image\nif(i<filenames.length-1){await new Promise(resolve=>setTimeout(resolve,2000));// 2 second pause\n}}// Store final results\nsetBatchResults(results);// After batch processing is complete, display the first successfully processed road image\nconst processedRoadImages=results.filter(r=>r.success&&r.processed&&r.isRoad);if(processedRoadImages.length>0){const firstProcessedRoadImage=processedRoadImages[0];setProcessedImage(firstProcessedRoadImage.processedImage);setResults(firstProcessedRoadImage.data);// Set the current image index to 0 (first processed road image)\nsetCurrentImageIndex(0);}else{// No road images were processed, clear the display\nsetProcessedImage(null);setResults(null);setCurrentImageIndex(0);}// Auto-clear uploaded image icons after processing is complete\n// Store processed images data before clearing (for both road and non-road)\nconst processedData={};results.forEach(result=>{if(result.success){const originalImage=imagePreviewsMap[result.filename];processedData[result.filename]={originalImage:originalImage,processedImage:result.isRoad?result.processedImage:null,results:result.data,isRoad:result.isRoad};console.log('Storing image data for:',result.filename,'isRoad:',result.isRoad,'hasOriginalImage:',!!originalImage);}});setProcessedImagesData(prev=>({...prev,...processedData}));// Clear image previews and files but keep results\nsetImageFiles([]);setImagePreviewsMap({});setImageLocationMap({});setCurrentImageIndex(0);// Reset coordinates when clearing all images\nsetCoordinates('Not Available');setLocationError('');setLocationPermission('unknown');if(fileInputRef.current){fileInputRef.current.value='';}}catch(error){setError('Failed to process batch: '+(error.message||'Unknown error'));}finally{setBatchProcessing(false);}};// Reset detection\nconst handleReset=()=>{setImageFiles([]);setImagePreviewsMap({});setImageLocationMap({});setCurrentImageIndex(0);setProcessedImage(null);setResults(null);setError('');setBatchResults([]);setProcessedCount(0);setTotalToProcess(0);setProcessedImagesData({});// Reset coordinates when clearing all images\nsetCoordinates('Not Available');setLocationError('');setLocationPermission('unknown');if(fileInputRef.current){fileInputRef.current.value='';}};// Add function to handle thumbnail clicks\nconst handleThumbnailClick=imageData=>{setSelectedImageData(imageData);setShowImageModal(true);};// Add sorting function for detection results\nconst handleSort=key=>{let direction='asc';if(sortConfig.key===key&&sortConfig.direction==='asc'){direction='desc';}setSortConfig({key,direction});};// Function to sort detection results\nconst sortDetections=detections=>{if(!sortConfig.key)return detections;return[...detections].sort((a,b)=>{let aValue=a[sortConfig.key];let bValue=b[sortConfig.key];// Handle numeric values\nif(typeof aValue==='number'&&typeof bValue==='number'){return sortConfig.direction==='asc'?aValue-bValue:bValue-aValue;}// Handle string values\nif(typeof aValue==='string'&&typeof bValue==='string'){return sortConfig.direction==='asc'?aValue.localeCompare(bValue):bValue.localeCompare(aValue);}// Handle null/undefined values\nif(aValue==null&&bValue==null)return 0;if(aValue==null)return sortConfig.direction==='asc'?1:-1;if(bValue==null)return sortConfig.direction==='asc'?-1:1;return 0;});};// Function to export detection results to CSV\nconst exportToCSV=()=>{// Flatten all detection results\nconst allDetections=[];batchResults.forEach(result=>{if(result.success&&result.processed&&result.detectionResults){const{potholes,cracks,kerbs}=result.detectionResults;// Add potholes\npotholes.forEach(pothole=>{allDetections.push({filename:result.filename,type:'Pothole',id:pothole.pothole_id,area_cm2:pothole.area_cm2,depth_cm:pothole.depth_cm,volume:pothole.volume,volume_range:pothole.volume_range,crack_type:'',area_range:'',kerb_type:'',condition:'',length_m:'',confidence:pothole.confidence});});// Add cracks\ncracks.forEach(crack=>{allDetections.push({filename:result.filename,type:'Crack',id:crack.crack_id,area_cm2:crack.area_cm2,depth_cm:'',volume:'',volume_range:'',crack_type:crack.crack_type,area_range:crack.area_range,kerb_type:'',condition:'',length_m:'',confidence:crack.confidence});});// Add kerbs\nkerbs.forEach(kerb=>{allDetections.push({filename:result.filename,type:'Kerb',id:kerb.kerb_id,area_cm2:'',depth_cm:'',volume:'',volume_range:'',crack_type:'',area_range:'',kerb_type:kerb.kerb_type,condition:kerb.condition,length_m:kerb.length_m,confidence:kerb.confidence});});}});if(allDetections.length===0){alert('No detection results to export.');return;}// Create CSV content\nconst headers=['Image Filename','Detection Type','ID','Area (cm²)','Depth (cm)','Volume (cm³)','Volume Range','Crack Type','Area Range','Kerb Type','Condition','Length (m)','Confidence'];const csvContent=[headers.join(','),...allDetections.map(detection=>[detection.filename,detection.type,detection.id||'',detection.area_cm2||'',detection.depth_cm||'',detection.volume||'',detection.volume_range||'',detection.crack_type||'',detection.area_range||'',detection.kerb_type||'',detection.condition||'',detection.length_m||'',detection.confidence||''].join(','))].join('\\n');// Create and download file\nconst blob=new Blob([csvContent],{type:'text/csv;charset=utf-8;'});const link=document.createElement('a');const url=URL.createObjectURL(blob);link.setAttribute('href',url);link.setAttribute('download',`pavement_detection_results_${new Date().toISOString().split('T')[0]}.csv`);link.style.visibility='hidden';document.body.appendChild(link);link.click();document.body.removeChild(link);};// Clear results when road classification toggle is changed\nuseEffect(()=>{if(batchResults.length>0){// Clear batch results when toggle changes to prevent showing outdated classification data\nsetBatchResults([]);setProcessedImage(null);setResults(null);}},[roadClassificationEnabled]);// Handle location permission changes\nuseEffect(()=>{if(cameraActive&&locationPermission==='unknown'){// Try to get location when camera is first activated\nhandleLocationRequest();}},[cameraActive]);// Listen for permission changes if supported\nuseEffect(()=>{let permissionWatcher=null;const watchPermissions=async()=>{try{if(navigator.permissions&&navigator.permissions.query){const permission=await navigator.permissions.query({name:'geolocation'});permissionWatcher=()=>{setLocationPermission(permission.state);if(permission.state==='granted'&&cameraActive&&coordinates==='Not Available'){handleLocationRequest();}};permission.addEventListener('change',permissionWatcher);}}catch(err){console.warn('Permission watching not supported:',err);}};watchPermissions();return()=>{if(permissionWatcher){try{const permission=navigator.permissions.query({name:'geolocation'});permission.then(p=>p.removeEventListener('change',permissionWatcher));}catch(err){console.warn('Error removing permission listener:',err);}}};},[cameraActive,coordinates]);// Force re-render when current image changes to update location display\nuseEffect(()=>{// This effect ensures the UI updates when switching between images\n// The getCurrentImageLocation function will return the correct location for the selected image\n},[currentImageIndex,imageLocationMap]);return/*#__PURE__*/_jsxs(Container,{className:\"pavement-page\",children:[/*#__PURE__*/_jsxs(Tabs,{activeKey:activeTab,onSelect:k=>setActiveTab(k),className:\"mb-3\",children:[/*#__PURE__*/_jsxs(Tab,{eventKey:\"detection\",title:\"Image Detection\",children:[/*#__PURE__*/_jsx(Card,{className:\"mb-3\",children:/*#__PURE__*/_jsxs(Card.Body,{className:\"py-3\",children:[/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{className:\"mb-1\",children:\"Detection Type\"}),/*#__PURE__*/_jsxs(Form.Select,{value:detectionType,onChange:e=>setDetectionType(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"All (Potholes + Cracks + Kerbs)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"potholes\",children:\"Potholes\"}),/*#__PURE__*/_jsx(\"option\",{value:\"cracks\",children:\"Alligator Cracks\"}),/*#__PURE__*/_jsx(\"option\",{value:\"kerbs\",children:\"Kerbs\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-start gap-2 mb-3\",children:[/*#__PURE__*/_jsx(OverlayTrigger,{trigger:\"click\",placement:\"right\",overlay:reminderPopover,rootClose:true,children:/*#__PURE__*/_jsx(\"div\",{className:\"sticky-note-icon\",style:{cursor:'pointer',display:'inline-block'},children:/*#__PURE__*/_jsx(\"img\",{src:\"/remindericon.svg\",alt:\"Image Upload Guidelines\",style:{width:'28px',height:'28px'}})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"road-classification-control\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center justify-content-between mb-1\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"me-2\",style:{fontSize:'0.9rem',fontWeight:'500',color:'#495057'},children:\"Road Classification\"}),/*#__PURE__*/_jsx(OverlayTrigger,{placement:\"right\",delay:{show:200,hide:100},overlay:/*#__PURE__*/_jsxs(Popover,{id:\"road-classification-detailed-info\",style:{maxWidth:'350px'},children:[/*#__PURE__*/_jsxs(Popover.Header,{as:\"h3\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-brain me-2 text-primary\"}),\"Road Classification Feature\"]}),/*#__PURE__*/_jsxs(Popover.Body,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-toggle-on text-success me-2\"}),/*#__PURE__*/_jsx(\"strong\",{children:\"ENABLED (ON):\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',color:'#6c757d',marginLeft:'20px'},children:[\"\\u2022 AI analyzes images for road content first\",/*#__PURE__*/_jsx(\"br\",{}),\"\\u2022 Only road images get defect detection\",/*#__PURE__*/_jsx(\"br\",{}),\"\\u2022 More accurate results, slightly slower\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-toggle-off text-secondary me-2\"}),/*#__PURE__*/_jsx(\"strong\",{children:\"DISABLED (OFF):\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',color:'#6c757d',marginLeft:'20px'},children:[\"\\u2022 All images processed directly\",/*#__PURE__*/_jsx(\"br\",{}),\"\\u2022 No road verification step\",/*#__PURE__*/_jsx(\"br\",{}),\"\\u2022 Faster processing, may have false positives\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"alert alert-info py-2 px-2 mb-0\",style:{fontSize:'11px'},children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-lightbulb me-1\"}),/*#__PURE__*/_jsx(\"strong\",{children:\"Recommendation:\"}),\" Keep enabled for mixed image types. Disable only when all images contain roads and speed is priority.\"]})]})]}),children:/*#__PURE__*/_jsx(\"span\",{className:\"info-icon-wrapper\",children:/*#__PURE__*/_jsx(\"span\",{className:\"road-classification-info-icon\",style:{fontSize:'14px',cursor:'help',color:'#007bff',display:'inline-flex',alignItems:'center',justifyContent:'center',position:'relative',zIndex:'1000',fontWeight:'bold'},children:\"i\"})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"toggle-switch me-2\",onClick:()=>setRoadClassificationEnabled(!roadClassificationEnabled),style:{width:'60px',height:'30px',backgroundColor:roadClassificationEnabled?'#28a745':'#6c757d',borderRadius:'15px',position:'relative',cursor:'pointer',transition:'background-color 0.3s ease',border:'2px solid transparent'},children:[/*#__PURE__*/_jsx(\"div\",{className:\"toggle-slider\",style:{width:'22px',height:'22px',backgroundColor:'white',borderRadius:'50%',position:'absolute',top:'2px',left:roadClassificationEnabled?'34px':'2px',transition:'left 0.3s ease',boxShadow:'0 2px 4px rgba(0,0,0,0.2)'}}),/*#__PURE__*/_jsx(\"span\",{style:{position:'absolute',top:'50%',left:roadClassificationEnabled?'8px':'32px',transform:'translateY(-50%)',fontSize:'10px',fontWeight:'600',color:'white',transition:'all 0.3s ease',userSelect:'none'},children:roadClassificationEnabled?'ON':'OFF'})]}),/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",style:{fontSize:'11px'},children:roadClassificationEnabled?\"Only road images processed\":\"All images processed\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Image Source\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex gap-2 mb-2\",children:[/*#__PURE__*/_jsx(Button,{variant:cameraActive?\"primary\":\"outline-primary\",onClick:toggleCamera,disabled:locationLoading,children:locationLoading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Spinner,{as:\"span\",animation:\"border\",size:\"sm\",role:\"status\",\"aria-hidden\":\"true\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ms-2\",children:\"Getting Location...\"})]}):cameraActive?\"Disable Camera\":\"Enable Camera\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"file-input-container\",children:[/*#__PURE__*/_jsxs(\"label\",{className:\"file-input-label\",children:[\"Upload Image\",/*#__PURE__*/_jsx(\"input\",{type:\"file\",className:\"file-input\",accept:\"image/*,.avif\",onChange:handleFileChange,ref:fileInputRef,disabled:cameraActive,multiple:true})]}),/*#__PURE__*/_jsxs(\"small\",{className:\"text-muted d-block mt-1\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-info-circle me-1\"}),\"Supports all image formats including AVIF (automatically converted to JPG for processing)\"]})]})]}),cameraActive&&/*#__PURE__*/_jsxs(\"div\",{className:\"location-status mb-3\",children:[/*#__PURE__*/_jsxs(\"small\",{className:\"text-muted\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Location Status:\"}),locationPermission==='granted'&&/*#__PURE__*/_jsx(\"span\",{className:\"text-success ms-1\",children:\"\\u2713 Enabled\"}),locationPermission==='denied'&&/*#__PURE__*/_jsx(\"span\",{className:\"text-danger ms-1\",children:\"\\u2717 Denied\"}),locationPermission==='prompt'&&/*#__PURE__*/_jsx(\"span\",{className:\"text-warning ms-1\",children:\"\\u26A0 Requesting...\"}),locationPermission==='unknown'&&/*#__PURE__*/_jsx(\"span\",{className:\"text-secondary ms-1\",children:\"? Unknown\"})]}),(coordinates!=='Not Available'||Object.keys(imagePreviewsMap).length>0)&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-1\",children:[/*#__PURE__*/_jsxs(\"small\",{className:\"text-muted\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Current Location:\"}),\" \",/*#__PURE__*/_jsx(\"span\",{className:\"text-primary\",children:coordinates})]}),Object.keys(imagePreviewsMap).length>0&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-1\",children:/*#__PURE__*/_jsxs(\"small\",{className:\"text-muted\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Selected Image Location:\"}),\" \",/*#__PURE__*/_jsx(\"span\",{className:\"text-primary\",children:getCurrentImageLocation()})]})})]}),locationError&&/*#__PURE__*/_jsxs(Alert,{variant:\"warning\",className:\"mt-2 mb-0\",style:{fontSize:'0.875rem'},children:[/*#__PURE__*/_jsx(Alert.Heading,{as:\"h6\",children:\"Location Access Issue\"}),/*#__PURE__*/_jsx(\"div\",{style:{whiteSpace:'pre-line'},children:locationError}),/*#__PURE__*/_jsx(\"hr\",{}),/*#__PURE__*/_jsx(\"div\",{className:\"d-flex justify-content-end\",children:/*#__PURE__*/_jsx(Button,{variant:\"outline-warning\",size:\"sm\",onClick:handleLocationRequest,children:\"Retry Location Access\"})})]})]})]}),cameraActive&&/*#__PURE__*/_jsxs(\"div\",{className:\"webcam-container mb-3\",children:[/*#__PURE__*/_jsx(Webcam,{audio:false,ref:webcamRef,screenshotFormat:\"image/jpeg\",className:\"webcam\",videoConstraints:{width:640,height:480,facingMode:cameraOrientation}}),isMobile&&/*#__PURE__*/_jsx(Button,{variant:\"outline-secondary\",onClick:toggleCameraOrientation,className:\"mt-2 mb-2\",size:\"sm\",children:\"Rotate Camera\"}),/*#__PURE__*/_jsx(Button,{variant:\"success\",onClick:handleCapture,className:\"mt-2\",children:\"Capture Photo\"})]}),Object.keys(imagePreviewsMap).length>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"image-preview-container mb-3\",children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Previews\"}),/*#__PURE__*/_jsx(\"div\",{className:\"image-gallery\",children:Object.entries(imagePreviewsMap).map((_ref,index)=>{let[name,preview]=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:`image-thumbnail ${index===currentImageIndex?'selected':''}`,onClick:()=>setCurrentImageIndex(index),children:[/*#__PURE__*/_jsx(\"img\",{src:preview,alt:`Preview ${index+1}`,className:\"img-thumbnail\"}),name.toLowerCase().endsWith('.avif')&&/*#__PURE__*/_jsxs(\"div\",{className:\"avif-indicator\",title:\"AVIF file - will be converted to JPG during processing\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-sync-alt text-info\"}),/*#__PURE__*/_jsx(\"small\",{className:\"ms-1\",children:\"AVIF\"})]}),/*#__PURE__*/_jsx(\"button\",{className:\"btn btn-sm btn-danger remove-image\",onClick:e=>{e.stopPropagation();const newFiles=imageFiles.filter((_,i)=>i!==index);const newPreviewsMap={...imagePreviewsMap};const newLocationMap={...imageLocationMap};delete newPreviewsMap[name];delete newLocationMap[name];setImageFiles(newFiles);setImagePreviewsMap(newPreviewsMap);setImageLocationMap(newLocationMap);if(currentImageIndex>=newFiles.length){setCurrentImageIndex(Math.max(0,newFiles.length-1));}},children:\"\\xD7\"})]},name);})}),/*#__PURE__*/_jsx(\"div\",{className:\"current-image-preview\",children:Object.values(imagePreviewsMap)[currentImageIndex]&&/*#__PURE__*/_jsx(\"img\",{src:Object.values(imagePreviewsMap)[currentImageIndex],alt:\"Current Preview\",className:\"image-preview img-fluid\"})})]}),error&&/*#__PURE__*/_jsx(Alert,{variant:\"danger\",children:error}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex gap-2 mb-3\",children:[/*#__PURE__*/_jsx(Button,{variant:\"primary\",onClick:handleProcess,disabled:Object.keys(imagePreviewsMap).length===0||loading||batchProcessing,children:loading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Spinner,{as:\"span\",animation:\"border\",size:\"sm\",role:\"status\",\"aria-hidden\":\"true\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ms-2\",children:\"Detecting...\"})]}):`Detect Current Image`}),/*#__PURE__*/_jsx(Button,{variant:\"success\",onClick:handleProcessAll,disabled:Object.keys(imagePreviewsMap).length===0||loading||batchProcessing,children:batchProcessing?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Spinner,{as:\"span\",animation:\"border\",size:\"sm\",role:\"status\",\"aria-hidden\":\"true\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"ms-2\",children:[\"Processing \",processedCount,\"/\",totalToProcess]})]}):`Process All Images`}),/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:handleReset,disabled:loading||batchProcessing,children:\"Reset\"})]})]})}),batchResults.some(result=>{var _result$detectionCoun;return result.success&&result.processed&&((_result$detectionCoun=result.detectionCounts)===null||_result$detectionCoun===void 0?void 0:_result$detectionCoun.total)>0;})&&/*#__PURE__*/_jsxs(Card,{className:\"mb-4\",children:[/*#__PURE__*/_jsx(Card.Header,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center\",children:[/*#__PURE__*/_jsxs(\"h5\",{className:\"mb-0\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-table me-2\"}),\"Detailed Detection Results\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"d-flex gap-2\",children:/*#__PURE__*/_jsxs(Button,{variant:\"success\",size:\"sm\",onClick:exportToCSV,title:\"Export results to CSV\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-download me-1\"}),\"Export CSV\"]})})]})}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex gap-2 flex-wrap\",children:[/*#__PURE__*/_jsx(Button,{variant:detectionTableFilter==='all'?'primary':'outline-primary',size:\"sm\",onClick:()=>setDetectionTableFilter('all'),children:\"All Detections\"}),/*#__PURE__*/_jsx(Button,{variant:detectionTableFilter==='potholes'?'danger':'outline-danger',size:\"sm\",onClick:()=>setDetectionTableFilter('potholes'),children:\"Potholes Only\"}),/*#__PURE__*/_jsx(Button,{variant:detectionTableFilter==='cracks'?'warning':'outline-warning',size:\"sm\",onClick:()=>setDetectionTableFilter('cracks'),children:\"Cracks Only\"}),/*#__PURE__*/_jsx(Button,{variant:detectionTableFilter==='kerbs'?'info':'outline-info',size:\"sm\",onClick:()=>setDetectionTableFilter('kerbs'),children:\"Kerbs Only\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"mb-4 detection-summary-cards\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-md-3\",children:/*#__PURE__*/_jsx(\"div\",{className:\"card bg-danger text-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card-body text-center py-2\",children:[/*#__PURE__*/_jsx(\"h6\",{className:\"mb-1\",children:\"Total Potholes\"}),/*#__PURE__*/_jsx(\"h4\",{className:\"mb-0\",children:batchResults.reduce((sum,result)=>{var _result$detectionCoun2;return sum+(((_result$detectionCoun2=result.detectionCounts)===null||_result$detectionCoun2===void 0?void 0:_result$detectionCoun2.potholes)||0);},0)})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-md-3\",children:/*#__PURE__*/_jsx(\"div\",{className:\"card bg-warning text-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card-body text-center py-2\",children:[/*#__PURE__*/_jsx(\"h6\",{className:\"mb-1\",children:\"Total Cracks\"}),/*#__PURE__*/_jsx(\"h4\",{className:\"mb-0\",children:batchResults.reduce((sum,result)=>{var _result$detectionCoun3;return sum+(((_result$detectionCoun3=result.detectionCounts)===null||_result$detectionCoun3===void 0?void 0:_result$detectionCoun3.cracks)||0);},0)})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-md-3\",children:/*#__PURE__*/_jsx(\"div\",{className:\"card bg-info text-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card-body text-center py-2\",children:[/*#__PURE__*/_jsx(\"h6\",{className:\"mb-1\",children:\"Total Kerbs\"}),/*#__PURE__*/_jsx(\"h4\",{className:\"mb-0\",children:batchResults.reduce((sum,result)=>{var _result$detectionCoun4;return sum+(((_result$detectionCoun4=result.detectionCounts)===null||_result$detectionCoun4===void 0?void 0:_result$detectionCoun4.kerbs)||0);},0)})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-md-3\",children:/*#__PURE__*/_jsx(\"div\",{className:\"card bg-success text-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card-body text-center py-2\",children:[/*#__PURE__*/_jsx(\"h6\",{className:\"mb-1\",children:\"Total Detections\"}),/*#__PURE__*/_jsx(\"h4\",{className:\"mb-0\",children:batchResults.reduce((sum,result)=>{var _result$detectionCoun5;return sum+(((_result$detectionCoun5=result.detectionCounts)===null||_result$detectionCoun5===void 0?void 0:_result$detectionCoun5.total)||0);},0)})]})})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"table-responsive detection-table-container\",children:(()=>{// Flatten all detection results into a single array\nconst allDetections=[];batchResults.forEach(result=>{if(result.success&&result.processed&&result.detectionResults){const{potholes,cracks,kerbs}=result.detectionResults;// Add potholes\nif(detectionTableFilter==='all'||detectionTableFilter==='potholes'){potholes.forEach(pothole=>{allDetections.push({...pothole,type:'Pothole',filename:result.filename,detectionType:'potholes'});});}// Add cracks\nif(detectionTableFilter==='all'||detectionTableFilter==='cracks'){cracks.forEach(crack=>{allDetections.push({...crack,type:'Crack',filename:result.filename,detectionType:'cracks'});});}// Add kerbs\nif(detectionTableFilter==='all'||detectionTableFilter==='kerbs'){kerbs.forEach(kerb=>{allDetections.push({...kerb,type:'Kerb',filename:result.filename,detectionType:'kerbs'});});}}});if(allDetections.length===0){return/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-5\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-3\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-search fa-3x text-muted\"})}),/*#__PURE__*/_jsx(\"h6\",{className:\"text-muted\",children:\"No detections found\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-muted mb-0\",children:detectionTableFilter==='all'?'No defects were detected in the processed images.':`No ${detectionTableFilter} were detected in the processed images.`})]});}return/*#__PURE__*/_jsxs(\"table\",{className:\"table table-striped table-bordered\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Original Image\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Processed Image\"}),/*#__PURE__*/_jsxs(\"th\",{style:{cursor:'pointer'},onClick:()=>handleSort('detectionType'),children:[\"Type \",sortConfig.key==='detectionType'&&/*#__PURE__*/_jsx(\"i\",{className:`fas fa-sort-${sortConfig.direction==='asc'?'up':'down'} ms-1`})]}),/*#__PURE__*/_jsx(\"th\",{children:\"ID\"}),(detectionTableFilter==='all'||detectionTableFilter==='potholes')&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"th\",{style:{cursor:'pointer'},onClick:()=>handleSort('area_cm2'),children:[\"Area (cm\\xB2) \",sortConfig.key==='area_cm2'&&/*#__PURE__*/_jsx(\"i\",{className:`fas fa-sort-${sortConfig.direction==='asc'?'up':'down'} ms-1`})]}),/*#__PURE__*/_jsxs(\"th\",{style:{cursor:'pointer'},onClick:()=>handleSort('depth_cm'),children:[\"Depth (cm) \",sortConfig.key==='depth_cm'&&/*#__PURE__*/_jsx(\"i\",{className:`fas fa-sort-${sortConfig.direction==='asc'?'up':'down'} ms-1`})]}),/*#__PURE__*/_jsxs(\"th\",{style:{cursor:'pointer'},onClick:()=>handleSort('volume'),children:[\"Volume (cm\\xB3) \",sortConfig.key==='volume'&&/*#__PURE__*/_jsx(\"i\",{className:`fas fa-sort-${sortConfig.direction==='asc'?'up':'down'} ms-1`})]}),/*#__PURE__*/_jsx(\"th\",{children:\"Volume Range\"})]}),(detectionTableFilter==='all'||detectionTableFilter==='cracks')&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"th\",{style:{cursor:'pointer'},onClick:()=>handleSort('crack_type'),children:[\"Crack Type \",sortConfig.key==='crack_type'&&/*#__PURE__*/_jsx(\"i\",{className:`fas fa-sort-${sortConfig.direction==='asc'?'up':'down'} ms-1`})]}),/*#__PURE__*/_jsxs(\"th\",{style:{cursor:'pointer'},onClick:()=>handleSort('area_cm2'),children:[\"Area (cm\\xB2) \",sortConfig.key==='area_cm2'&&/*#__PURE__*/_jsx(\"i\",{className:`fas fa-sort-${sortConfig.direction==='asc'?'up':'down'} ms-1`})]}),/*#__PURE__*/_jsx(\"th\",{children:\"Area Range\"})]}),(detectionTableFilter==='all'||detectionTableFilter==='kerbs')&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"th\",{style:{cursor:'pointer'},onClick:()=>handleSort('kerb_type'),children:[\"Kerb Type \",sortConfig.key==='kerb_type'&&/*#__PURE__*/_jsx(\"i\",{className:`fas fa-sort-${sortConfig.direction==='asc'?'up':'down'} ms-1`})]}),/*#__PURE__*/_jsxs(\"th\",{style:{cursor:'pointer'},onClick:()=>handleSort('condition'),children:[\"Condition \",sortConfig.key==='condition'&&/*#__PURE__*/_jsx(\"i\",{className:`fas fa-sort-${sortConfig.direction==='asc'?'up':'down'} ms-1`})]}),/*#__PURE__*/_jsxs(\"th\",{style:{cursor:'pointer'},onClick:()=>handleSort('length_m'),children:[\"Length (m) \",sortConfig.key==='length_m'&&/*#__PURE__*/_jsx(\"i\",{className:`fas fa-sort-${sortConfig.direction==='asc'?'up':'down'} ms-1`})]})]}),/*#__PURE__*/_jsxs(\"th\",{style:{cursor:'pointer'},onClick:()=>handleSort('confidence'),children:[\"Confidence \",sortConfig.key==='confidence'&&/*#__PURE__*/_jsx(\"i\",{className:`fas fa-sort-${sortConfig.direction==='asc'?'up':'down'} ms-1`})]})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:sortDetections(allDetections).map((detection,index)=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{style:{width:'120px',textAlign:'center'},children:(()=>{const result=batchResults.find(r=>r.filename===detection.filename);const originalImage=result===null||result===void 0?void 0:result.originalImage;return originalImage?/*#__PURE__*/_jsxs(\"div\",{className:\"image-thumbnail-container\",onClick:()=>handleThumbnailClick({originalImage:originalImage,processedImage:result===null||result===void 0?void 0:result.processedImage,isRoad:result===null||result===void 0?void 0:result.isRoad,filename:detection.filename}),title:\"Click to enlarge \\uD83D\\uDD0D\",children:[/*#__PURE__*/_jsx(\"img\",{src:originalImage,alt:\"Original\",className:\"image-thumbnail\"}),/*#__PURE__*/_jsx(\"div\",{className:\"thumbnail-overlay\",children:\"\\uD83D\\uDD0D\"})]}):/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:\"No image\"});})()}),/*#__PURE__*/_jsx(\"td\",{style:{width:'120px',textAlign:'center'},children:(()=>{const result=batchResults.find(r=>r.filename===detection.filename);const processedImage=result===null||result===void 0?void 0:result.processedImage;const originalImage=result===null||result===void 0?void 0:result.originalImage;return processedImage&&result!==null&&result!==void 0&&result.isRoad?/*#__PURE__*/_jsxs(\"div\",{className:\"image-thumbnail-container\",onClick:()=>handleThumbnailClick({originalImage:originalImage,processedImage:processedImage,isRoad:result.isRoad,filename:detection.filename}),title:\"Click to enlarge \\uD83D\\uDD0D\",children:[/*#__PURE__*/_jsx(\"img\",{src:processedImage,alt:\"Processed\",className:\"image-thumbnail\"}),/*#__PURE__*/_jsx(\"div\",{className:\"thumbnail-overlay\",children:\"\\uD83D\\uDD0D\"})]}):/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:\"No processed image\"});})()}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"span\",{className:`badge ${detection.detectionType==='potholes'?'bg-danger':detection.detectionType==='cracks'?'bg-warning':'bg-info'}`,children:detection.type})}),/*#__PURE__*/_jsx(\"td\",{children:detection.pothole_id||detection.crack_id||detection.kerb_id||index+1}),(detectionTableFilter==='all'||detectionTableFilter==='potholes')&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"td\",{children:detection.area_cm2?detection.area_cm2.toFixed(2):'N/A'}),/*#__PURE__*/_jsx(\"td\",{children:detection.depth_cm?detection.depth_cm.toFixed(2):'N/A'}),/*#__PURE__*/_jsx(\"td\",{children:detection.volume?detection.volume.toFixed(2):'N/A'}),/*#__PURE__*/_jsx(\"td\",{children:detection.volume_range||'N/A'})]}),(detectionTableFilter==='all'||detectionTableFilter==='cracks')&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"td\",{children:detection.crack_type||'N/A'}),/*#__PURE__*/_jsx(\"td\",{children:detection.area_cm2?detection.area_cm2.toFixed(2):'N/A'}),/*#__PURE__*/_jsx(\"td\",{children:detection.area_range||'N/A'})]}),(detectionTableFilter==='all'||detectionTableFilter==='kerbs')&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"td\",{children:detection.kerb_type||'N/A'}),/*#__PURE__*/_jsx(\"td\",{children:detection.condition||'N/A'}),/*#__PURE__*/_jsx(\"td\",{children:detection.length_m?detection.length_m.toFixed(2):'N/A'})]}),/*#__PURE__*/_jsx(\"td\",{children:detection.confidence?(detection.confidence*100).toFixed(1)+'%':'N/A'})]},`${detection.filename}-${detection.detectionType}-${index}`))})]});})()})]})]}),batchProcessing&&/*#__PURE__*/_jsx(\"div\",{className:\"batch-processing-status mt-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"me-3\",children:/*#__PURE__*/_jsx(Spinner,{animation:\"border\",size:\"sm\",role:\"status\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"h6\",{className:\"mb-1\",children:[\"Processing images: \",processedCount,\"/\",totalToProcess]}),/*#__PURE__*/_jsx(\"div\",{className:\"progress\",style:{height:'10px'},children:/*#__PURE__*/_jsx(\"div\",{className:\"progress-bar\",role:\"progressbar\",style:{width:`${processedCount/totalToProcess*100}%`},\"aria-valuenow\":processedCount,\"aria-valuemin\":\"0\",\"aria-valuemax\":totalToProcess})})]})]})}),!batchProcessing&&batchResults.length>0&&(()=>{const totalImages=batchResults.length;const successfulImages=batchResults.filter(r=>r.success).length;const failedImages=batchResults.filter(r=>!r.success).length;let alertVariant='light';let alertClass='';if(batchResults.some(result=>result.wasClassificationEnabled)){// When classification was enabled during processing, use road/non-road logic\n// When classification is enabled, use road/non-road logic\nconst nonRoadImages=batchResults.filter(r=>!r.isRoad).length;const nonRoadPercentage=totalImages>0?nonRoadImages/totalImages*100:0;if(totalImages>0){if(nonRoadPercentage===0){// 100% road detection - Green\nalertVariant='success';}else if(nonRoadPercentage===100){// 100% non-road detection - Red\nalertVariant='danger';}else{// Combined detection (mixed results) - Light Orange\nalertVariant='warning';alertClass='summary-light-orange';}}}else{// When classification is disabled, use success/failure logic\nif(failedImages===0){// All successful - Green\nalertVariant='success';}else if(successfulImages===0){// All failed - Red\nalertVariant='danger';}else{// Mixed results - Warning\nalertVariant='warning';}}return/*#__PURE__*/_jsx(\"div\",{className:\"batch-complete-status mt-4\",children:/*#__PURE__*/_jsxs(Alert,{variant:alertVariant,className:alertClass,children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-check-circle me-2\"}),\"Processed \",batchResults.length,\" images.\",roadClassificationEnabled?/*#__PURE__*/_jsxs(_Fragment,{children:[batchResults.filter(r=>r.success&&r.processed).length,\" road images processed,\",batchResults.filter(r=>r.success&&!r.processed).length,\" non-road images detected,\",batchResults.filter(r=>!r.success).length,\" failed.\"]}):/*#__PURE__*/_jsxs(_Fragment,{children:[batchResults.filter(r=>r.success).length,\" images processed successfully,\",batchResults.filter(r=>!r.success).length,\" failed.\"]})]})});})(),!batchProcessing&&batchResults.length>0&&batchResults.some(result=>result.wasClassificationEnabled)&&/*#__PURE__*/_jsx(\"div\",{className:\"image-status-table mt-4\",children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Card.Header,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center\",children:[/*#__PURE__*/_jsx(\"h5\",{className:\"mb-0\",children:\"Image Processing Status\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-buttons\",children:[/*#__PURE__*/_jsx(Button,{variant:imageFilter==='all'?'primary':'outline-primary',size:\"sm\",className:\"me-2\",onClick:()=>setImageFilter('all'),children:\"Show All Images\"}),/*#__PURE__*/_jsx(Button,{variant:imageFilter==='road'?'success':'outline-success',size:\"sm\",className:\"me-2\",onClick:()=>setImageFilter('road'),children:\"Show Only Road Images\"}),/*#__PURE__*/_jsx(Button,{variant:imageFilter==='non-road'?'danger':'outline-danger',size:\"sm\",onClick:()=>setImageFilter('non-road'),children:\"Show Only Non-Road Images\"})]})]})}),/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsx(\"div\",{className:\"table-responsive\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"table table-striped\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Image\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Detection Status\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:batchResults.filter(result=>{if(imageFilter==='road')return result.isRoad;if(imageFilter==='non-road')return!result.isRoad;return true;// 'all'\n}).map((result,index)=>{const filename=result.filename;const isRoad=result.isRoad;// Get image from stored processed data\nlet imagePreview=null;let imageData=null;if(processedImagesData[filename]){// Use stored processed image data\nimagePreview=processedImagesData[filename].originalImage;imageData=processedImagesData[filename];console.log('Found stored data for:',filename,'hasImage:',!!imagePreview);}else if(imagePreviewsMap[filename]){// Fallback to current preview (for any remaining unprocessed images)\nimagePreview=imagePreviewsMap[filename];imageData={originalImage:imagePreview,processedImage:null,results:null,isRoad:isRoad};console.log('Using fallback data for:',filename,'hasImage:',!!imagePreview);}else{console.log('No image data found for:',filename);}return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[imagePreview?/*#__PURE__*/_jsx(\"img\",{src:imagePreview,alt:`Thumbnail ${index+1}`,className:\"img-thumbnail me-2\",style:{width:'60px',height:'60px',objectFit:'cover',cursor:'pointer'},onClick:()=>handleThumbnailClick(imageData),title:\"Click to view full size\"}):/*#__PURE__*/_jsx(\"div\",{className:\"img-thumbnail me-2 d-flex align-items-center justify-content-center\",style:{width:'60px',height:'60px',backgroundColor:'#f8f9fa',border:'1px solid #dee2e6'},children:/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:\"No Image\"})}),/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:filename})]})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"span\",{className:`badge ${isRoad?'bg-success':'bg-danger'}`,children:isRoad?'Road':'Non-Road'})})]},filename);})})]})})})]})})]}),/*#__PURE__*/_jsx(Tab,{eventKey:\"video\",title:\"Video Detection\",children:/*#__PURE__*/_jsx(VideoDefectDetection,{})}),/*#__PURE__*/_jsx(Tab,{eventKey:\"information\",title:\"Information\",children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h4\",{children:\"About Pavement Analysis\"}),/*#__PURE__*/_jsx(\"p\",{children:\"The Pavement Analysis module uses advanced computer vision to detect and analyze various types of pavement defects and features:\"}),/*#__PURE__*/_jsx(\"h5\",{children:\"1. Potholes\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Potholes are bowl-shaped holes of various sizes in the road surface that can be a serious hazard to vehicles. The system detects potholes and calculates:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"Area in square centimeters\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Depth in centimeters\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Volume\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Classification by size (Small, Medium, Large)\"})]}),/*#__PURE__*/_jsx(\"h5\",{children:\"2. Alligator Cracks\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Alligator cracks are a series of interconnected cracks creating a pattern resembling an alligator's scales. These indicate underlying structural weakness. The system identifies multiple types of cracks including:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"Alligator Cracks\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Edge Cracks\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Hairline Cracks\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Longitudinal Cracks\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Transverse Cracks\"})]}),/*#__PURE__*/_jsx(\"h5\",{children:\"3. Kerbs\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Kerbs are raised edges along a street or path that define boundaries between roadways and other areas. The system identifies different kerb conditions including:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"Normal/Good Kerbs - Structurally sound and properly visible\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Faded Kerbs - Reduced visibility due to worn paint or weathering\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Damaged Kerbs - Physically damaged or broken kerbs requiring repair\"})]}),/*#__PURE__*/_jsx(\"h5\",{children:\"Location Services & GPS Data\"}),/*#__PURE__*/_jsx(\"p\",{children:\"When using the live camera option, the application can capture GPS coordinates to provide precise geolocation data for detected defects. This helps in:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"Accurately mapping defect locations\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Creating location-based reports\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Enabling field teams to find specific issues\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Tracking defect patterns by geographic area\"})]}),/*#__PURE__*/_jsx(\"h6\",{children:\"Location Requirements:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Secure Connection:\"}),\" Location services require HTTPS\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Browser Permissions:\"}),\" You must allow location access when prompted\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Safari Users:\"}),\" Enable location services in Safari settings\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Mobile Devices:\"}),\" Ensure location services are enabled in device settings\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"alert alert-info\",children:[/*#__PURE__*/_jsxs(\"h6\",{children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-info-circle me-2\"}),\"Troubleshooting Location Issues\"]}),/*#__PURE__*/_jsx(\"p\",{children:/*#__PURE__*/_jsx(\"strong\",{children:\"If location access is denied:\"})}),/*#__PURE__*/_jsxs(\"ul\",{className:\"mb-2\",children:[/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Safari:\"}),\" Settings \\u2192 Privacy & Security \\u2192 Location Services\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Chrome:\"}),\" Settings \\u2192 Privacy and security \\u2192 Site Settings \\u2192 Location\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Firefox:\"}),\" Settings \\u2192 Privacy & Security \\u2192 Permissions \\u2192 Location\"]})]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"On mobile devices:\"}),\" Also check your device's location settings and ensure the browser has location permission.\"]})]}),/*#__PURE__*/_jsx(\"h5\",{children:\"How to Use This Module\"}),/*#__PURE__*/_jsxs(\"ol\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"Select the detection type (Potholes, Alligator Cracks, or Kerbs)\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Upload an image or use the camera to capture a photo\"}),/*#__PURE__*/_jsx(\"li\",{children:\"If using the camera, allow location access when prompted for GPS coordinates\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Click the Detect button to analyze the image\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Review the detection results and measurements\"})]}),/*#__PURE__*/_jsx(\"h5\",{children:\"Supported Image Formats\"}),/*#__PURE__*/_jsx(\"p\",{children:\"The system supports all common image formats including:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"JPEG/JPG\"}),\" - Standard format, optimal for processing\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"PNG\"}),\" - Lossless format, good for detailed images\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"AVIF\"}),\" - Modern format, automatically converted to JPG for processing\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"WebP\"}),\" - Google's format, also automatically converted\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Other formats\"}),\" - BMP, TIFF, etc. are also supported\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"alert alert-info\",children:[/*#__PURE__*/_jsxs(\"h6\",{children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-info-circle me-2\"}),\"Automatic Format Conversion\"]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-0\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"AVIF and WebP images\"}),\" are automatically converted to JPG format during processing to ensure compatibility with the YOLO detection models. This conversion maintains image quality while ensuring reliable detection results.\"]})]}),/*#__PURE__*/_jsx(\"p\",{children:\"The detected defects are automatically recorded in the database for tracking and analysis in the Dashboard module.\"})]})})})]}),/*#__PURE__*/_jsxs(Modal,{show:showImageModal,onHide:()=>setShowImageModal(false),size:\"lg\",centered:true,children:[/*#__PURE__*/_jsx(Modal.Header,{closeButton:true,children:/*#__PURE__*/_jsxs(Modal.Title,{children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-image me-2\"}),\"Image View \",(selectedImageData===null||selectedImageData===void 0?void 0:selectedImageData.filename)&&/*#__PURE__*/_jsxs(\"small\",{className:\"text-muted\",children:[\"- \",selectedImageData.filename]})]})}),/*#__PURE__*/_jsx(Modal.Body,{children:selectedImageData&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsxs(\"h6\",{className:\"mb-3\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-camera me-2\"}),\"Original Image\"]}),/*#__PURE__*/_jsx(\"img\",{src:selectedImageData.originalImage,alt:\"Original Image\",className:\"img-fluid\",style:{maxHeight:'400px',borderRadius:'8px',border:'2px solid #dee2e6',boxShadow:'0 4px 8px rgba(0,0,0,0.1)'}})]}),selectedImageData.processedImage&&selectedImageData.isRoad&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4\",children:[/*#__PURE__*/_jsxs(\"h6\",{className:\"mb-3\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-search me-2\"}),\"Processed Image (Detection Results)\"]}),/*#__PURE__*/_jsx(\"img\",{src:selectedImageData.processedImage,alt:\"Processed Image\",className:\"img-fluid\",style:{maxHeight:'400px',borderRadius:'8px',border:'2px solid #28a745',boxShadow:'0 4px 8px rgba(0,0,0,0.1)'}})]}),!selectedImageData.isRoad&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-3\",children:/*#__PURE__*/_jsxs(Alert,{variant:\"info\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-info-circle me-2\"}),\"This image was classified as non-road and therefore no defect detection was performed.\"]})})]})}),/*#__PURE__*/_jsx(Modal.Footer,{children:/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:()=>setShowImageModal(false),children:\"Close\"})})]})]});};// Add CSS styles for the enhanced detection table\nconst styles=`\n  .detection-table-container {\n    max-height: 600px;\n    overflow-y: auto;\n  }\n\n  .detection-table-container th {\n    position: sticky;\n    top: 0;\n    background-color: #f8f9fa;\n    z-index: 10;\n  }\n\n  .detection-table-container th:hover {\n    background-color: #e9ecef;\n  }\n\n  .detection-summary-cards .card {\n    transition: transform 0.2s ease-in-out;\n  }\n\n  .detection-summary-cards .card:hover {\n    transform: translateY(-2px);\n  }\n\n  .table-responsive {\n    border-radius: 8px;\n    box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  }\n\n  .badge {\n    font-size: 0.75em;\n  }\n\n  .avif-indicator {\n    position: absolute;\n    top: 5px;\n    left: 5px;\n    background-color: rgba(23, 162, 184, 0.9);\n    color: white;\n    padding: 2px 6px;\n    border-radius: 12px;\n    font-size: 10px;\n    display: flex;\n    align-items: center;\n    z-index: 5;\n  }\n\n  .image-thumbnail {\n    position: relative;\n  }\n\n  @media (max-width: 768px) {\n    .detection-summary-cards .col-md-3 {\n      margin-bottom: 1rem;\n    }\n\n    .d-flex.gap-2.flex-wrap {\n      flex-direction: column;\n    }\n\n    .d-flex.gap-2.flex-wrap .btn {\n      margin-bottom: 0.5rem;\n    }\n  }\n`;// Inject styles into the document head\nif(typeof document!=='undefined'){const styleSheet=document.createElement('style');styleSheet.innerText=styles;document.head.appendChild(styleSheet);}export default Pavement;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Container", "Card", "<PERSON><PERSON>", "Form", "Tabs", "Tab", "<PERSON><PERSON>", "Spinner", "OverlayTrigger", "Popover", "Modal", "axios", "Webcam", "useResponsive", "VideoDefectDetection", "validateMultipleFiles", "showFileValidationError", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Pavement", "activeTab", "setActiveTab", "detectionType", "setDetectionType", "imageFiles", "setImageFiles", "imagePreviewsMap", "setImagePreviewsMap", "imageLocationMap", "setImageLocationMap", "currentImageIndex", "setCurrentImageIndex", "processedImage", "setProcessedImage", "results", "setResults", "loading", "setLoading", "error", "setError", "cameraActive", "setCameraActive", "coordinates", "setCoordinates", "cameraOrientation", "setCameraOrientation", "locationPermission", "setLocationPermission", "locationError", "setLocationError", "locationLoading", "setLocationLoading", "batchResults", "setBatchResults", "batchProcessing", "setBatchProcessing", "processedCount", "setProcessedCount", "processedImagesData", "setProcessedImagesData", "showClassificationModal", "setShowClassificationModal", "classificationError", "setClassificationError", "totalToProcess", "setTotalToProcess", "showImageModal", "setShowImageModal", "selectedImageData", "setSelectedImageData", "imageFilter", "setImageFilter", "roadClassificationEnabled", "setRoadClassificationEnabled", "detectionTableFilter", "setDetectionTableFilter", "sortConfig", "setSortConfig", "key", "direction", "webcamRef", "fileInputRef", "isMobile", "reminderPopover", "id", "style", "max<PERSON><PERSON><PERSON>", "children", "Header", "as", "Body", "marginBottom", "paddingLeft", "checkLocationPermission", "navigator", "permissions", "query", "permission", "name", "state", "err", "console", "warn", "requestLocation", "Promise", "resolve", "reject", "geolocation", "Error", "window", "isSecureContext", "options", "enableHighAccuracy", "timeout", "maximumAge", "getCurrentPosition", "position", "errorMessage", "code", "PERMISSION_DENIED", "POSITION_UNAVAILABLE", "TIMEOUT", "message", "handleLocationRequest", "permissionState", "errorMsg", "latitude", "longitude", "coords", "formattedCoords", "toFixed", "log", "accuracy", "includes", "handleFileChange", "e", "files", "Array", "from", "target", "length", "validation", "<PERSON><PERSON><PERSON><PERSON>", "value", "for<PERSON>ach", "file", "reader", "FileReader", "onloadend", "prev", "result", "readAsDataURL", "toLowerCase", "endsWith", "handleCapture", "imageSrc", "current", "getScreenshot", "timestamp", "Date", "toISOString", "filename", "captureCoordinates", "getCurrentImageLocation", "Object", "keys", "currentFilename", "toggleCamera", "newCameraState", "toggleCameraOrientation", "handleClassificationError", "handleProcess", "userString", "sessionStorage", "getItem", "user", "JSON", "parse", "currentImagePreview", "values", "imageCoordinates", "filenames", "requestData", "image", "username", "role", "skip_road_classification", "endpoint", "response", "post", "data", "success", "_response$data$classi", "isProcessed", "processed", "isRoad", "classification", "is_road", "processed_image", "detectionResults", "potholes", "cracks", "kerbs", "batchResult", "originalImage", "wasClassificationEnabled", "detectionCounts", "total", "isClassificationError", "_error$response", "_error$response$data", "handleProcessAll", "i", "imageData", "_response$data$classi2", "push", "_error$response2", "_error$response2$data", "setTimeout", "processedRoadImages", "filter", "r", "firstProcessedRoadImage", "processedData", "handleReset", "handleThumbnailClick", "handleSort", "sortDetections", "detections", "sort", "a", "b", "aValue", "bValue", "localeCompare", "exportToCSV", "allDetections", "pothole", "type", "pothole_id", "area_cm2", "depth_cm", "volume", "volume_range", "crack_type", "area_range", "kerb_type", "condition", "length_m", "confidence", "crack", "crack_id", "kerb", "kerb_id", "alert", "headers", "csv<PERSON><PERSON>nt", "join", "map", "detection", "blob", "Blob", "link", "document", "createElement", "url", "URL", "createObjectURL", "setAttribute", "split", "visibility", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "permissionWatcher", "watchPermissions", "addEventListener", "then", "p", "removeEventListener", "className", "active<PERSON><PERSON>", "onSelect", "k", "eventKey", "title", "Group", "Label", "Select", "onChange", "trigger", "placement", "overlay", "rootClose", "cursor", "display", "src", "alt", "width", "height", "fontSize", "fontWeight", "color", "delay", "show", "hide", "marginLeft", "alignItems", "justifyContent", "zIndex", "onClick", "backgroundColor", "borderRadius", "transition", "border", "top", "left", "boxShadow", "transform", "userSelect", "variant", "disabled", "animation", "size", "accept", "ref", "multiple", "Heading", "whiteSpace", "audio", "screenshotFormat", "videoConstraints", "facingMode", "entries", "_ref", "index", "preview", "stopPropagation", "newFiles", "_", "newPreviewsMap", "newLocationMap", "Math", "max", "some", "_result$detectionCoun", "reduce", "sum", "_result$detectionCoun2", "_result$detectionCoun3", "_result$detectionCoun4", "_result$detectionCoun5", "textAlign", "find", "totalImages", "successfulImages", "failedImages", "alertVariant", "alertClass", "nonRoadImages", "nonRoadPercentage", "imagePreview", "objectFit", "onHide", "centered", "closeButton", "Title", "maxHeight", "Footer", "styles", "styleSheet", "innerText", "head"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/pages/Pavement.js"], "sourcesContent": ["\r\nimport React, { useState, useRef, useEffect } from 'react';\r\nimport { <PERSON><PERSON><PERSON>, <PERSON>, But<PERSON>, Form, Tabs, Tab, <PERSON><PERSON>, Spinner, OverlayTrigger, Popover, Modal } from 'react-bootstrap';\r\nimport axios from 'axios';\r\nimport Webcam from 'react-webcam';\r\nimport './Pavement.css';\r\nimport useResponsive from '../hooks/useResponsive';\r\nimport VideoDefectDetection from '../components/VideoDefectDetection';\r\nimport { validateMultipleFiles, showFileValidationError } from '../utils/fileValidation';\r\n\r\n\r\nconst Pavement = () => {\r\n  const [activeTab, setActiveTab] = useState('detection');\r\n  const [detectionType, setDetectionType] = useState('all');\r\n  const [imageFiles, setImageFiles] = useState([]);\r\n  const [imagePreviewsMap, setImagePreviewsMap] = useState({});\r\n  const [imageLocationMap, setImageLocationMap] = useState({});\r\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\r\n  const [processedImage, setProcessedImage] = useState(null);\r\n  const [results, setResults] = useState(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [cameraActive, setCameraActive] = useState(false);\r\n  const [coordinates, setCoordinates] = useState('Not Available');\r\n  const [cameraOrientation, setCameraOrientation] = useState('environment');\r\n  const [locationPermission, setLocationPermission] = useState('unknown');\r\n  const [locationError, setLocationError] = useState('');\r\n  const [locationLoading, setLocationLoading] = useState(false);\r\n  \r\n  // Add state for batch processing results\r\n  const [batchResults, setBatchResults] = useState([]);\r\n  const [batchProcessing, setBatchProcessing] = useState(false);\r\n  const [processedCount, setProcessedCount] = useState(0);\r\n  \r\n  // Add state for storing processed images for results table\r\n  const [processedImagesData, setProcessedImagesData] = useState({});\r\n\r\n  // Add state for classification error modal\r\n  const [showClassificationModal, setShowClassificationModal] = useState(false);\r\n  const [classificationError, setClassificationError] = useState('');\r\n  const [totalToProcess, setTotalToProcess] = useState(0);\r\n  \r\n  // Add state for image modal\r\n  const [showImageModal, setShowImageModal] = useState(false);\r\n  const [selectedImageData, setSelectedImageData] = useState(null);\r\n\r\n  // Add state for image status table filtering\r\n  const [imageFilter, setImageFilter] = useState('all'); // 'all', 'road', 'non-road'\r\n\r\n\r\n\r\n  // Add state for road classification toggle (default to false for better user experience)\r\n  const [roadClassificationEnabled, setRoadClassificationEnabled] = useState(false);\r\n\r\n  // Add state for enhanced detection results table\r\n  const [detectionTableFilter, setDetectionTableFilter] = useState('all'); // 'all', 'potholes', 'cracks', 'kerbs'\r\n  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });\r\n\r\n  // Auto-clear is always enabled - no toggle needed\r\n  \r\n  const webcamRef = useRef(null);\r\n  const fileInputRef = useRef(null);\r\n  const { isMobile } = useResponsive();\r\n\r\n  // Create the popover content\r\n  const reminderPopover = (\r\n    <Popover id=\"reminder-popover\" style={{ maxWidth: '300px' }}>\r\n      <Popover.Header as=\"h3\">📸 Image Upload Guidelines</Popover.Header>\r\n      <Popover.Body>\r\n        <p style={{ marginBottom: '10px' }}>\r\n          Please ensure your uploaded images are:\r\n        </p>\r\n        <ul style={{ marginBottom: '0', paddingLeft: '20px' }}>\r\n          <li>Focused directly on the road surface</li>\r\n          <li>Well-lit and clear</li>\r\n          <li>Showing the entire area of concern</li>\r\n          <li>Taken from a reasonable distance to capture context</li>\r\n        </ul>\r\n      </Popover.Body>\r\n    </Popover>\r\n  );\r\n\r\n  // Safari-compatible geolocation permission check\r\n  const checkLocationPermission = async () => {\r\n    if (!navigator.permissions || !navigator.permissions.query) {\r\n      // Fallback for older browsers\r\n      return 'prompt';\r\n    }\r\n    \r\n    try {\r\n      const permission = await navigator.permissions.query({ name: 'geolocation' });\r\n      return permission.state;\r\n    } catch (err) {\r\n      console.warn('Permission API not supported or failed:', err);\r\n      return 'prompt';\r\n    }\r\n  };\r\n\r\n  // Safari-compatible geolocation request\r\n  const requestLocation = () => {\r\n    return new Promise((resolve, reject) => {\r\n      // Check if geolocation is supported\r\n      if (!navigator.geolocation) {\r\n        reject(new Error('Geolocation is not supported by this browser'));\r\n        return;\r\n      }\r\n\r\n      // Check if we're in a secure context (HTTPS)\r\n      if (!window.isSecureContext) {\r\n        reject(new Error('Geolocation requires a secure context (HTTPS)'));\r\n        return;\r\n      }\r\n\r\n      const options = {\r\n        enableHighAccuracy: true,\r\n        timeout: 15000, // 15 seconds timeout\r\n        maximumAge: 60000 // Accept cached position up to 1 minute old\r\n      };\r\n\r\n      navigator.geolocation.getCurrentPosition(\r\n        (position) => {\r\n          resolve(position);\r\n        },\r\n        (error) => {\r\n          let errorMessage = 'Unable to retrieve location';\r\n          \r\n          switch (error.code) {\r\n            case error.PERMISSION_DENIED:\r\n              errorMessage = 'Location access denied. Please enable location permissions in your browser settings.';\r\n              break;\r\n            case error.POSITION_UNAVAILABLE:\r\n              errorMessage = 'Location information is unavailable. Please try again.';\r\n              break;\r\n            case error.TIMEOUT:\r\n              errorMessage = 'Location request timed out. Please try again.';\r\n              break;\r\n            default:\r\n              errorMessage = `Location error: ${error.message}`;\r\n              break;\r\n          }\r\n          \r\n          reject(new Error(errorMessage));\r\n        },\r\n        options\r\n      );\r\n    });\r\n  };\r\n\r\n  // Enhanced location handler with Safari-specific fixes\r\n  const handleLocationRequest = async () => {\r\n    setLocationLoading(true);\r\n    setLocationError('');\r\n    \r\n    try {\r\n      // First check permission state\r\n      const permissionState = await checkLocationPermission();\r\n      setLocationPermission(permissionState);\r\n      \r\n      // If permission is denied, provide user guidance\r\n      if (permissionState === 'denied') {\r\n        const errorMsg = 'Location access denied. To enable location access:\\n' +\r\n                        '• Safari: Settings > Privacy & Security > Location Services\\n' +\r\n                        '• Chrome: Settings > Privacy > Location\\n' +\r\n                        '• Firefox: Settings > Privacy > Location\\n' +\r\n                        'Then refresh this page and try again.';\r\n        setLocationError(errorMsg);\r\n        setCoordinates('Permission Denied');\r\n        return;\r\n      }\r\n      \r\n      // Request location\r\n      const position = await requestLocation();\r\n      const { latitude, longitude } = position.coords;\r\n      \r\n      // Format coordinates with better precision\r\n      const formattedCoords = `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;\r\n      setCoordinates(formattedCoords);\r\n      setLocationPermission('granted');\r\n      setLocationError('');\r\n      \r\n      console.log('Location acquired:', { latitude, longitude, accuracy: position.coords.accuracy });\r\n      \r\n    } catch (error) {\r\n      console.error('Location request failed:', error);\r\n      setLocationError(error.message);\r\n      setCoordinates('Location Error');\r\n      \r\n      // Update permission state based on error\r\n      if (error.message.includes('denied')) {\r\n        setLocationPermission('denied');\r\n      }\r\n    } finally {\r\n      setLocationLoading(false);\r\n    }\r\n  };\r\n\r\n  // Handle multiple file input change\r\n  const handleFileChange = (e) => {\r\n    const files = Array.from(e.target.files);\r\n    if (files.length > 0) {\r\n      // Validate files before processing\r\n      const validation = validateMultipleFiles(files, 'image', 'pavement_detection');\r\n      if (!validation.isValid) {\r\n        showFileValidationError(validation.errorMessage, setError);\r\n        // Clear the file input\r\n        if (e.target) {\r\n          e.target.value = '';\r\n        }\r\n        return;\r\n      }\r\n\r\n      // Clear any previous errors\r\n      setError('');\r\n\r\n      setImageFiles([...imageFiles, ...files]);\r\n\r\n      // Create previews and location data for each file\r\n      files.forEach(file => {\r\n        const reader = new FileReader();\r\n        reader.onloadend = () => {\r\n          setImagePreviewsMap(prev => ({\r\n            ...prev,\r\n            [file.name]: reader.result\r\n          }));\r\n        };\r\n        reader.readAsDataURL(file);\r\n        \r\n        // Store location as \"Not Available\" for uploaded files\r\n        setImageLocationMap(prev => ({\r\n          ...prev,\r\n          [file.name]: 'Not Available'\r\n        }));\r\n        \r\n        // Show AVIF conversion notice if applicable\r\n        if (file.name.toLowerCase().endsWith('.avif')) {\r\n          console.log(`AVIF file detected: ${file.name} - will be automatically converted to JPG during processing`);\r\n        }\r\n      });\r\n      \r\n      // Reset results\r\n      setProcessedImage(null);\r\n      setResults(null);\r\n      setError('');\r\n    }\r\n  };\r\n\r\n  // Handle camera capture with location validation\r\n  const handleCapture = async () => {\r\n    const imageSrc = webcamRef.current.getScreenshot();\r\n    if (imageSrc) {\r\n      // If we don't have location data, try to get it before capturing\r\n      if (coordinates === 'Not Available' || coordinates === 'Location Error') {\r\n        await handleLocationRequest();\r\n      }\r\n      \r\n      const timestamp = new Date().toISOString();\r\n      const filename = `camera_capture_${timestamp}.jpg`;\r\n      const captureCoordinates = coordinates; // Capture current coordinates\r\n      \r\n      setImageFiles([...imageFiles, filename]);\r\n      setImagePreviewsMap(prev => ({\r\n        ...prev,\r\n        [filename]: imageSrc\r\n      }));\r\n      setImageLocationMap(prev => ({\r\n        ...prev,\r\n        [filename]: captureCoordinates\r\n      }));\r\n      setCurrentImageIndex(imageFiles.length);\r\n      \r\n      setProcessedImage(null);\r\n      setResults(null);\r\n      setError('');\r\n      \r\n      // Log capture with current coordinates\r\n      console.log('Photo captured with coordinates:', captureCoordinates);\r\n    }\r\n  };\r\n\r\n  // Get location data for currently selected image\r\n  const getCurrentImageLocation = () => {\r\n    if (Object.keys(imagePreviewsMap).length === 0) {\r\n      return coordinates; // Use current coordinates if no images\r\n    }\r\n    \r\n    const currentFilename = Object.keys(imagePreviewsMap)[currentImageIndex];\r\n    return imageLocationMap[currentFilename] || 'Not Available';\r\n  };\r\n\r\n  // Toggle camera with improved location handling\r\n  const toggleCamera = async () => {\r\n    const newCameraState = !cameraActive;\r\n    setCameraActive(newCameraState);\r\n    \r\n    if (newCameraState) {\r\n      // Get location when camera is activated\r\n      await handleLocationRequest();\r\n    } else {\r\n      // Only reset location if no images are captured\r\n      // This preserves location data for captured images\r\n      if (Object.keys(imagePreviewsMap).length === 0) {\r\n        setCoordinates('Not Available');\r\n        setLocationError('');\r\n        setLocationPermission('unknown');\r\n      }\r\n    }\r\n  };\r\n\r\n  // Toggle camera orientation (front/back) for mobile devices\r\n  const toggleCameraOrientation = () => {\r\n    setCameraOrientation(prev => prev === 'environment' ? 'user' : 'environment');\r\n  };\r\n\r\n  // Helper function to handle classification errors\r\n  const handleClassificationError = (errorMessage) => {\r\n    setClassificationError(errorMessage);\r\n    setShowClassificationModal(true);\r\n    setError(''); // Clear general error since we're showing specific modal\r\n  };\r\n\r\n  // Process image for detection\r\n  const handleProcess = async () => {\r\n    setLoading(true);\r\n    setError('');\r\n\r\n    try {\r\n      // Get user info from session storage\r\n      const userString = sessionStorage.getItem('user');\r\n      const user = userString ? JSON.parse(userString) : null;\r\n\r\n      // Get the currently selected image\r\n      const currentImagePreview = Object.values(imagePreviewsMap)[currentImageIndex];\r\n\r\n      if (!currentImagePreview) {\r\n        setError('No image selected for processing');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n\r\n      // Get coordinates for the current image\r\n      const imageCoordinates = getCurrentImageLocation();\r\n\r\n      // Get the current image filename\r\n      const filenames = Object.keys(imagePreviewsMap);\r\n      const currentFilename = filenames[currentImageIndex];\r\n\r\n      // Prepare request data\r\n      const requestData = {\r\n        image: currentImagePreview,\r\n        coordinates: imageCoordinates,\r\n        username: user?.username || 'Unknown',\r\n        role: user?.role || 'Unknown',\r\n        skip_road_classification: !roadClassificationEnabled\r\n      };\r\n\r\n      // Determine endpoint based on detection type\r\n      let endpoint;\r\n      switch(detectionType) {\r\n        case 'all':\r\n          endpoint = '/api/pavement/detect-all';\r\n          break;\r\n        case 'potholes':\r\n          endpoint = '/api/pavement/detect-potholes';\r\n          break;\r\n        case 'cracks':\r\n          endpoint = '/api/pavement/detect-cracks';\r\n          break;\r\n        case 'kerbs':\r\n          endpoint = '/api/pavement/detect-kerbs';\r\n          break;\r\n        default:\r\n          endpoint = '/api/pavement/detect-all';\r\n      }\r\n\r\n      // Make API request\r\n      const response = await axios.post(endpoint, requestData);\r\n\r\n      // Handle response\r\n      if (response.data.success) {\r\n        // Check if the image was actually processed (contains road) or just classified\r\n        const isProcessed = response.data.processed !== false;\r\n        const isRoad = response.data.classification?.is_road || false;\r\n\r\n        // Set the processed image and results for display\r\n        setProcessedImage(response.data.processed_image);\r\n        setResults(response.data);\r\n\r\n        // Extract detailed detection results for table display\r\n        const detectionResults = {\r\n          potholes: response.data.potholes || [],\r\n          cracks: response.data.cracks || [],\r\n          kerbs: response.data.kerbs || []\r\n        };\r\n\r\n        // Create batch result entry for the status table\r\n        const batchResult = {\r\n          filename: currentFilename,\r\n          success: true,\r\n          processed: isProcessed,\r\n          isRoad: isRoad,\r\n          classification: response.data.classification,\r\n          processedImage: response.data.processed_image,\r\n          originalImage: currentImagePreview, // Add original image data\r\n          data: response.data,\r\n          detectionResults: detectionResults,\r\n          wasClassificationEnabled: roadClassificationEnabled, // ADD THIS LINE\r\n          detectionCounts: {\r\n            potholes: detectionResults.potholes.length,\r\n            cracks: detectionResults.cracks.length,\r\n            kerbs: detectionResults.kerbs.length,\r\n            total: detectionResults.potholes.length + detectionResults.cracks.length + detectionResults.kerbs.length\r\n          }\r\n        };\r\n\r\n        // Update batch results to show the status table\r\n        setBatchResults([batchResult]);\r\n\r\n        // Auto-clear uploaded image icons after successful single image processing\r\n        // Store the processed image data before clearing (for both road and non-road)\r\n        setProcessedImagesData(prev => ({\r\n          ...prev,\r\n          [currentFilename]: {\r\n            originalImage: currentImagePreview,\r\n            processedImage: isRoad ? response.data.processed_image : null,\r\n            results: response.data,\r\n            isRoad: isRoad\r\n          }\r\n        }));\r\n          \r\n          // Clear image previews and files but keep results\r\n          setImageFiles([]);\r\n          setImagePreviewsMap({});\r\n          setImageLocationMap({});\r\n          setCurrentImageIndex(0);\r\n          \r\n          // Reset coordinates when clearing all images\r\n          setCoordinates('Not Available');\r\n          setLocationError('');\r\n          setLocationPermission('unknown');\r\n          \r\n          if (fileInputRef.current) {\r\n            fileInputRef.current.value = '';\r\n          }\r\n      } else {\r\n        const errorMessage = response.data.message || 'Detection failed';\r\n\r\n        // Create batch result entry for failed processing\r\n        const batchResult = {\r\n          filename: currentFilename,\r\n          success: false,\r\n          processed: false,\r\n          isRoad: false,\r\n          error: errorMessage,\r\n          isClassificationError: errorMessage.includes('No road detected')\r\n        };\r\n\r\n        // Update batch results to show the status table\r\n        setBatchResults([batchResult]);\r\n\r\n        setError(errorMessage);\r\n      }\r\n    } catch (error) {\r\n      const errorMessage = error.response?.data?.message || 'An error occurred during detection. Please try again.';\r\n\r\n      // Get the current image filename for batch results\r\n      const filenames = Object.keys(imagePreviewsMap);\r\n      const currentFilename = filenames[currentImageIndex];\r\n\r\n      // Create batch result entry for error case\r\n      const batchResult = {\r\n        filename: currentFilename,\r\n        success: false,\r\n        processed: false,\r\n        isRoad: false,\r\n        error: errorMessage,\r\n        isClassificationError: errorMessage.includes('No road detected')\r\n      };\r\n\r\n      // Update batch results to show the status table\r\n      setBatchResults([batchResult]);\r\n\r\n      // Check if this is a classification error (no road detected)\r\n      if (errorMessage.includes('No road detected')) {\r\n        handleClassificationError(errorMessage);\r\n      } else {\r\n        setError(errorMessage);\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Add a new function to process all images\r\n  const handleProcessAll = async () => {\r\n    if (Object.keys(imagePreviewsMap).length === 0) {\r\n      setError('No images to process');\r\n      return;\r\n    }\r\n\r\n    setBatchProcessing(true);\r\n    setError('');\r\n    setBatchResults([]);\r\n    setProcessedCount(0);\r\n    setTotalToProcess(Object.keys(imagePreviewsMap).length);\r\n    \r\n    // Get user info from session storage\r\n    const userString = sessionStorage.getItem('user');\r\n    const user = userString ? JSON.parse(userString) : null;\r\n    \r\n    try {\r\n      // Determine endpoint based on detection type\r\n      let endpoint;\r\n      switch(detectionType) {\r\n        case 'all':\r\n          endpoint = '/api/pavement/detect-all';\r\n          break;\r\n        case 'potholes':\r\n          endpoint = '/api/pavement/detect-potholes';\r\n          break;\r\n        case 'cracks':\r\n          endpoint = '/api/pavement/detect-cracks';\r\n          break;\r\n        case 'kerbs':\r\n          endpoint = '/api/pavement/detect-kerbs';\r\n          break;\r\n        default:\r\n          endpoint = '/api/pavement/detect-all';\r\n      }\r\n      \r\n      const results = [];\r\n      const filenames = Object.keys(imagePreviewsMap);\r\n      \r\n      // Process each image sequentially and display immediately\r\n      for (let i = 0; i < filenames.length; i++) {\r\n        const filename = filenames[i];\r\n        const imageData = imagePreviewsMap[filename];\r\n        \r\n        try {\r\n          // Update current image index to show which image is being processed\r\n          setCurrentImageIndex(i);\r\n          \r\n          // Get coordinates for this specific image\r\n          const imageCoordinates = imageLocationMap[filename] || 'Not Available';\r\n          \r\n          // Prepare request data\r\n          const requestData = {\r\n            image: imageData,\r\n            coordinates: imageCoordinates,\r\n            username: user?.username || 'Unknown',\r\n            role: user?.role || 'Unknown',\r\n            skip_road_classification: !roadClassificationEnabled\r\n          };\r\n          \r\n          // Make API request\r\n          const response = await axios.post(endpoint, requestData);\r\n          \r\n          if (response.data.success) {\r\n            // Check if the image was actually processed (contains road) or just classified\r\n            const isProcessed = response.data.processed !== false;\r\n            const isRoad = response.data.classification?.is_road || false;\r\n\r\n            if (isProcessed && isRoad) {\r\n              // Road image that was processed - display the results\r\n              setProcessedImage(response.data.processed_image);\r\n              setResults(response.data);\r\n            }\r\n\r\n            // Extract detailed detection results for table display\r\n            const detectionResults = {\r\n              potholes: response.data.potholes || [],\r\n              cracks: response.data.cracks || [],\r\n              kerbs: response.data.kerbs || []\r\n            };\r\n\r\n            results.push({\r\n              filename,\r\n              success: true,\r\n              processed: isProcessed,\r\n              isRoad: isRoad,\r\n              classification: response.data.classification,\r\n              processedImage: response.data.processed_image,\r\n              originalImage: imageData, // Add original image data\r\n              data: response.data,\r\n              detectionResults: detectionResults,\r\n              wasClassificationEnabled: roadClassificationEnabled, // ADD THIS LINE\r\n              detectionCounts: {\r\n                potholes: detectionResults.potholes.length,\r\n                cracks: detectionResults.cracks.length,\r\n                kerbs: detectionResults.kerbs.length,\r\n                total: detectionResults.potholes.length + detectionResults.cracks.length + detectionResults.kerbs.length\r\n              }\r\n            });\r\n          } else {\r\n            const errorMessage = response.data.message || 'Detection failed';\r\n            results.push({\r\n              filename,\r\n              success: false,\r\n              processed: false,\r\n              isRoad: false,\r\n              error: errorMessage,\r\n              isClassificationError: errorMessage.includes('No road detected')\r\n            });\r\n          }\r\n        } catch (error) {\r\n          const errorMessage = error.response?.data?.message || 'An error occurred during detection';\r\n          results.push({\r\n            filename,\r\n            success: false,\r\n            processed: false,\r\n            isRoad: false,\r\n            error: errorMessage,\r\n            isClassificationError: errorMessage.includes('No road detected')\r\n          });\r\n        }\r\n        \r\n        // Update progress\r\n        setProcessedCount(prev => prev + 1);\r\n        \r\n        // Pause briefly to allow user to see the result before moving to next image\r\n        // Only pause if not on the last image\r\n        if (i < filenames.length - 1) {\r\n          await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second pause\r\n        }\r\n      }\r\n      \r\n      // Store final results\r\n      setBatchResults(results);\r\n\r\n      // After batch processing is complete, display the first successfully processed road image\r\n      const processedRoadImages = results.filter(r => r.success && r.processed && r.isRoad);\r\n      if (processedRoadImages.length > 0) {\r\n        const firstProcessedRoadImage = processedRoadImages[0];\r\n        setProcessedImage(firstProcessedRoadImage.processedImage);\r\n        setResults(firstProcessedRoadImage.data);\r\n\r\n        // Set the current image index to 0 (first processed road image)\r\n        setCurrentImageIndex(0);\r\n      } else {\r\n        // No road images were processed, clear the display\r\n        setProcessedImage(null);\r\n        setResults(null);\r\n        setCurrentImageIndex(0);\r\n      }\r\n\r\n      // Auto-clear uploaded image icons after processing is complete\r\n      // Store processed images data before clearing (for both road and non-road)\r\n      const processedData = {};\r\n      results.forEach(result => {\r\n        if (result.success) {\r\n          const originalImage = imagePreviewsMap[result.filename];\r\n          processedData[result.filename] = {\r\n            originalImage: originalImage,\r\n            processedImage: result.isRoad ? result.processedImage : null,\r\n            results: result.data,\r\n            isRoad: result.isRoad\r\n          };\r\n          console.log('Storing image data for:', result.filename, 'isRoad:', result.isRoad, 'hasOriginalImage:', !!originalImage);\r\n        }\r\n      });\r\n      setProcessedImagesData(prev => ({ ...prev, ...processedData }));\r\n      \r\n      // Clear image previews and files but keep results\r\n      setImageFiles([]);\r\n      setImagePreviewsMap({});\r\n      setImageLocationMap({});\r\n      setCurrentImageIndex(0);\r\n      \r\n      // Reset coordinates when clearing all images\r\n      setCoordinates('Not Available');\r\n      setLocationError('');\r\n      setLocationPermission('unknown');\r\n      \r\n      if (fileInputRef.current) {\r\n        fileInputRef.current.value = '';\r\n      }\r\n\r\n    } catch (error) {\r\n      setError('Failed to process batch: ' + (error.message || 'Unknown error'));\r\n    } finally {\r\n      setBatchProcessing(false);\r\n    }\r\n  };\r\n\r\n  // Reset detection\r\n  const handleReset = () => {\r\n    setImageFiles([]);\r\n    setImagePreviewsMap({});\r\n    setImageLocationMap({});\r\n    setCurrentImageIndex(0);\r\n    setProcessedImage(null);\r\n    setResults(null);\r\n    setError('');\r\n    setBatchResults([]);\r\n    setProcessedCount(0);\r\n    setTotalToProcess(0);\r\n    setProcessedImagesData({});\r\n    \r\n    // Reset coordinates when clearing all images\r\n    setCoordinates('Not Available');\r\n    setLocationError('');\r\n    setLocationPermission('unknown');\r\n    \r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n\r\n\r\n\r\n\r\n  // Add function to handle thumbnail clicks\r\n  const handleThumbnailClick = (imageData) => {\r\n    setSelectedImageData(imageData);\r\n    setShowImageModal(true);\r\n  };\r\n\r\n  // Add sorting function for detection results\r\n  const handleSort = (key) => {\r\n    let direction = 'asc';\r\n    if (sortConfig.key === key && sortConfig.direction === 'asc') {\r\n      direction = 'desc';\r\n    }\r\n    setSortConfig({ key, direction });\r\n  };\r\n\r\n  // Function to sort detection results\r\n  const sortDetections = (detections) => {\r\n    if (!sortConfig.key) return detections;\r\n\r\n    return [...detections].sort((a, b) => {\r\n      let aValue = a[sortConfig.key];\r\n      let bValue = b[sortConfig.key];\r\n\r\n      // Handle numeric values\r\n      if (typeof aValue === 'number' && typeof bValue === 'number') {\r\n        return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;\r\n      }\r\n\r\n      // Handle string values\r\n      if (typeof aValue === 'string' && typeof bValue === 'string') {\r\n        return sortConfig.direction === 'asc'\r\n          ? aValue.localeCompare(bValue)\r\n          : bValue.localeCompare(aValue);\r\n      }\r\n\r\n      // Handle null/undefined values\r\n      if (aValue == null && bValue == null) return 0;\r\n      if (aValue == null) return sortConfig.direction === 'asc' ? 1 : -1;\r\n      if (bValue == null) return sortConfig.direction === 'asc' ? -1 : 1;\r\n\r\n      return 0;\r\n    });\r\n  };\r\n\r\n  // Function to export detection results to CSV\r\n  const exportToCSV = () => {\r\n    // Flatten all detection results\r\n    const allDetections = [];\r\n\r\n    batchResults.forEach(result => {\r\n      if (result.success && result.processed && result.detectionResults) {\r\n        const { potholes, cracks, kerbs } = result.detectionResults;\r\n\r\n        // Add potholes\r\n        potholes.forEach(pothole => {\r\n          allDetections.push({\r\n            filename: result.filename,\r\n            type: 'Pothole',\r\n            id: pothole.pothole_id,\r\n            area_cm2: pothole.area_cm2,\r\n            depth_cm: pothole.depth_cm,\r\n            volume: pothole.volume,\r\n            volume_range: pothole.volume_range,\r\n            crack_type: '',\r\n            area_range: '',\r\n            kerb_type: '',\r\n            condition: '',\r\n            length_m: '',\r\n            confidence: pothole.confidence\r\n          });\r\n        });\r\n\r\n        // Add cracks\r\n        cracks.forEach(crack => {\r\n          allDetections.push({\r\n            filename: result.filename,\r\n            type: 'Crack',\r\n            id: crack.crack_id,\r\n            area_cm2: crack.area_cm2,\r\n            depth_cm: '',\r\n            volume: '',\r\n            volume_range: '',\r\n            crack_type: crack.crack_type,\r\n            area_range: crack.area_range,\r\n            kerb_type: '',\r\n            condition: '',\r\n            length_m: '',\r\n            confidence: crack.confidence\r\n          });\r\n        });\r\n\r\n        // Add kerbs\r\n        kerbs.forEach(kerb => {\r\n          allDetections.push({\r\n            filename: result.filename,\r\n            type: 'Kerb',\r\n            id: kerb.kerb_id,\r\n            area_cm2: '',\r\n            depth_cm: '',\r\n            volume: '',\r\n            volume_range: '',\r\n            crack_type: '',\r\n            area_range: '',\r\n            kerb_type: kerb.kerb_type,\r\n            condition: kerb.condition,\r\n            length_m: kerb.length_m,\r\n            confidence: kerb.confidence\r\n          });\r\n        });\r\n      }\r\n    });\r\n\r\n    if (allDetections.length === 0) {\r\n      alert('No detection results to export.');\r\n      return;\r\n    }\r\n\r\n    // Create CSV content\r\n    const headers = [\r\n      'Image Filename',\r\n      'Detection Type',\r\n      'ID',\r\n      'Area (cm²)',\r\n      'Depth (cm)',\r\n      'Volume (cm³)',\r\n      'Volume Range',\r\n      'Crack Type',\r\n      'Area Range',\r\n      'Kerb Type',\r\n      'Condition',\r\n      'Length (m)',\r\n      'Confidence'\r\n    ];\r\n\r\n    const csvContent = [\r\n      headers.join(','),\r\n      ...allDetections.map(detection => [\r\n        detection.filename,\r\n        detection.type,\r\n        detection.id || '',\r\n        detection.area_cm2 || '',\r\n        detection.depth_cm || '',\r\n        detection.volume || '',\r\n        detection.volume_range || '',\r\n        detection.crack_type || '',\r\n        detection.area_range || '',\r\n        detection.kerb_type || '',\r\n        detection.condition || '',\r\n        detection.length_m || '',\r\n        detection.confidence || ''\r\n      ].join(','))\r\n    ].join('\\n');\r\n\r\n    // Create and download file\r\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n    const link = document.createElement('a');\r\n    const url = URL.createObjectURL(blob);\r\n    link.setAttribute('href', url);\r\n    link.setAttribute('download', `pavement_detection_results_${new Date().toISOString().split('T')[0]}.csv`);\r\n    link.style.visibility = 'hidden';\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  };\r\n\r\n\r\n// Clear results when road classification toggle is changed\r\n  useEffect(() => {\r\n    if (batchResults.length > 0) {\r\n      // Clear batch results when toggle changes to prevent showing outdated classification data\r\n      setBatchResults([]);\r\n      setProcessedImage(null);\r\n      setResults(null);\r\n    }\r\n  }, [roadClassificationEnabled]);\r\n\r\n\r\n  // Handle location permission changes\r\n  useEffect(() => {\r\n    if (cameraActive && locationPermission === 'unknown') {\r\n      // Try to get location when camera is first activated\r\n      handleLocationRequest();\r\n    }\r\n  }, [cameraActive]);\r\n\r\n  // Listen for permission changes if supported\r\n  useEffect(() => {\r\n    let permissionWatcher = null;\r\n    \r\n    const watchPermissions = async () => {\r\n      try {\r\n        if (navigator.permissions && navigator.permissions.query) {\r\n          const permission = await navigator.permissions.query({ name: 'geolocation' });\r\n          \r\n          permissionWatcher = () => {\r\n            setLocationPermission(permission.state);\r\n            if (permission.state === 'granted' && cameraActive && coordinates === 'Not Available') {\r\n              handleLocationRequest();\r\n            }\r\n          };\r\n          \r\n          permission.addEventListener('change', permissionWatcher);\r\n        }\r\n      } catch (err) {\r\n        console.warn('Permission watching not supported:', err);\r\n      }\r\n    };\r\n    \r\n    watchPermissions();\r\n    \r\n    return () => {\r\n      if (permissionWatcher) {\r\n        try {\r\n          const permission = navigator.permissions.query({ name: 'geolocation' });\r\n          permission.then(p => p.removeEventListener('change', permissionWatcher));\r\n        } catch (err) {\r\n          console.warn('Error removing permission listener:', err);\r\n        }\r\n      }\r\n    };\r\n  }, [cameraActive, coordinates]);\r\n\r\n  // Force re-render when current image changes to update location display\r\n  useEffect(() => {\r\n    // This effect ensures the UI updates when switching between images\r\n    // The getCurrentImageLocation function will return the correct location for the selected image\r\n  }, [currentImageIndex, imageLocationMap]);\r\n\r\n\r\n\r\n  return (\r\n    <Container className=\"pavement-page\">\r\n      \r\n      <Tabs\r\n        activeKey={activeTab}\r\n        onSelect={(k) => setActiveTab(k)}\r\n        className=\"mb-3\"\r\n      >\r\n        <Tab eventKey=\"detection\" title=\"Image Detection\">\r\n          <Card className=\"mb-3\">\r\n            <Card.Body className=\"py-3\">\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label className=\"mb-1\">Detection Type</Form.Label>\r\n                <Form.Select \r\n                  value={detectionType}\r\n                  onChange={(e) => setDetectionType(e.target.value)}\r\n                >\r\n                  <option value=\"all\">All (Potholes + Cracks + Kerbs)</option>\r\n                  <option value=\"potholes\">Potholes</option>\r\n                  <option value=\"cracks\">Alligator Cracks</option>\r\n                  <option value=\"kerbs\">Kerbs</option>\r\n                </Form.Select>\r\n              </Form.Group>\r\n\r\n              {/* Sticky note reminder and road classification toggle */}\r\n              <div className=\"d-flex align-items-start gap-2 mb-3\">\r\n                <OverlayTrigger\r\n                  trigger=\"click\"\r\n                  placement=\"right\"\r\n                  overlay={reminderPopover}\r\n                  rootClose\r\n                >\r\n                  <div\r\n                    className=\"sticky-note-icon\"\r\n                    style={{ cursor: 'pointer', display: 'inline-block' }}\r\n                  >\r\n                    <img\r\n                      src=\"/remindericon.svg\"\r\n                      alt=\"Image Upload Guidelines\"\r\n                      style={{ width: '28px', height: '28px' }}\r\n                    />\r\n                  </div>\r\n                </OverlayTrigger>\r\n\r\n                {/* Road Classification Toggle - Improved Design */}\r\n                <div className=\"road-classification-control\">\r\n                  <div className=\"d-flex align-items-center justify-content-between mb-1\">\r\n                    <span className=\"me-2\" style={{ fontSize: '0.9rem', fontWeight: '500', color: '#495057' }}>\r\n                      Road Classification\r\n                    </span>\r\n                      <OverlayTrigger\r\n                        placement=\"right\"\r\n                        delay={{ show: 200, hide: 100 }}\r\n                        overlay={\r\n                          <Popover id=\"road-classification-detailed-info\" style={{ maxWidth: '350px' }}>\r\n                            <Popover.Header as=\"h3\">\r\n                              <i className=\"fas fa-brain me-2 text-primary\"></i>\r\n                              Road Classification Feature\r\n                            </Popover.Header>\r\n                            <Popover.Body>\r\n                              <div className=\"mb-2\">\r\n                                <div className=\"mb-1\">\r\n                                  <i className=\"fas fa-toggle-on text-success me-2\"></i>\r\n                                  <strong>ENABLED (ON):</strong>\r\n                                </div>\r\n                                <div style={{ fontSize: '12px', color: '#6c757d', marginLeft: '20px' }}>\r\n                                  • AI analyzes images for road content first<br/>\r\n                                  • Only road images get defect detection<br/>\r\n                                  • More accurate results, slightly slower\r\n                                </div>\r\n                              </div>\r\n\r\n                              <div className=\"mb-2\">\r\n                                <div className=\"mb-1\">\r\n                                  <i className=\"fas fa-toggle-off text-secondary me-2\"></i>\r\n                                  <strong>DISABLED (OFF):</strong>\r\n                                </div>\r\n                                <div style={{ fontSize: '12px', color: '#6c757d', marginLeft: '20px' }}>\r\n                                  • All images processed directly<br/>\r\n                                  • No road verification step<br/>\r\n                                  • Faster processing, may have false positives\r\n                                </div>\r\n                              </div>\r\n\r\n                              <div className=\"alert alert-info py-2 px-2 mb-0\" style={{ fontSize: '11px' }}>\r\n                                <i className=\"fas fa-lightbulb me-1\"></i>\r\n                                <strong>Recommendation:</strong> Keep enabled for mixed image types.\r\n                                Disable only when all images contain roads and speed is priority.\r\n                              </div>\r\n                            </Popover.Body>\r\n                          </Popover>\r\n                        }\r\n                      >\r\n                        <span className=\"info-icon-wrapper\">\r\n                          <span className=\"road-classification-info-icon\"\r\n                             style={{\r\n                               fontSize: '14px',\r\n                               cursor: 'help',\r\n                               color: '#007bff',\r\n                               display: 'inline-flex',\r\n                               alignItems: 'center',\r\n                               justifyContent: 'center',\r\n                               position: 'relative',\r\n                               zIndex: '1000',\r\n                               fontWeight: 'bold'\r\n                             }}\r\n                          >i</span>\r\n                        </span>\r\n                      </OverlayTrigger>\r\n                    </div>\r\n                    <div className=\"d-flex align-items-center\">\r\n                      <div\r\n                        className=\"toggle-switch me-2\"\r\n                        onClick={() => setRoadClassificationEnabled(!roadClassificationEnabled)}\r\n                        style={{\r\n                          width: '60px',\r\n                          height: '30px',\r\n                          backgroundColor: roadClassificationEnabled ? '#28a745' : '#6c757d',\r\n                          borderRadius: '15px',\r\n                          position: 'relative',\r\n                          cursor: 'pointer',\r\n                          transition: 'background-color 0.3s ease',\r\n                          border: '2px solid transparent'\r\n                        }}\r\n                      >\r\n                        <div\r\n                          className=\"toggle-slider\"\r\n                          style={{\r\n                            width: '22px',\r\n                            height: '22px',\r\n                            backgroundColor: 'white',\r\n                            borderRadius: '50%',\r\n                            position: 'absolute',\r\n                            top: '2px',\r\n                            left: roadClassificationEnabled ? '34px' : '2px',\r\n                            transition: 'left 0.3s ease',\r\n                            boxShadow: '0 2px 4px rgba(0,0,0,0.2)'\r\n                          }}\r\n                        />\r\n                        <span\r\n                          style={{\r\n                            position: 'absolute',\r\n                            top: '50%',\r\n                            left: roadClassificationEnabled ? '8px' : '32px',\r\n                            transform: 'translateY(-50%)',\r\n                            fontSize: '10px',\r\n                            fontWeight: '600',\r\n                            color: 'white',\r\n                            transition: 'all 0.3s ease',\r\n                            userSelect: 'none'\r\n                          }}\r\n                        >\r\n                          {roadClassificationEnabled ? 'ON' : 'OFF'}\r\n                        </span>\r\n                      </div>\r\n                      <small className=\"text-muted\" style={{ fontSize: '11px' }}>\r\n                        {roadClassificationEnabled ? \"Only road images processed\" : \"All images processed\"}\r\n                      </small>\r\n                    </div>\r\n                  </div>\r\n                  \r\n\r\n                </div>\r\n\r\n              <div className=\"mb-3\">\r\n                <Form.Label>Image Source</Form.Label>\r\n                <div className=\"d-flex gap-2 mb-2\">\r\n                  <Button \r\n                    variant={cameraActive ? \"primary\" : \"outline-primary\"}\r\n                    onClick={toggleCamera}\r\n                    disabled={locationLoading}\r\n                  >\r\n                    {locationLoading ? (\r\n                      <>\r\n                        <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" />\r\n                        <span className=\"ms-2\">Getting Location...</span>\r\n                      </>\r\n                    ) : (\r\n                      cameraActive ? \"Disable Camera\" : \"Enable Camera\"\r\n                    )}\r\n                  </Button>\r\n                                      <div className=\"file-input-container\">\r\n                      <label className=\"file-input-label\">\r\n                        Upload Image\r\n                        <input\r\n                          type=\"file\"\r\n                          className=\"file-input\"\r\n                          accept=\"image/*,.avif\"\r\n                          onChange={handleFileChange}\r\n                          ref={fileInputRef}\r\n                          disabled={cameraActive}\r\n                          multiple\r\n                        />\r\n                      </label>\r\n                      <small className=\"text-muted d-block mt-1\">\r\n                        <i className=\"fas fa-info-circle me-1\"></i>\r\n                        Supports all image formats including AVIF (automatically converted to JPG for processing)\r\n                      </small>\r\n                    </div>\r\n                </div>\r\n                \r\n                {/* Location Status Display */}\r\n                {cameraActive && (\r\n                  <div className=\"location-status mb-3\">\r\n                    <small className=\"text-muted\">\r\n                      <strong>Location Status:</strong> \r\n                      {locationPermission === 'granted' && <span className=\"text-success ms-1\">✓ Enabled</span>}\r\n                      {locationPermission === 'denied' && <span className=\"text-danger ms-1\">✗ Denied</span>}\r\n                      {locationPermission === 'prompt' && <span className=\"text-warning ms-1\">⚠ Requesting...</span>}\r\n                      {locationPermission === 'unknown' && <span className=\"text-secondary ms-1\">? Unknown</span>}\r\n                    </small>\r\n                    {(coordinates !== 'Not Available' || Object.keys(imagePreviewsMap).length > 0) && (\r\n                      <div className=\"mt-1\">\r\n                        <small className=\"text-muted\">\r\n                          <strong>Current Location:</strong> <span className=\"text-primary\">{coordinates}</span>\r\n                        </small>\r\n                        {Object.keys(imagePreviewsMap).length > 0 && (\r\n                          <div className=\"mt-1\">\r\n                            <small className=\"text-muted\">\r\n                              <strong>Selected Image Location:</strong> <span className=\"text-primary\">{getCurrentImageLocation()}</span>\r\n                            </small>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    )}\r\n                    {locationError && (\r\n                      <Alert variant=\"warning\" className=\"mt-2 mb-0\" style={{ fontSize: '0.875rem' }}>\r\n                        <Alert.Heading as=\"h6\">Location Access Issue</Alert.Heading>\r\n                        <div style={{ whiteSpace: 'pre-line' }}>{locationError}</div>\r\n                        <hr />\r\n                        <div className=\"d-flex justify-content-end\">\r\n                          <Button variant=\"outline-warning\" size=\"sm\" onClick={handleLocationRequest}>\r\n                            Retry Location Access\r\n                          </Button>\r\n                        </div>\r\n                      </Alert>\r\n                    )}\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {cameraActive && (\r\n                <div className=\"webcam-container mb-3\">\r\n                  <Webcam\r\n                    audio={false}\r\n                    ref={webcamRef}\r\n                    screenshotFormat=\"image/jpeg\"\r\n                    className=\"webcam\"\r\n                    videoConstraints={{\r\n                      width: 640,\r\n                      height: 480,\r\n                      facingMode: cameraOrientation\r\n                    }}\r\n                  />\r\n                  {isMobile && (\r\n                    <Button \r\n                      variant=\"outline-secondary\" \r\n                      onClick={toggleCameraOrientation}\r\n                      className=\"mt-2 mb-2\"\r\n                      size=\"sm\"\r\n                    >\r\n                      Rotate Camera\r\n                    </Button>\r\n                  )}\r\n                  <Button \r\n                    variant=\"success\" \r\n                    onClick={handleCapture}\r\n                    className=\"mt-2\"\r\n                  >\r\n                    Capture Photo\r\n                  </Button>\r\n                </div>\r\n              )}\r\n\r\n              {Object.keys(imagePreviewsMap).length > 0 && (\r\n                <div className=\"image-preview-container mb-3\">\r\n                  <h5>Previews</h5>\r\n                  <div className=\"image-gallery\">\r\n                    {Object.entries(imagePreviewsMap).map(([name, preview], index) => (\r\n                      <div \r\n                        key={name} \r\n                        className={`image-thumbnail ${index === currentImageIndex ? 'selected' : ''}`}\r\n                        onClick={() => setCurrentImageIndex(index)}\r\n                      >\r\n                        <img \r\n                          src={preview} \r\n                          alt={`Preview ${index + 1}`} \r\n                          className=\"img-thumbnail\" \r\n                        />\r\n                        {/* AVIF conversion indicator */}\r\n                        {name.toLowerCase().endsWith('.avif') && (\r\n                          <div className=\"avif-indicator\" title=\"AVIF file - will be converted to JPG during processing\">\r\n                            <i className=\"fas fa-sync-alt text-info\"></i>\r\n                            <small className=\"ms-1\">AVIF</small>\r\n                          </div>\r\n                        )}\r\n                        <button \r\n                          className=\"btn btn-sm btn-danger remove-image\" \r\n                          onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            const newFiles = imageFiles.filter((_, i) => i !== index);\r\n                            const newPreviewsMap = {...imagePreviewsMap};\r\n                            const newLocationMap = {...imageLocationMap};\r\n                            delete newPreviewsMap[name];\r\n                            delete newLocationMap[name];\r\n                            setImageFiles(newFiles);\r\n                            setImagePreviewsMap(newPreviewsMap);\r\n                            setImageLocationMap(newLocationMap);\r\n                            if (currentImageIndex >= newFiles.length) {\r\n                              setCurrentImageIndex(Math.max(0, newFiles.length - 1));\r\n                            }\r\n                          }}\r\n                        >\r\n                          ×\r\n                        </button>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                  <div className=\"current-image-preview\">\r\n                    {Object.values(imagePreviewsMap)[currentImageIndex] && (\r\n                      <img \r\n                        src={Object.values(imagePreviewsMap)[currentImageIndex]} \r\n                        alt=\"Current Preview\" \r\n                        className=\"image-preview img-fluid\" \r\n                      />\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {error && <Alert variant=\"danger\">{error}</Alert>}\r\n\r\n              <div className=\"d-flex gap-2 mb-3\">\r\n                <Button \r\n                  variant=\"primary\" \r\n                  onClick={handleProcess}\r\n                  disabled={Object.keys(imagePreviewsMap).length === 0 || loading || batchProcessing}\r\n                >\r\n                  {loading ? (\r\n                    <>\r\n                      <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" />\r\n                      <span className=\"ms-2\">Detecting...</span>\r\n                    </>\r\n                  ) : (\r\n                    `Detect Current Image`\r\n                  )}\r\n                </Button>\r\n                \r\n                <Button \r\n                  variant=\"success\" \r\n                  onClick={handleProcessAll}\r\n                  disabled={Object.keys(imagePreviewsMap).length === 0 || loading || batchProcessing}\r\n                >\r\n                  {batchProcessing ? (\r\n                    <>\r\n                      <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" />\r\n                      <span className=\"ms-2\">Processing {processedCount}/{totalToProcess}</span>\r\n                    </>\r\n                  ) : (\r\n                    `Process All Images`\r\n                  )}\r\n                </Button>\r\n                \r\n                <Button \r\n                  variant=\"secondary\" \r\n                  onClick={handleReset}\r\n                  disabled={loading || batchProcessing}\r\n                >\r\n                  Reset\r\n                </Button>\r\n              </div>\r\n            </Card.Body>\r\n          </Card>\r\n\r\n\r\n          {/* Enhanced Detection Results Table */}\r\n          {batchResults.some(result => result.success && result.processed && result.detectionCounts?.total > 0) && (\r\n            <Card className=\"mb-4\">\r\n              <Card.Header>\r\n                <div className=\"d-flex justify-content-between align-items-center\">\r\n                  <h5 className=\"mb-0\">\r\n                    <i className=\"fas fa-table me-2\"></i>\r\n                    Detailed Detection Results\r\n                  </h5>\r\n                  <div className=\"d-flex gap-2\">\r\n                    <Button\r\n                      variant=\"success\"\r\n                      size=\"sm\"\r\n                      onClick={exportToCSV}\r\n                      title=\"Export results to CSV\"\r\n                    >\r\n                      <i className=\"fas fa-download me-1\"></i>\r\n                      Export CSV\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              </Card.Header>\r\n              <Card.Body>\r\n                  {/* Filter Controls */}\r\n                  <div className=\"mb-3\">\r\n                    <div className=\"d-flex gap-2 flex-wrap\">\r\n                      <Button\r\n                        variant={detectionTableFilter === 'all' ? 'primary' : 'outline-primary'}\r\n                        size=\"sm\"\r\n                        onClick={() => setDetectionTableFilter('all')}\r\n                      >\r\n                        All Detections\r\n                      </Button>\r\n                      <Button\r\n                        variant={detectionTableFilter === 'potholes' ? 'danger' : 'outline-danger'}\r\n                        size=\"sm\"\r\n                        onClick={() => setDetectionTableFilter('potholes')}\r\n                      >\r\n                        Potholes Only\r\n                      </Button>\r\n                      <Button\r\n                        variant={detectionTableFilter === 'cracks' ? 'warning' : 'outline-warning'}\r\n                        size=\"sm\"\r\n                        onClick={() => setDetectionTableFilter('cracks')}\r\n                      >\r\n                        Cracks Only\r\n                      </Button>\r\n                      <Button\r\n                        variant={detectionTableFilter === 'kerbs' ? 'info' : 'outline-info'}\r\n                        size=\"sm\"\r\n                        onClick={() => setDetectionTableFilter('kerbs')}\r\n                      >\r\n                        Kerbs Only\r\n                      </Button>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Summary Statistics */}\r\n                  <div className=\"mb-4 detection-summary-cards\">\r\n                    <div className=\"row\">\r\n                      <div className=\"col-md-3\">\r\n                        <div className=\"card bg-danger text-white\">\r\n                          <div className=\"card-body text-center py-2\">\r\n                            <h6 className=\"mb-1\">Total Potholes</h6>\r\n                            <h4 className=\"mb-0\">\r\n                              {batchResults.reduce((sum, result) => sum + (result.detectionCounts?.potholes || 0), 0)}\r\n                            </h4>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"col-md-3\">\r\n                        <div className=\"card bg-warning text-white\">\r\n                          <div className=\"card-body text-center py-2\">\r\n                            <h6 className=\"mb-1\">Total Cracks</h6>\r\n                            <h4 className=\"mb-0\">\r\n                              {batchResults.reduce((sum, result) => sum + (result.detectionCounts?.cracks || 0), 0)}\r\n                            </h4>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"col-md-3\">\r\n                        <div className=\"card bg-info text-white\">\r\n                          <div className=\"card-body text-center py-2\">\r\n                            <h6 className=\"mb-1\">Total Kerbs</h6>\r\n                            <h4 className=\"mb-0\">\r\n                              {batchResults.reduce((sum, result) => sum + (result.detectionCounts?.kerbs || 0), 0)}\r\n                            </h4>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"col-md-3\">\r\n                        <div className=\"card bg-success text-white\">\r\n                          <div className=\"card-body text-center py-2\">\r\n                            <h6 className=\"mb-1\">Total Detections</h6>\r\n                            <h4 className=\"mb-0\">\r\n                              {batchResults.reduce((sum, result) => sum + (result.detectionCounts?.total || 0), 0)}\r\n                            </h4>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Detailed Results Table */}\r\n                  <div className=\"table-responsive detection-table-container\">\r\n                    {(() => {\r\n                      // Flatten all detection results into a single array\r\n                      const allDetections = [];\r\n\r\n                      batchResults.forEach(result => {\r\n                        if (result.success && result.processed && result.detectionResults) {\r\n                          const { potholes, cracks, kerbs } = result.detectionResults;\r\n\r\n                          // Add potholes\r\n                          if (detectionTableFilter === 'all' || detectionTableFilter === 'potholes') {\r\n                            potholes.forEach(pothole => {\r\n                              allDetections.push({\r\n                                ...pothole,\r\n                                type: 'Pothole',\r\n                                filename: result.filename,\r\n                                detectionType: 'potholes'\r\n                              });\r\n                            });\r\n                          }\r\n\r\n                          // Add cracks\r\n                          if (detectionTableFilter === 'all' || detectionTableFilter === 'cracks') {\r\n                            cracks.forEach(crack => {\r\n                              allDetections.push({\r\n                                ...crack,\r\n                                type: 'Crack',\r\n                                filename: result.filename,\r\n                                detectionType: 'cracks'\r\n                              });\r\n                            });\r\n                          }\r\n\r\n                          // Add kerbs\r\n                          if (detectionTableFilter === 'all' || detectionTableFilter === 'kerbs') {\r\n                            kerbs.forEach(kerb => {\r\n                              allDetections.push({\r\n                                ...kerb,\r\n                                type: 'Kerb',\r\n                                filename: result.filename,\r\n                                detectionType: 'kerbs'\r\n                              });\r\n                            });\r\n                          }\r\n                        }\r\n                      });\r\n\r\n                      if (allDetections.length === 0) {\r\n                        return (\r\n                          <div className=\"text-center py-5\">\r\n                            <div className=\"mb-3\">\r\n                              <i className=\"fas fa-search fa-3x text-muted\"></i>\r\n                            </div>\r\n                            <h6 className=\"text-muted\">No detections found</h6>\r\n                            <p className=\"text-muted mb-0\">\r\n                              {detectionTableFilter === 'all'\r\n                                ? 'No defects were detected in the processed images.'\r\n                                : `No ${detectionTableFilter} were detected in the processed images.`}\r\n                            </p>\r\n                          </div>\r\n                        );\r\n                      }\r\n\r\n                      return (\r\n                        <table className=\"table table-striped table-bordered\">\r\n                          <thead>\r\n                            <tr>\r\n                              <th>Original Image</th>\r\n                              <th>Processed Image</th>\r\n                              <th\r\n                                style={{ cursor: 'pointer' }}\r\n                                onClick={() => handleSort('detectionType')}\r\n                              >\r\n                                Type {sortConfig.key === 'detectionType' && (\r\n                                  <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\r\n                                )}\r\n                              </th>\r\n                              <th>ID</th>\r\n                              {(detectionTableFilter === 'all' || detectionTableFilter === 'potholes') && (\r\n                                <>\r\n                                  <th\r\n                                    style={{ cursor: 'pointer' }}\r\n                                    onClick={() => handleSort('area_cm2')}\r\n                                  >\r\n                                    Area (cm²) {sortConfig.key === 'area_cm2' && (\r\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\r\n                                    )}\r\n                                  </th>\r\n                                  <th\r\n                                    style={{ cursor: 'pointer' }}\r\n                                    onClick={() => handleSort('depth_cm')}\r\n                                  >\r\n                                    Depth (cm) {sortConfig.key === 'depth_cm' && (\r\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\r\n                                    )}\r\n                                  </th>\r\n                                  <th\r\n                                    style={{ cursor: 'pointer' }}\r\n                                    onClick={() => handleSort('volume')}\r\n                                  >\r\n                                    Volume (cm³) {sortConfig.key === 'volume' && (\r\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\r\n                                    )}\r\n                                  </th>\r\n                                  <th>Volume Range</th>\r\n                                </>\r\n                              )}\r\n                              {(detectionTableFilter === 'all' || detectionTableFilter === 'cracks') && (\r\n                                <>\r\n                                  <th\r\n                                    style={{ cursor: 'pointer' }}\r\n                                    onClick={() => handleSort('crack_type')}\r\n                                  >\r\n                                    Crack Type {sortConfig.key === 'crack_type' && (\r\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\r\n                                    )}\r\n                                  </th>\r\n                                  <th\r\n                                    style={{ cursor: 'pointer' }}\r\n                                    onClick={() => handleSort('area_cm2')}\r\n                                  >\r\n                                    Area (cm²) {sortConfig.key === 'area_cm2' && (\r\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\r\n                                    )}\r\n                                  </th>\r\n                                  <th>Area Range</th>\r\n                                </>\r\n                              )}\r\n                              {(detectionTableFilter === 'all' || detectionTableFilter === 'kerbs') && (\r\n                                <>\r\n                                  <th\r\n                                    style={{ cursor: 'pointer' }}\r\n                                    onClick={() => handleSort('kerb_type')}\r\n                                  >\r\n                                    Kerb Type {sortConfig.key === 'kerb_type' && (\r\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\r\n                                    )}\r\n                                  </th>\r\n                                  <th\r\n                                    style={{ cursor: 'pointer' }}\r\n                                    onClick={() => handleSort('condition')}\r\n                                  >\r\n                                    Condition {sortConfig.key === 'condition' && (\r\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\r\n                                    )}\r\n                                  </th>\r\n                                  <th\r\n                                    style={{ cursor: 'pointer' }}\r\n                                    onClick={() => handleSort('length_m')}\r\n                                  >\r\n                                    Length (m) {sortConfig.key === 'length_m' && (\r\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\r\n                                    )}\r\n                                  </th>\r\n                                </>\r\n                              )}\r\n                              <th\r\n                                style={{ cursor: 'pointer' }}\r\n                                onClick={() => handleSort('confidence')}\r\n                              >\r\n                                Confidence {sortConfig.key === 'confidence' && (\r\n                                  <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\r\n                                )}\r\n                              </th>\r\n                            </tr>\r\n                          </thead>\r\n                          <tbody>\r\n                            {sortDetections(allDetections).map((detection, index) => (\r\n                              <tr key={`${detection.filename}-${detection.detectionType}-${index}`}>\r\n                                {/* Original Image Column */}\r\n                                <td style={{ width: '120px', textAlign: 'center' }}>\r\n                                  {(() => {\r\n                                    const result = batchResults.find(r => r.filename === detection.filename);\r\n                                    const originalImage = result?.originalImage;\r\n                                    return originalImage ? (\r\n                                      <div\r\n                                        className=\"image-thumbnail-container\"\r\n                                        onClick={() => handleThumbnailClick({\r\n                                          originalImage: originalImage,\r\n                                          processedImage: result?.processedImage,\r\n                                          isRoad: result?.isRoad,\r\n                                          filename: detection.filename\r\n                                        })}\r\n                                        title=\"Click to enlarge 🔍\"\r\n                                      >\r\n                                        <img\r\n                                          src={originalImage}\r\n                                          alt=\"Original\"\r\n                                          className=\"image-thumbnail\"\r\n                                        />\r\n                                        <div className=\"thumbnail-overlay\">\r\n                                          🔍\r\n                                        </div>\r\n                                      </div>\r\n                                    ) : (\r\n                                      <small className=\"text-muted\">No image</small>\r\n                                    );\r\n                                  })()}\r\n                                </td>\r\n\r\n                                {/* Processed Image Column */}\r\n                                <td style={{ width: '120px', textAlign: 'center' }}>\r\n                                  {(() => {\r\n                                    const result = batchResults.find(r => r.filename === detection.filename);\r\n                                    const processedImage = result?.processedImage;\r\n                                    const originalImage = result?.originalImage;\r\n\r\n                                    return processedImage && result?.isRoad ? (\r\n                                      <div\r\n                                        className=\"image-thumbnail-container\"\r\n                                        onClick={() => handleThumbnailClick({\r\n                                          originalImage: originalImage,\r\n                                          processedImage: processedImage,\r\n                                          isRoad: result.isRoad,\r\n                                          filename: detection.filename\r\n                                        })}\r\n                                        title=\"Click to enlarge 🔍\"\r\n                                      >\r\n                                        <img\r\n                                          src={processedImage}\r\n                                          alt=\"Processed\"\r\n                                          className=\"image-thumbnail\"\r\n                                        />\r\n                                        <div className=\"thumbnail-overlay\">\r\n                                          🔍\r\n                                        </div>\r\n                                      </div>\r\n                                    ) : (\r\n                                      <small className=\"text-muted\">No processed image</small>\r\n                                    );\r\n                                  })()}\r\n                                </td>\r\n                                <td>\r\n                                  <span className={`badge ${\r\n                                    detection.detectionType === 'potholes' ? 'bg-danger' :\r\n                                    detection.detectionType === 'cracks' ? 'bg-warning' : 'bg-info'\r\n                                  }`}>\r\n                                    {detection.type}\r\n                                  </span>\r\n                                </td>\r\n                                <td>{detection.pothole_id || detection.crack_id || detection.kerb_id || index + 1}</td>\r\n\r\n                                {/* Pothole-specific columns */}\r\n                                {(detectionTableFilter === 'all' || detectionTableFilter === 'potholes') && (\r\n                                  <>\r\n                                    <td>{detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'}</td>\r\n                                    <td>{detection.depth_cm ? detection.depth_cm.toFixed(2) : 'N/A'}</td>\r\n                                    <td>{detection.volume ? detection.volume.toFixed(2) : 'N/A'}</td>\r\n                                    <td>{detection.volume_range || 'N/A'}</td>\r\n                                  </>\r\n                                )}\r\n\r\n                                {/* Crack-specific columns */}\r\n                                {(detectionTableFilter === 'all' || detectionTableFilter === 'cracks') && (\r\n                                  <>\r\n                                    <td>{detection.crack_type || 'N/A'}</td>\r\n                                    <td>{detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'}</td>\r\n                                    <td>{detection.area_range || 'N/A'}</td>\r\n                                  </>\r\n                                )}\r\n\r\n                                {/* Kerb-specific columns */}\r\n                                {(detectionTableFilter === 'all' || detectionTableFilter === 'kerbs') && (\r\n                                  <>\r\n                                    <td>{detection.kerb_type || 'N/A'}</td>\r\n                                    <td>{detection.condition || 'N/A'}</td>\r\n                                    <td>{detection.length_m ? detection.length_m.toFixed(2) : 'N/A'}</td>\r\n                                  </>\r\n                                )}\r\n\r\n                                <td>{detection.confidence ? (detection.confidence * 100).toFixed(1) + '%' : 'N/A'}</td>\r\n                              </tr>\r\n                            ))}\r\n                          </tbody>\r\n                        </table>\r\n                      );\r\n                    })()}\r\n                  </div>\r\n                </Card.Body>\r\n            </Card>\r\n          )}\r\n\r\n          {/* Batch processing status indicator */}\r\n          {batchProcessing && (\r\n            <div className=\"batch-processing-status mt-3\">\r\n              <div className=\"d-flex align-items-center\">\r\n                <div className=\"me-3\">\r\n                  <Spinner animation=\"border\" size=\"sm\" role=\"status\" />\r\n                </div>\r\n                <div>\r\n                  <h6 className=\"mb-1\">Processing images: {processedCount}/{totalToProcess}</h6>\r\n                  <div className=\"progress\" style={{ height: '10px' }}>\r\n                    <div \r\n                      className=\"progress-bar\" \r\n                      role=\"progressbar\" \r\n                      style={{ width: `${(processedCount / totalToProcess) * 100}%` }}\r\n                      aria-valuenow={processedCount}\r\n                      aria-valuemin=\"0\" \r\n                      aria-valuemax={totalToProcess}\r\n                    />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n\r\n\r\n\r\n\r\n          {/* Batch Processing Summary */}\r\n          {!batchProcessing && batchResults.length > 0 && (() => {\r\n            const totalImages = batchResults.length;\r\n            const successfulImages = batchResults.filter(r => r.success).length;\r\n            const failedImages = batchResults.filter(r => !r.success).length;\r\n\r\n            let alertVariant = 'light';\r\n            let alertClass = '';\r\n\r\n            if (batchResults.some(result => result.wasClassificationEnabled)) {\r\n              // When classification was enabled during processing, use road/non-road logic\r\n              // When classification is enabled, use road/non-road logic\r\n              const nonRoadImages = batchResults.filter(r => !r.isRoad).length;\r\n              const nonRoadPercentage = totalImages > 0 ? (nonRoadImages / totalImages) * 100 : 0;\r\n\r\n              if (totalImages > 0) {\r\n                if (nonRoadPercentage === 0) {\r\n                  // 100% road detection - Green\r\n                  alertVariant = 'success';\r\n                } else if (nonRoadPercentage === 100) {\r\n                  // 100% non-road detection - Red\r\n                  alertVariant = 'danger';\r\n                } else {\r\n                  // Combined detection (mixed results) - Light Orange\r\n                  alertVariant = 'warning';\r\n                  alertClass = 'summary-light-orange';\r\n                }\r\n              }\r\n            } else {\r\n              // When classification is disabled, use success/failure logic\r\n              if (failedImages === 0) {\r\n                // All successful - Green\r\n                alertVariant = 'success';\r\n              } else if (successfulImages === 0) {\r\n                // All failed - Red\r\n                alertVariant = 'danger';\r\n              } else {\r\n                // Mixed results - Warning\r\n                alertVariant = 'warning';\r\n              }\r\n            }\r\n\r\n            return (\r\n              <div className=\"batch-complete-status mt-4\">\r\n                <Alert variant={alertVariant} className={alertClass}>\r\n                  <i className=\"fas fa-check-circle me-2\"></i>\r\n                  Processed {batchResults.length} images.\r\n                  {roadClassificationEnabled ? (\r\n                    <>\r\n                      {batchResults.filter(r => r.success && r.processed).length} road images processed,\r\n                      {batchResults.filter(r => r.success && !r.processed).length} non-road images detected,\r\n                      {batchResults.filter(r => !r.success).length} failed.\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      {batchResults.filter(r => r.success).length} images processed successfully,\r\n                      {batchResults.filter(r => !r.success).length} failed.\r\n                    </>\r\n                  )}\r\n                </Alert>\r\n              </div>\r\n            );\r\n          })()}\r\n\r\n          {/* Image Status Table - Only show when road classification is enabled */}\r\n          {/* Image Status Table - Only show when road classification was enabled during processing */}\r\n          {!batchProcessing && batchResults.length > 0 && batchResults.some(result => result.wasClassificationEnabled) && (\r\n            <div className=\"image-status-table mt-4\">\r\n              <Card>\r\n                <Card.Header>\r\n                  <div className=\"d-flex justify-content-between align-items-center\">\r\n                    <h5 className=\"mb-0\">Image Processing Status</h5>\r\n                    <div className=\"filter-buttons\">\r\n                      <Button\r\n                        variant={imageFilter === 'all' ? 'primary' : 'outline-primary'}\r\n                        size=\"sm\"\r\n                        className=\"me-2\"\r\n                        onClick={() => setImageFilter('all')}\r\n                      >\r\n                        Show All Images\r\n                      </Button>\r\n                      <Button\r\n                        variant={imageFilter === 'road' ? 'success' : 'outline-success'}\r\n                        size=\"sm\"\r\n                        className=\"me-2\"\r\n                        onClick={() => setImageFilter('road')}\r\n                      >\r\n                        Show Only Road Images\r\n                      </Button>\r\n                      <Button\r\n                        variant={imageFilter === 'non-road' ? 'danger' : 'outline-danger'}\r\n                        size=\"sm\"\r\n                        onClick={() => setImageFilter('non-road')}\r\n                      >\r\n                        Show Only Non-Road Images\r\n                      </Button>\r\n                    </div>\r\n                  </div>\r\n                </Card.Header>\r\n                <Card.Body>\r\n                  <div className=\"table-responsive\">\r\n                    <table className=\"table table-striped\">\r\n                      <thead>\r\n                        <tr>\r\n                          <th>Image</th>\r\n                          <th>Detection Status</th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody>\r\n                        {batchResults\r\n                          .filter(result => {\r\n                            if (imageFilter === 'road') return result.isRoad;\r\n                            if (imageFilter === 'non-road') return !result.isRoad;\r\n                            return true; // 'all'\r\n                          })\r\n                          .map((result, index) => {\r\n                            const filename = result.filename;\r\n                            const isRoad = result.isRoad;\r\n                            \r\n                            // Get image from stored processed data\r\n                            let imagePreview = null;\r\n                            let imageData = null;\r\n                            \r\n                            if (processedImagesData[filename]) {\r\n                              // Use stored processed image data\r\n                              imagePreview = processedImagesData[filename].originalImage;\r\n                              imageData = processedImagesData[filename];\r\n                              console.log('Found stored data for:', filename, 'hasImage:', !!imagePreview);\r\n                            } else if (imagePreviewsMap[filename]) {\r\n                              // Fallback to current preview (for any remaining unprocessed images)\r\n                              imagePreview = imagePreviewsMap[filename];\r\n                              imageData = {\r\n                                originalImage: imagePreview,\r\n                                processedImage: null,\r\n                                results: null,\r\n                                isRoad: isRoad\r\n                              };\r\n                              console.log('Using fallback data for:', filename, 'hasImage:', !!imagePreview);\r\n                            } else {\r\n                              console.log('No image data found for:', filename);\r\n                            }\r\n\r\n                            return (\r\n                              <tr key={filename}>\r\n                                <td>\r\n                                  <div className=\"d-flex align-items-center\">\r\n                                    {imagePreview ? (\r\n                                      <img\r\n                                        src={imagePreview}\r\n                                        alt={`Thumbnail ${index + 1}`}\r\n                                        className=\"img-thumbnail me-2\"\r\n                                        style={{ \r\n                                          width: '60px', \r\n                                          height: '60px', \r\n                                          objectFit: 'cover',\r\n                                          cursor: 'pointer'\r\n                                        }}\r\n                                        onClick={() => handleThumbnailClick(imageData)}\r\n                                        title=\"Click to view full size\"\r\n                                      />\r\n                                    ) : (\r\n                                      <div \r\n                                        className=\"img-thumbnail me-2 d-flex align-items-center justify-content-center\"\r\n                                        style={{ \r\n                                          width: '60px', \r\n                                          height: '60px', \r\n                                          backgroundColor: '#f8f9fa',\r\n                                          border: '1px solid #dee2e6'\r\n                                        }}\r\n                                      >\r\n                                        <small className=\"text-muted\">No Image</small>\r\n                                      </div>\r\n                                    )}\r\n                                    <small className=\"text-muted\">{filename}</small>\r\n                                  </div>\r\n                                </td>\r\n                                <td>\r\n                                  <span className={`badge ${isRoad ? 'bg-success' : 'bg-danger'}`}>\r\n                                    {isRoad ? 'Road' : 'Non-Road'}\r\n                                  </span>\r\n                                </td>\r\n                              </tr>\r\n                            );\r\n                          })}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n\r\n\r\n\r\n\r\n                </Card.Body>\r\n              </Card>\r\n            </div>\r\n          )}\r\n        </Tab>\r\n        \r\n        <Tab eventKey=\"video\" title=\"Video Detection\">\r\n          <VideoDefectDetection />\r\n        </Tab>\r\n        \r\n        <Tab eventKey=\"information\" title=\"Information\">\r\n          <Card>\r\n            <Card.Body>\r\n              <h4>About Pavement Analysis</h4>\r\n              <p>\r\n                The Pavement Analysis module uses advanced computer vision to detect and analyze \r\n                various types of pavement defects and features:\r\n              </p>\r\n              \r\n              <h5>1. Potholes</h5>\r\n              <p>\r\n                Potholes are bowl-shaped holes of various sizes in the road surface that can be a \r\n                serious hazard to vehicles. The system detects potholes and calculates:\r\n              </p>\r\n              <ul>\r\n                <li>Area in square centimeters</li>\r\n                <li>Depth in centimeters</li>\r\n                <li>Volume</li>\r\n                <li>Classification by size (Small, Medium, Large)</li>\r\n              </ul>\r\n              \r\n              <h5>2. Alligator Cracks</h5>\r\n              <p>\r\n                Alligator cracks are a series of interconnected cracks creating a pattern resembling \r\n                an alligator's scales. These indicate underlying structural weakness. The system \r\n                identifies multiple types of cracks including:\r\n              </p>\r\n              <ul>\r\n                <li>Alligator Cracks</li>\r\n                <li>Edge Cracks</li>\r\n                <li>Hairline Cracks</li>\r\n                <li>Longitudinal Cracks</li>\r\n                <li>Transverse Cracks</li>\r\n              </ul>\r\n              \r\n              <h5>3. Kerbs</h5>\r\n              <p>\r\n                Kerbs are raised edges along a street or path that define boundaries between roadways \r\n                and other areas. The system identifies different kerb conditions including:\r\n              </p>\r\n              <ul>\r\n                <li>Normal/Good Kerbs - Structurally sound and properly visible</li>\r\n                <li>Faded Kerbs - Reduced visibility due to worn paint or weathering</li>\r\n                <li>Damaged Kerbs - Physically damaged or broken kerbs requiring repair</li>\r\n              </ul>\r\n              \r\n              <h5>Location Services & GPS Data</h5>\r\n              <p>\r\n                When using the live camera option, the application can capture GPS coordinates \r\n                to provide precise geolocation data for detected defects. This helps in:\r\n              </p>\r\n              <ul>\r\n                <li>Accurately mapping defect locations</li>\r\n                <li>Creating location-based reports</li>\r\n                <li>Enabling field teams to find specific issues</li>\r\n                <li>Tracking defect patterns by geographic area</li>\r\n              </ul>\r\n              \r\n              <h6>Location Requirements:</h6>\r\n              <ul>\r\n                <li><strong>Secure Connection:</strong> Location services require HTTPS</li>\r\n                <li><strong>Browser Permissions:</strong> You must allow location access when prompted</li>\r\n                <li><strong>Safari Users:</strong> Enable location services in Safari settings</li>\r\n                <li><strong>Mobile Devices:</strong> Ensure location services are enabled in device settings</li>\r\n              </ul>\r\n              \r\n              <div className=\"alert alert-info\">\r\n                <h6><i className=\"fas fa-info-circle me-2\"></i>Troubleshooting Location Issues</h6>\r\n                <p><strong>If location access is denied:</strong></p>\r\n                <ul className=\"mb-2\">\r\n                  <li><strong>Safari:</strong> Settings → Privacy & Security → Location Services</li>\r\n                  <li><strong>Chrome:</strong> Settings → Privacy and security → Site Settings → Location</li>\r\n                  <li><strong>Firefox:</strong> Settings → Privacy & Security → Permissions → Location</li>\r\n                </ul>\r\n                <p><strong>On mobile devices:</strong> Also check your device's location settings and ensure the browser has location permission.</p>\r\n              </div>\r\n\r\n              <h5>How to Use This Module</h5>\r\n              <ol>\r\n                <li>Select the detection type (Potholes, Alligator Cracks, or Kerbs)</li>\r\n                <li>Upload an image or use the camera to capture a photo</li>\r\n                <li>If using the camera, allow location access when prompted for GPS coordinates</li>\r\n                <li>Click the Detect button to analyze the image</li>\r\n                <li>Review the detection results and measurements</li>\r\n              </ol>\r\n              \r\n              <h5>Supported Image Formats</h5>\r\n              <p>\r\n                The system supports all common image formats including:\r\n              </p>\r\n              <ul>\r\n                <li><strong>JPEG/JPG</strong> - Standard format, optimal for processing</li>\r\n                <li><strong>PNG</strong> - Lossless format, good for detailed images</li>\r\n                <li><strong>AVIF</strong> - Modern format, automatically converted to JPG for processing</li>\r\n                <li><strong>WebP</strong> - Google's format, also automatically converted</li>\r\n                <li><strong>Other formats</strong> - BMP, TIFF, etc. are also supported</li>\r\n              </ul>\r\n              \r\n              <div className=\"alert alert-info\">\r\n                <h6><i className=\"fas fa-info-circle me-2\"></i>Automatic Format Conversion</h6>\r\n                <p className=\"mb-0\">\r\n                  <strong>AVIF and WebP images</strong> are automatically converted to JPG format during processing \r\n                  to ensure compatibility with the YOLO detection models. This conversion maintains image quality \r\n                  while ensuring reliable detection results.\r\n                </p>\r\n              </div>\r\n              \r\n              <p>\r\n                The detected defects are automatically recorded in the database for tracking \r\n                and analysis in the Dashboard module.\r\n              </p>\r\n            </Card.Body>\r\n          </Card>\r\n        </Tab>\r\n      </Tabs>\r\n\r\n      {/* Classification Error Modal */}\r\n      {/* / <Modal\r\n        show={showClassificationModal}\r\n        onHide={() => setShowClassificationModal(false)}\r\n        centered\r\n      >\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>\r\n            <i className=\"fas fa-exclamation-triangle text-warning me-2\"></i>\r\n            Road Detection Failed\r\n          </Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <div className=\"text-center\">\r\n            <div className=\"mb-3\">\r\n              <i className=\"fas fa-road fa-3x text-muted\"></i>\r\n            </div>\r\n            <h5 className=\"text-danger mb-3\">No Road Detected</h5>\r\n            <p className=\"mb-3\">\r\n              {classificationError || 'The uploaded image does not appear to contain a road. Please upload an image that clearly shows a road surface for defect detection.'}\r\n            </p>\r\n            <div className=\"alert alert-info\">\r\n              <strong>Tips for better results:</strong>\r\n              <ul className=\"mb-0 mt-2 text-start\">\r\n                <li>Ensure the image clearly shows a road surface</li>\r\n                <li>Avoid images with only buildings, sky, or vegetation</li>\r\n                <li>Make sure the road takes up a significant portion of the image</li>\r\n                <li>Use good lighting conditions for clearer road visibility</li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button\r\n            variant=\"primary\"\r\n            onClick={() => setShowClassificationModal(false)}\r\n          >\r\n            Try Another Image\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal> */}\r\n\r\n      {/* Image Modal for Full-Size View */}\r\n      <Modal\r\n        show={showImageModal}\r\n        onHide={() => setShowImageModal(false)}\r\n        size=\"lg\"\r\n        centered\r\n      >\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>\r\n            <i className=\"fas fa-image me-2\"></i>\r\n            Image View {selectedImageData?.filename && (\r\n              <small className=\"text-muted\">- {selectedImageData.filename}</small>\r\n            )}\r\n          </Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          {selectedImageData && (\r\n            <div className=\"text-center\">\r\n              <div className=\"mb-4\">\r\n                <h6 className=\"mb-3\">\r\n                  <i className=\"fas fa-camera me-2\"></i>\r\n                  Original Image\r\n                </h6>\r\n                <img\r\n                  src={selectedImageData.originalImage}\r\n                  alt=\"Original Image\"\r\n                  className=\"img-fluid\"\r\n                  style={{\r\n                    maxHeight: '400px',\r\n                    borderRadius: '8px',\r\n                    border: '2px solid #dee2e6',\r\n                    boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\r\n                  }}\r\n                />\r\n              </div>\r\n              {selectedImageData.processedImage && selectedImageData.isRoad && (\r\n                <div className=\"mt-4\">\r\n                  <h6 className=\"mb-3\">\r\n                    <i className=\"fas fa-search me-2\"></i>\r\n                    Processed Image (Detection Results)\r\n                  </h6>\r\n                  <img\r\n                    src={selectedImageData.processedImage}\r\n                    alt=\"Processed Image\"\r\n                    className=\"img-fluid\"\r\n                    style={{\r\n                      maxHeight: '400px',\r\n                      borderRadius: '8px',\r\n                      border: '2px solid #28a745',\r\n                      boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\r\n                    }}\r\n                  />\r\n                </div>\r\n              )}\r\n              {!selectedImageData.isRoad && (\r\n                <div className=\"mt-3\">\r\n                  <Alert variant=\"info\">\r\n                    <i className=\"fas fa-info-circle me-2\"></i>\r\n                    This image was classified as non-road and therefore no defect detection was performed.\r\n                  </Alert>\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button\r\n            variant=\"secondary\"\r\n            onClick={() => setShowImageModal(false)}\r\n          >\r\n            Close\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </Container>\r\n  );\r\n};\r\n\r\n// Add CSS styles for the enhanced detection table\r\nconst styles = `\r\n  .detection-table-container {\r\n    max-height: 600px;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .detection-table-container th {\r\n    position: sticky;\r\n    top: 0;\r\n    background-color: #f8f9fa;\r\n    z-index: 10;\r\n  }\r\n\r\n  .detection-table-container th:hover {\r\n    background-color: #e9ecef;\r\n  }\r\n\r\n  .detection-summary-cards .card {\r\n    transition: transform 0.2s ease-in-out;\r\n  }\r\n\r\n  .detection-summary-cards .card:hover {\r\n    transform: translateY(-2px);\r\n  }\r\n\r\n  .table-responsive {\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n  }\r\n\r\n  .badge {\r\n    font-size: 0.75em;\r\n  }\r\n\r\n  .avif-indicator {\r\n    position: absolute;\r\n    top: 5px;\r\n    left: 5px;\r\n    background-color: rgba(23, 162, 184, 0.9);\r\n    color: white;\r\n    padding: 2px 6px;\r\n    border-radius: 12px;\r\n    font-size: 10px;\r\n    display: flex;\r\n    align-items: center;\r\n    z-index: 5;\r\n  }\r\n\r\n  .image-thumbnail {\r\n    position: relative;\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    .detection-summary-cards .col-md-3 {\r\n      margin-bottom: 1rem;\r\n    }\r\n\r\n    .d-flex.gap-2.flex-wrap {\r\n      flex-direction: column;\r\n    }\r\n\r\n    .d-flex.gap-2.flex-wrap .btn {\r\n      margin-bottom: 0.5rem;\r\n    }\r\n  }\r\n`;\r\n\r\n// Inject styles into the document head\r\nif (typeof document !== 'undefined') {\r\n  const styleSheet = document.createElement('style');\r\n\r\n  styleSheet.innerText = styles;\r\n  document.head.appendChild(styleSheet);\r\n}\r\n\r\nexport default Pavement;\r\n \r\n\r\n\r\n"], "mappings": "AACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,OAASC,SAAS,CAAEC,IAAI,CAAEC,MAAM,CAAEC,IAAI,CAAEC,IAAI,CAAEC,GAAG,CAAEC,KAAK,CAAEC,OAAO,CAAEC,cAAc,CAAEC,OAAO,CAAEC,KAAK,KAAQ,iBAAiB,CAC1H,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,cAAc,CACjC,MAAO,gBAAgB,CACvB,MAAO,CAAAC,aAAa,KAAM,wBAAwB,CAClD,MAAO,CAAAC,oBAAoB,KAAM,oCAAoC,CACrE,OAASC,qBAAqB,CAAEC,uBAAuB,KAAQ,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAGzF,KAAM,CAAAC,QAAQ,CAAGA,CAAA,GAAM,CACrB,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAG5B,QAAQ,CAAC,WAAW,CAAC,CACvD,KAAM,CAAC6B,aAAa,CAAEC,gBAAgB,CAAC,CAAG9B,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAAC+B,UAAU,CAAEC,aAAa,CAAC,CAAGhC,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACiC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC5D,KAAM,CAACmC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC5D,KAAM,CAACqC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGtC,QAAQ,CAAC,CAAC,CAAC,CAC7D,KAAM,CAACuC,cAAc,CAAEC,iBAAiB,CAAC,CAAGxC,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACyC,OAAO,CAAEC,UAAU,CAAC,CAAG1C,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC2C,OAAO,CAAEC,UAAU,CAAC,CAAG5C,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC6C,KAAK,CAAEC,QAAQ,CAAC,CAAG9C,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC+C,YAAY,CAAEC,eAAe,CAAC,CAAGhD,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACiD,WAAW,CAAEC,cAAc,CAAC,CAAGlD,QAAQ,CAAC,eAAe,CAAC,CAC/D,KAAM,CAACmD,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGpD,QAAQ,CAAC,aAAa,CAAC,CACzE,KAAM,CAACqD,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGtD,QAAQ,CAAC,SAAS,CAAC,CACvE,KAAM,CAACuD,aAAa,CAAEC,gBAAgB,CAAC,CAAGxD,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACyD,eAAe,CAAEC,kBAAkB,CAAC,CAAG1D,QAAQ,CAAC,KAAK,CAAC,CAE7D;AACA,KAAM,CAAC2D,YAAY,CAAEC,eAAe,CAAC,CAAG5D,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC6D,eAAe,CAAEC,kBAAkB,CAAC,CAAG9D,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAC+D,cAAc,CAAEC,iBAAiB,CAAC,CAAGhE,QAAQ,CAAC,CAAC,CAAC,CAEvD;AACA,KAAM,CAACiE,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGlE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAElE;AACA,KAAM,CAACmE,uBAAuB,CAAEC,0BAA0B,CAAC,CAAGpE,QAAQ,CAAC,KAAK,CAAC,CAC7E,KAAM,CAACqE,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGtE,QAAQ,CAAC,EAAE,CAAC,CAClE,KAAM,CAACuE,cAAc,CAAEC,iBAAiB,CAAC,CAAGxE,QAAQ,CAAC,CAAC,CAAC,CAEvD;AACA,KAAM,CAACyE,cAAc,CAAEC,iBAAiB,CAAC,CAAG1E,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAAC2E,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG5E,QAAQ,CAAC,IAAI,CAAC,CAEhE;AACA,KAAM,CAAC6E,WAAW,CAAEC,cAAc,CAAC,CAAG9E,QAAQ,CAAC,KAAK,CAAC,CAAE;AAIvD;AACA,KAAM,CAAC+E,yBAAyB,CAAEC,4BAA4B,CAAC,CAAGhF,QAAQ,CAAC,KAAK,CAAC,CAEjF;AACA,KAAM,CAACiF,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGlF,QAAQ,CAAC,KAAK,CAAC,CAAE;AACzE,KAAM,CAACmF,UAAU,CAAEC,aAAa,CAAC,CAAGpF,QAAQ,CAAC,CAAEqF,GAAG,CAAE,IAAI,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE7E;AAEA,KAAM,CAAAC,SAAS,CAAGtF,MAAM,CAAC,IAAI,CAAC,CAC9B,KAAM,CAAAuF,YAAY,CAAGvF,MAAM,CAAC,IAAI,CAAC,CACjC,KAAM,CAAEwF,QAAS,CAAC,CAAGzE,aAAa,CAAC,CAAC,CAEpC;AACA,KAAM,CAAA0E,eAAe,cACnBnE,KAAA,CAACX,OAAO,EAAC+E,EAAE,CAAC,kBAAkB,CAACC,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAAC,QAAA,eAC1DzE,IAAA,CAACT,OAAO,CAACmF,MAAM,EAACC,EAAE,CAAC,IAAI,CAAAF,QAAA,CAAC,sCAA0B,CAAgB,CAAC,cACnEvE,KAAA,CAACX,OAAO,CAACqF,IAAI,EAAAH,QAAA,eACXzE,IAAA,MAAGuE,KAAK,CAAE,CAAEM,YAAY,CAAE,MAAO,CAAE,CAAAJ,QAAA,CAAC,yCAEpC,CAAG,CAAC,cACJvE,KAAA,OAAIqE,KAAK,CAAE,CAAEM,YAAY,CAAE,GAAG,CAAEC,WAAW,CAAE,MAAO,CAAE,CAAAL,QAAA,eACpDzE,IAAA,OAAAyE,QAAA,CAAI,sCAAoC,CAAI,CAAC,cAC7CzE,IAAA,OAAAyE,QAAA,CAAI,oBAAkB,CAAI,CAAC,cAC3BzE,IAAA,OAAAyE,QAAA,CAAI,oCAAkC,CAAI,CAAC,cAC3CzE,IAAA,OAAAyE,QAAA,CAAI,qDAAmD,CAAI,CAAC,EAC1D,CAAC,EACO,CAAC,EACR,CACV,CAED;AACA,KAAM,CAAAM,uBAAuB,CAAG,KAAAA,CAAA,GAAY,CAC1C,GAAI,CAACC,SAAS,CAACC,WAAW,EAAI,CAACD,SAAS,CAACC,WAAW,CAACC,KAAK,CAAE,CAC1D;AACA,MAAO,QAAQ,CACjB,CAEA,GAAI,CACF,KAAM,CAAAC,UAAU,CAAG,KAAM,CAAAH,SAAS,CAACC,WAAW,CAACC,KAAK,CAAC,CAAEE,IAAI,CAAE,aAAc,CAAC,CAAC,CAC7E,MAAO,CAAAD,UAAU,CAACE,KAAK,CACzB,CAAE,MAAOC,GAAG,CAAE,CACZC,OAAO,CAACC,IAAI,CAAC,yCAAyC,CAAEF,GAAG,CAAC,CAC5D,MAAO,QAAQ,CACjB,CACF,CAAC,CAED;AACA,KAAM,CAAAG,eAAe,CAAGA,CAAA,GAAM,CAC5B,MAAO,IAAI,CAAAC,OAAO,CAAC,CAACC,OAAO,CAAEC,MAAM,GAAK,CACtC;AACA,GAAI,CAACZ,SAAS,CAACa,WAAW,CAAE,CAC1BD,MAAM,CAAC,GAAI,CAAAE,KAAK,CAAC,8CAA8C,CAAC,CAAC,CACjE,OACF,CAEA;AACA,GAAI,CAACC,MAAM,CAACC,eAAe,CAAE,CAC3BJ,MAAM,CAAC,GAAI,CAAAE,KAAK,CAAC,+CAA+C,CAAC,CAAC,CAClE,OACF,CAEA,KAAM,CAAAG,OAAO,CAAG,CACdC,kBAAkB,CAAE,IAAI,CACxBC,OAAO,CAAE,KAAK,CAAE;AAChBC,UAAU,CAAE,KAAM;AACpB,CAAC,CAEDpB,SAAS,CAACa,WAAW,CAACQ,kBAAkB,CACrCC,QAAQ,EAAK,CACZX,OAAO,CAACW,QAAQ,CAAC,CACnB,CAAC,CACA9E,KAAK,EAAK,CACT,GAAI,CAAA+E,YAAY,CAAG,6BAA6B,CAEhD,OAAQ/E,KAAK,CAACgF,IAAI,EAChB,IAAK,CAAAhF,KAAK,CAACiF,iBAAiB,CAC1BF,YAAY,CAAG,sFAAsF,CACrG,MACF,IAAK,CAAA/E,KAAK,CAACkF,oBAAoB,CAC7BH,YAAY,CAAG,wDAAwD,CACvE,MACF,IAAK,CAAA/E,KAAK,CAACmF,OAAO,CAChBJ,YAAY,CAAG,+CAA+C,CAC9D,MACF,QACEA,YAAY,CAAG,mBAAmB/E,KAAK,CAACoF,OAAO,EAAE,CACjD,MACJ,CAEAhB,MAAM,CAAC,GAAI,CAAAE,KAAK,CAACS,YAAY,CAAC,CAAC,CACjC,CAAC,CACDN,OACF,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAY,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxCxE,kBAAkB,CAAC,IAAI,CAAC,CACxBF,gBAAgB,CAAC,EAAE,CAAC,CAEpB,GAAI,CACF;AACA,KAAM,CAAA2E,eAAe,CAAG,KAAM,CAAA/B,uBAAuB,CAAC,CAAC,CACvD9C,qBAAqB,CAAC6E,eAAe,CAAC,CAEtC;AACA,GAAIA,eAAe,GAAK,QAAQ,CAAE,CAChC,KAAM,CAAAC,QAAQ,CAAG,sDAAsD,CACvD,+DAA+D,CAC/D,2CAA2C,CAC3C,4CAA4C,CAC5C,uCAAuC,CACvD5E,gBAAgB,CAAC4E,QAAQ,CAAC,CAC1BlF,cAAc,CAAC,mBAAmB,CAAC,CACnC,OACF,CAEA;AACA,KAAM,CAAAyE,QAAQ,CAAG,KAAM,CAAAb,eAAe,CAAC,CAAC,CACxC,KAAM,CAAEuB,QAAQ,CAAEC,SAAU,CAAC,CAAGX,QAAQ,CAACY,MAAM,CAE/C;AACA,KAAM,CAAAC,eAAe,CAAG,GAAGH,QAAQ,CAACI,OAAO,CAAC,CAAC,CAAC,KAAKH,SAAS,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE,CACzEvF,cAAc,CAACsF,eAAe,CAAC,CAC/BlF,qBAAqB,CAAC,SAAS,CAAC,CAChCE,gBAAgB,CAAC,EAAE,CAAC,CAEpBoD,OAAO,CAAC8B,GAAG,CAAC,oBAAoB,CAAE,CAAEL,QAAQ,CAAEC,SAAS,CAAEK,QAAQ,CAAEhB,QAAQ,CAACY,MAAM,CAACI,QAAS,CAAC,CAAC,CAEhG,CAAE,MAAO9F,KAAK,CAAE,CACd+D,OAAO,CAAC/D,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChDW,gBAAgB,CAACX,KAAK,CAACoF,OAAO,CAAC,CAC/B/E,cAAc,CAAC,gBAAgB,CAAC,CAEhC;AACA,GAAIL,KAAK,CAACoF,OAAO,CAACW,QAAQ,CAAC,QAAQ,CAAC,CAAE,CACpCtF,qBAAqB,CAAC,QAAQ,CAAC,CACjC,CACF,CAAC,OAAS,CACRI,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CACF,CAAC,CAED;AACA,KAAM,CAAAmF,gBAAgB,CAAIC,CAAC,EAAK,CAC9B,KAAM,CAAAC,KAAK,CAAGC,KAAK,CAACC,IAAI,CAACH,CAAC,CAACI,MAAM,CAACH,KAAK,CAAC,CACxC,GAAIA,KAAK,CAACI,MAAM,CAAG,CAAC,CAAE,CACpB;AACA,KAAM,CAAAC,UAAU,CAAGlI,qBAAqB,CAAC6H,KAAK,CAAE,OAAO,CAAE,oBAAoB,CAAC,CAC9E,GAAI,CAACK,UAAU,CAACC,OAAO,CAAE,CACvBlI,uBAAuB,CAACiI,UAAU,CAACxB,YAAY,CAAE9E,QAAQ,CAAC,CAC1D;AACA,GAAIgG,CAAC,CAACI,MAAM,CAAE,CACZJ,CAAC,CAACI,MAAM,CAACI,KAAK,CAAG,EAAE,CACrB,CACA,OACF,CAEA;AACAxG,QAAQ,CAAC,EAAE,CAAC,CAEZd,aAAa,CAAC,CAAC,GAAGD,UAAU,CAAE,GAAGgH,KAAK,CAAC,CAAC,CAExC;AACAA,KAAK,CAACQ,OAAO,CAACC,IAAI,EAAI,CACpB,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,SAAS,CAAG,IAAM,CACvBzH,mBAAmB,CAAC0H,IAAI,GAAK,CAC3B,GAAGA,IAAI,CACP,CAACJ,IAAI,CAAC/C,IAAI,EAAGgD,MAAM,CAACI,MACtB,CAAC,CAAC,CAAC,CACL,CAAC,CACDJ,MAAM,CAACK,aAAa,CAACN,IAAI,CAAC,CAE1B;AACApH,mBAAmB,CAACwH,IAAI,GAAK,CAC3B,GAAGA,IAAI,CACP,CAACJ,IAAI,CAAC/C,IAAI,EAAG,eACf,CAAC,CAAC,CAAC,CAEH;AACA,GAAI+C,IAAI,CAAC/C,IAAI,CAACsD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAE,CAC7CpD,OAAO,CAAC8B,GAAG,CAAC,uBAAuBc,IAAI,CAAC/C,IAAI,6DAA6D,CAAC,CAC5G,CACF,CAAC,CAAC,CAEF;AACAjE,iBAAiB,CAAC,IAAI,CAAC,CACvBE,UAAU,CAAC,IAAI,CAAC,CAChBI,QAAQ,CAAC,EAAE,CAAC,CACd,CACF,CAAC,CAED;AACA,KAAM,CAAAmH,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,KAAM,CAAAC,QAAQ,CAAG3E,SAAS,CAAC4E,OAAO,CAACC,aAAa,CAAC,CAAC,CAClD,GAAIF,QAAQ,CAAE,CACZ;AACA,GAAIjH,WAAW,GAAK,eAAe,EAAIA,WAAW,GAAK,gBAAgB,CAAE,CACvE,KAAM,CAAAiF,qBAAqB,CAAC,CAAC,CAC/B,CAEA,KAAM,CAAAmC,SAAS,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAC1C,KAAM,CAAAC,QAAQ,CAAG,kBAAkBH,SAAS,MAAM,CAClD,KAAM,CAAAI,kBAAkB,CAAGxH,WAAW,CAAE;AAExCjB,aAAa,CAAC,CAAC,GAAGD,UAAU,CAAEyI,QAAQ,CAAC,CAAC,CACxCtI,mBAAmB,CAAC0H,IAAI,GAAK,CAC3B,GAAGA,IAAI,CACP,CAACY,QAAQ,EAAGN,QACd,CAAC,CAAC,CAAC,CACH9H,mBAAmB,CAACwH,IAAI,GAAK,CAC3B,GAAGA,IAAI,CACP,CAACY,QAAQ,EAAGC,kBACd,CAAC,CAAC,CAAC,CACHnI,oBAAoB,CAACP,UAAU,CAACoH,MAAM,CAAC,CAEvC3G,iBAAiB,CAAC,IAAI,CAAC,CACvBE,UAAU,CAAC,IAAI,CAAC,CAChBI,QAAQ,CAAC,EAAE,CAAC,CAEZ;AACA8D,OAAO,CAAC8B,GAAG,CAAC,kCAAkC,CAAE+B,kBAAkB,CAAC,CACrE,CACF,CAAC,CAED;AACA,KAAM,CAAAC,uBAAuB,CAAGA,CAAA,GAAM,CACpC,GAAIC,MAAM,CAACC,IAAI,CAAC3I,gBAAgB,CAAC,CAACkH,MAAM,GAAK,CAAC,CAAE,CAC9C,MAAO,CAAAlG,WAAW,CAAE;AACtB,CAEA,KAAM,CAAA4H,eAAe,CAAGF,MAAM,CAACC,IAAI,CAAC3I,gBAAgB,CAAC,CAACI,iBAAiB,CAAC,CACxE,MAAO,CAAAF,gBAAgB,CAAC0I,eAAe,CAAC,EAAI,eAAe,CAC7D,CAAC,CAED;AACA,KAAM,CAAAC,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,KAAM,CAAAC,cAAc,CAAG,CAAChI,YAAY,CACpCC,eAAe,CAAC+H,cAAc,CAAC,CAE/B,GAAIA,cAAc,CAAE,CAClB;AACA,KAAM,CAAA7C,qBAAqB,CAAC,CAAC,CAC/B,CAAC,IAAM,CACL;AACA;AACA,GAAIyC,MAAM,CAACC,IAAI,CAAC3I,gBAAgB,CAAC,CAACkH,MAAM,GAAK,CAAC,CAAE,CAC9CjG,cAAc,CAAC,eAAe,CAAC,CAC/BM,gBAAgB,CAAC,EAAE,CAAC,CACpBF,qBAAqB,CAAC,SAAS,CAAC,CAClC,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAA0H,uBAAuB,CAAGA,CAAA,GAAM,CACpC5H,oBAAoB,CAACwG,IAAI,EAAIA,IAAI,GAAK,aAAa,CAAG,MAAM,CAAG,aAAa,CAAC,CAC/E,CAAC,CAED;AACA,KAAM,CAAAqB,yBAAyB,CAAIrD,YAAY,EAAK,CAClDtD,sBAAsB,CAACsD,YAAY,CAAC,CACpCxD,0BAA0B,CAAC,IAAI,CAAC,CAChCtB,QAAQ,CAAC,EAAE,CAAC,CAAE;AAChB,CAAC,CAED;AACA,KAAM,CAAAoI,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChCtI,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CAEZ,GAAI,CACF;AACA,KAAM,CAAAqI,UAAU,CAAGC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CACjD,KAAM,CAAAC,IAAI,CAAGH,UAAU,CAAGI,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC,CAAG,IAAI,CAEvD;AACA,KAAM,CAAAM,mBAAmB,CAAGd,MAAM,CAACe,MAAM,CAACzJ,gBAAgB,CAAC,CAACI,iBAAiB,CAAC,CAE9E,GAAI,CAACoJ,mBAAmB,CAAE,CACxB3I,QAAQ,CAAC,kCAAkC,CAAC,CAC5CF,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA;AACA,KAAM,CAAA+I,gBAAgB,CAAGjB,uBAAuB,CAAC,CAAC,CAElD;AACA,KAAM,CAAAkB,SAAS,CAAGjB,MAAM,CAACC,IAAI,CAAC3I,gBAAgB,CAAC,CAC/C,KAAM,CAAA4I,eAAe,CAAGe,SAAS,CAACvJ,iBAAiB,CAAC,CAEpD;AACA,KAAM,CAAAwJ,WAAW,CAAG,CAClBC,KAAK,CAAEL,mBAAmB,CAC1BxI,WAAW,CAAE0I,gBAAgB,CAC7BI,QAAQ,CAAE,CAAAT,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAES,QAAQ,GAAI,SAAS,CACrCC,IAAI,CAAE,CAAAV,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEU,IAAI,GAAI,SAAS,CAC7BC,wBAAwB,CAAE,CAAClH,yBAC7B,CAAC,CAED;AACA,GAAI,CAAAmH,QAAQ,CACZ,OAAOrK,aAAa,EAClB,IAAK,KAAK,CACRqK,QAAQ,CAAG,0BAA0B,CACrC,MACF,IAAK,UAAU,CACbA,QAAQ,CAAG,+BAA+B,CAC1C,MACF,IAAK,QAAQ,CACXA,QAAQ,CAAG,6BAA6B,CACxC,MACF,IAAK,OAAO,CACVA,QAAQ,CAAG,4BAA4B,CACvC,MACF,QACEA,QAAQ,CAAG,0BAA0B,CACzC,CAEA;AACA,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAArL,KAAK,CAACsL,IAAI,CAACF,QAAQ,CAAEL,WAAW,CAAC,CAExD;AACA,GAAIM,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAE,KAAAC,qBAAA,CACzB;AACA,KAAM,CAAAC,WAAW,CAAGL,QAAQ,CAACE,IAAI,CAACI,SAAS,GAAK,KAAK,CACrD,KAAM,CAAAC,MAAM,CAAG,EAAAH,qBAAA,CAAAJ,QAAQ,CAACE,IAAI,CAACM,cAAc,UAAAJ,qBAAA,iBAA5BA,qBAAA,CAA8BK,OAAO,GAAI,KAAK,CAE7D;AACApK,iBAAiB,CAAC2J,QAAQ,CAACE,IAAI,CAACQ,eAAe,CAAC,CAChDnK,UAAU,CAACyJ,QAAQ,CAACE,IAAI,CAAC,CAEzB;AACA,KAAM,CAAAS,gBAAgB,CAAG,CACvBC,QAAQ,CAAEZ,QAAQ,CAACE,IAAI,CAACU,QAAQ,EAAI,EAAE,CACtCC,MAAM,CAAEb,QAAQ,CAACE,IAAI,CAACW,MAAM,EAAI,EAAE,CAClCC,KAAK,CAAEd,QAAQ,CAACE,IAAI,CAACY,KAAK,EAAI,EAChC,CAAC,CAED;AACA,KAAM,CAAAC,WAAW,CAAG,CAClB1C,QAAQ,CAAEK,eAAe,CACzByB,OAAO,CAAE,IAAI,CACbG,SAAS,CAAED,WAAW,CACtBE,MAAM,CAAEA,MAAM,CACdC,cAAc,CAAER,QAAQ,CAACE,IAAI,CAACM,cAAc,CAC5CpK,cAAc,CAAE4J,QAAQ,CAACE,IAAI,CAACQ,eAAe,CAC7CM,aAAa,CAAE1B,mBAAmB,CAAE;AACpCY,IAAI,CAAEF,QAAQ,CAACE,IAAI,CACnBS,gBAAgB,CAAEA,gBAAgB,CAClCM,wBAAwB,CAAErI,yBAAyB,CAAE;AACrDsI,eAAe,CAAE,CACfN,QAAQ,CAAED,gBAAgB,CAACC,QAAQ,CAAC5D,MAAM,CAC1C6D,MAAM,CAAEF,gBAAgB,CAACE,MAAM,CAAC7D,MAAM,CACtC8D,KAAK,CAAEH,gBAAgB,CAACG,KAAK,CAAC9D,MAAM,CACpCmE,KAAK,CAAER,gBAAgB,CAACC,QAAQ,CAAC5D,MAAM,CAAG2D,gBAAgB,CAACE,MAAM,CAAC7D,MAAM,CAAG2D,gBAAgB,CAACG,KAAK,CAAC9D,MACpG,CACF,CAAC,CAED;AACAvF,eAAe,CAAC,CAACsJ,WAAW,CAAC,CAAC,CAE9B;AACA;AACAhJ,sBAAsB,CAAC0F,IAAI,GAAK,CAC9B,GAAGA,IAAI,CACP,CAACiB,eAAe,EAAG,CACjBsC,aAAa,CAAE1B,mBAAmB,CAClClJ,cAAc,CAAEmK,MAAM,CAAGP,QAAQ,CAACE,IAAI,CAACQ,eAAe,CAAG,IAAI,CAC7DpK,OAAO,CAAE0J,QAAQ,CAACE,IAAI,CACtBK,MAAM,CAAEA,MACV,CACF,CAAC,CAAC,CAAC,CAED;AACA1K,aAAa,CAAC,EAAE,CAAC,CACjBE,mBAAmB,CAAC,CAAC,CAAC,CAAC,CACvBE,mBAAmB,CAAC,CAAC,CAAC,CAAC,CACvBE,oBAAoB,CAAC,CAAC,CAAC,CAEvB;AACAY,cAAc,CAAC,eAAe,CAAC,CAC/BM,gBAAgB,CAAC,EAAE,CAAC,CACpBF,qBAAqB,CAAC,SAAS,CAAC,CAEhC,GAAIkC,YAAY,CAAC2E,OAAO,CAAE,CACxB3E,YAAY,CAAC2E,OAAO,CAACb,KAAK,CAAG,EAAE,CACjC,CACJ,CAAC,IAAM,CACL,KAAM,CAAA1B,YAAY,CAAGuE,QAAQ,CAACE,IAAI,CAACpE,OAAO,EAAI,kBAAkB,CAEhE;AACA,KAAM,CAAAiF,WAAW,CAAG,CAClB1C,QAAQ,CAAEK,eAAe,CACzByB,OAAO,CAAE,KAAK,CACdG,SAAS,CAAE,KAAK,CAChBC,MAAM,CAAE,KAAK,CACb7J,KAAK,CAAE+E,YAAY,CACnB2F,qBAAqB,CAAE3F,YAAY,CAACgB,QAAQ,CAAC,kBAAkB,CACjE,CAAC,CAED;AACAhF,eAAe,CAAC,CAACsJ,WAAW,CAAC,CAAC,CAE9BpK,QAAQ,CAAC8E,YAAY,CAAC,CACxB,CACF,CAAE,MAAO/E,KAAK,CAAE,KAAA2K,eAAA,CAAAC,oBAAA,CACd,KAAM,CAAA7F,YAAY,CAAG,EAAA4F,eAAA,CAAA3K,KAAK,CAACsJ,QAAQ,UAAAqB,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBnB,IAAI,UAAAoB,oBAAA,iBAApBA,oBAAA,CAAsBxF,OAAO,GAAI,uDAAuD,CAE7G;AACA,KAAM,CAAA2D,SAAS,CAAGjB,MAAM,CAACC,IAAI,CAAC3I,gBAAgB,CAAC,CAC/C,KAAM,CAAA4I,eAAe,CAAGe,SAAS,CAACvJ,iBAAiB,CAAC,CAEpD;AACA,KAAM,CAAA6K,WAAW,CAAG,CAClB1C,QAAQ,CAAEK,eAAe,CACzByB,OAAO,CAAE,KAAK,CACdG,SAAS,CAAE,KAAK,CAChBC,MAAM,CAAE,KAAK,CACb7J,KAAK,CAAE+E,YAAY,CACnB2F,qBAAqB,CAAE3F,YAAY,CAACgB,QAAQ,CAAC,kBAAkB,CACjE,CAAC,CAED;AACAhF,eAAe,CAAC,CAACsJ,WAAW,CAAC,CAAC,CAE9B;AACA,GAAItF,YAAY,CAACgB,QAAQ,CAAC,kBAAkB,CAAC,CAAE,CAC7CqC,yBAAyB,CAACrD,YAAY,CAAC,CACzC,CAAC,IAAM,CACL9E,QAAQ,CAAC8E,YAAY,CAAC,CACxB,CACF,CAAC,OAAS,CACRhF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA8K,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI/C,MAAM,CAACC,IAAI,CAAC3I,gBAAgB,CAAC,CAACkH,MAAM,GAAK,CAAC,CAAE,CAC9CrG,QAAQ,CAAC,sBAAsB,CAAC,CAChC,OACF,CAEAgB,kBAAkB,CAAC,IAAI,CAAC,CACxBhB,QAAQ,CAAC,EAAE,CAAC,CACZc,eAAe,CAAC,EAAE,CAAC,CACnBI,iBAAiB,CAAC,CAAC,CAAC,CACpBQ,iBAAiB,CAACmG,MAAM,CAACC,IAAI,CAAC3I,gBAAgB,CAAC,CAACkH,MAAM,CAAC,CAEvD;AACA,KAAM,CAAAgC,UAAU,CAAGC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CACjD,KAAM,CAAAC,IAAI,CAAGH,UAAU,CAAGI,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC,CAAG,IAAI,CAEvD,GAAI,CACF;AACA,GAAI,CAAAe,QAAQ,CACZ,OAAOrK,aAAa,EAClB,IAAK,KAAK,CACRqK,QAAQ,CAAG,0BAA0B,CACrC,MACF,IAAK,UAAU,CACbA,QAAQ,CAAG,+BAA+B,CAC1C,MACF,IAAK,QAAQ,CACXA,QAAQ,CAAG,6BAA6B,CACxC,MACF,IAAK,OAAO,CACVA,QAAQ,CAAG,4BAA4B,CACvC,MACF,QACEA,QAAQ,CAAG,0BAA0B,CACzC,CAEA,KAAM,CAAAzJ,OAAO,CAAG,EAAE,CAClB,KAAM,CAAAmJ,SAAS,CAAGjB,MAAM,CAACC,IAAI,CAAC3I,gBAAgB,CAAC,CAE/C;AACA,IAAK,GAAI,CAAA0L,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAG/B,SAAS,CAACzC,MAAM,CAAEwE,CAAC,EAAE,CAAE,CACzC,KAAM,CAAAnD,QAAQ,CAAGoB,SAAS,CAAC+B,CAAC,CAAC,CAC7B,KAAM,CAAAC,SAAS,CAAG3L,gBAAgB,CAACuI,QAAQ,CAAC,CAE5C,GAAI,CACF;AACAlI,oBAAoB,CAACqL,CAAC,CAAC,CAEvB;AACA,KAAM,CAAAhC,gBAAgB,CAAGxJ,gBAAgB,CAACqI,QAAQ,CAAC,EAAI,eAAe,CAEtE;AACA,KAAM,CAAAqB,WAAW,CAAG,CAClBC,KAAK,CAAE8B,SAAS,CAChB3K,WAAW,CAAE0I,gBAAgB,CAC7BI,QAAQ,CAAE,CAAAT,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAES,QAAQ,GAAI,SAAS,CACrCC,IAAI,CAAE,CAAAV,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEU,IAAI,GAAI,SAAS,CAC7BC,wBAAwB,CAAE,CAAClH,yBAC7B,CAAC,CAED;AACA,KAAM,CAAAoH,QAAQ,CAAG,KAAM,CAAArL,KAAK,CAACsL,IAAI,CAACF,QAAQ,CAAEL,WAAW,CAAC,CAExD,GAAIM,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAE,KAAAuB,sBAAA,CACzB;AACA,KAAM,CAAArB,WAAW,CAAGL,QAAQ,CAACE,IAAI,CAACI,SAAS,GAAK,KAAK,CACrD,KAAM,CAAAC,MAAM,CAAG,EAAAmB,sBAAA,CAAA1B,QAAQ,CAACE,IAAI,CAACM,cAAc,UAAAkB,sBAAA,iBAA5BA,sBAAA,CAA8BjB,OAAO,GAAI,KAAK,CAE7D,GAAIJ,WAAW,EAAIE,MAAM,CAAE,CACzB;AACAlK,iBAAiB,CAAC2J,QAAQ,CAACE,IAAI,CAACQ,eAAe,CAAC,CAChDnK,UAAU,CAACyJ,QAAQ,CAACE,IAAI,CAAC,CAC3B,CAEA;AACA,KAAM,CAAAS,gBAAgB,CAAG,CACvBC,QAAQ,CAAEZ,QAAQ,CAACE,IAAI,CAACU,QAAQ,EAAI,EAAE,CACtCC,MAAM,CAAEb,QAAQ,CAACE,IAAI,CAACW,MAAM,EAAI,EAAE,CAClCC,KAAK,CAAEd,QAAQ,CAACE,IAAI,CAACY,KAAK,EAAI,EAChC,CAAC,CAEDxK,OAAO,CAACqL,IAAI,CAAC,CACXtD,QAAQ,CACR8B,OAAO,CAAE,IAAI,CACbG,SAAS,CAAED,WAAW,CACtBE,MAAM,CAAEA,MAAM,CACdC,cAAc,CAAER,QAAQ,CAACE,IAAI,CAACM,cAAc,CAC5CpK,cAAc,CAAE4J,QAAQ,CAACE,IAAI,CAACQ,eAAe,CAC7CM,aAAa,CAAES,SAAS,CAAE;AAC1BvB,IAAI,CAAEF,QAAQ,CAACE,IAAI,CACnBS,gBAAgB,CAAEA,gBAAgB,CAClCM,wBAAwB,CAAErI,yBAAyB,CAAE;AACrDsI,eAAe,CAAE,CACfN,QAAQ,CAAED,gBAAgB,CAACC,QAAQ,CAAC5D,MAAM,CAC1C6D,MAAM,CAAEF,gBAAgB,CAACE,MAAM,CAAC7D,MAAM,CACtC8D,KAAK,CAAEH,gBAAgB,CAACG,KAAK,CAAC9D,MAAM,CACpCmE,KAAK,CAAER,gBAAgB,CAACC,QAAQ,CAAC5D,MAAM,CAAG2D,gBAAgB,CAACE,MAAM,CAAC7D,MAAM,CAAG2D,gBAAgB,CAACG,KAAK,CAAC9D,MACpG,CACF,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,KAAM,CAAAvB,YAAY,CAAGuE,QAAQ,CAACE,IAAI,CAACpE,OAAO,EAAI,kBAAkB,CAChExF,OAAO,CAACqL,IAAI,CAAC,CACXtD,QAAQ,CACR8B,OAAO,CAAE,KAAK,CACdG,SAAS,CAAE,KAAK,CAChBC,MAAM,CAAE,KAAK,CACb7J,KAAK,CAAE+E,YAAY,CACnB2F,qBAAqB,CAAE3F,YAAY,CAACgB,QAAQ,CAAC,kBAAkB,CACjE,CAAC,CAAC,CACJ,CACF,CAAE,MAAO/F,KAAK,CAAE,KAAAkL,gBAAA,CAAAC,qBAAA,CACd,KAAM,CAAApG,YAAY,CAAG,EAAAmG,gBAAA,CAAAlL,KAAK,CAACsJ,QAAQ,UAAA4B,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgB1B,IAAI,UAAA2B,qBAAA,iBAApBA,qBAAA,CAAsB/F,OAAO,GAAI,oCAAoC,CAC1FxF,OAAO,CAACqL,IAAI,CAAC,CACXtD,QAAQ,CACR8B,OAAO,CAAE,KAAK,CACdG,SAAS,CAAE,KAAK,CAChBC,MAAM,CAAE,KAAK,CACb7J,KAAK,CAAE+E,YAAY,CACnB2F,qBAAqB,CAAE3F,YAAY,CAACgB,QAAQ,CAAC,kBAAkB,CACjE,CAAC,CAAC,CACJ,CAEA;AACA5E,iBAAiB,CAAC4F,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CAEnC;AACA;AACA,GAAI+D,CAAC,CAAG/B,SAAS,CAACzC,MAAM,CAAG,CAAC,CAAE,CAC5B,KAAM,IAAI,CAAApC,OAAO,CAACC,OAAO,EAAIiH,UAAU,CAACjH,OAAO,CAAE,IAAI,CAAC,CAAC,CAAE;AAC3D,CACF,CAEA;AACApD,eAAe,CAACnB,OAAO,CAAC,CAExB;AACA,KAAM,CAAAyL,mBAAmB,CAAGzL,OAAO,CAAC0L,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC9B,OAAO,EAAI8B,CAAC,CAAC3B,SAAS,EAAI2B,CAAC,CAAC1B,MAAM,CAAC,CACrF,GAAIwB,mBAAmB,CAAC/E,MAAM,CAAG,CAAC,CAAE,CAClC,KAAM,CAAAkF,uBAAuB,CAAGH,mBAAmB,CAAC,CAAC,CAAC,CACtD1L,iBAAiB,CAAC6L,uBAAuB,CAAC9L,cAAc,CAAC,CACzDG,UAAU,CAAC2L,uBAAuB,CAAChC,IAAI,CAAC,CAExC;AACA/J,oBAAoB,CAAC,CAAC,CAAC,CACzB,CAAC,IAAM,CACL;AACAE,iBAAiB,CAAC,IAAI,CAAC,CACvBE,UAAU,CAAC,IAAI,CAAC,CAChBJ,oBAAoB,CAAC,CAAC,CAAC,CACzB,CAEA;AACA;AACA,KAAM,CAAAgM,aAAa,CAAG,CAAC,CAAC,CACxB7L,OAAO,CAAC8G,OAAO,CAACM,MAAM,EAAI,CACxB,GAAIA,MAAM,CAACyC,OAAO,CAAE,CAClB,KAAM,CAAAa,aAAa,CAAGlL,gBAAgB,CAAC4H,MAAM,CAACW,QAAQ,CAAC,CACvD8D,aAAa,CAACzE,MAAM,CAACW,QAAQ,CAAC,CAAG,CAC/B2C,aAAa,CAAEA,aAAa,CAC5B5K,cAAc,CAAEsH,MAAM,CAAC6C,MAAM,CAAG7C,MAAM,CAACtH,cAAc,CAAG,IAAI,CAC5DE,OAAO,CAAEoH,MAAM,CAACwC,IAAI,CACpBK,MAAM,CAAE7C,MAAM,CAAC6C,MACjB,CAAC,CACD9F,OAAO,CAAC8B,GAAG,CAAC,yBAAyB,CAAEmB,MAAM,CAACW,QAAQ,CAAE,SAAS,CAAEX,MAAM,CAAC6C,MAAM,CAAE,mBAAmB,CAAE,CAAC,CAACS,aAAa,CAAC,CACzH,CACF,CAAC,CAAC,CACFjJ,sBAAsB,CAAC0F,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,GAAG0E,aAAc,CAAC,CAAC,CAAC,CAE/D;AACAtM,aAAa,CAAC,EAAE,CAAC,CACjBE,mBAAmB,CAAC,CAAC,CAAC,CAAC,CACvBE,mBAAmB,CAAC,CAAC,CAAC,CAAC,CACvBE,oBAAoB,CAAC,CAAC,CAAC,CAEvB;AACAY,cAAc,CAAC,eAAe,CAAC,CAC/BM,gBAAgB,CAAC,EAAE,CAAC,CACpBF,qBAAqB,CAAC,SAAS,CAAC,CAEhC,GAAIkC,YAAY,CAAC2E,OAAO,CAAE,CACxB3E,YAAY,CAAC2E,OAAO,CAACb,KAAK,CAAG,EAAE,CACjC,CAEF,CAAE,MAAOzG,KAAK,CAAE,CACdC,QAAQ,CAAC,2BAA2B,EAAID,KAAK,CAACoF,OAAO,EAAI,eAAe,CAAC,CAAC,CAC5E,CAAC,OAAS,CACRnE,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CACF,CAAC,CAED;AACA,KAAM,CAAAyK,WAAW,CAAGA,CAAA,GAAM,CACxBvM,aAAa,CAAC,EAAE,CAAC,CACjBE,mBAAmB,CAAC,CAAC,CAAC,CAAC,CACvBE,mBAAmB,CAAC,CAAC,CAAC,CAAC,CACvBE,oBAAoB,CAAC,CAAC,CAAC,CACvBE,iBAAiB,CAAC,IAAI,CAAC,CACvBE,UAAU,CAAC,IAAI,CAAC,CAChBI,QAAQ,CAAC,EAAE,CAAC,CACZc,eAAe,CAAC,EAAE,CAAC,CACnBI,iBAAiB,CAAC,CAAC,CAAC,CACpBQ,iBAAiB,CAAC,CAAC,CAAC,CACpBN,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAE1B;AACAhB,cAAc,CAAC,eAAe,CAAC,CAC/BM,gBAAgB,CAAC,EAAE,CAAC,CACpBF,qBAAqB,CAAC,SAAS,CAAC,CAEhC,GAAIkC,YAAY,CAAC2E,OAAO,CAAE,CACxB3E,YAAY,CAAC2E,OAAO,CAACb,KAAK,CAAG,EAAE,CACjC,CACF,CAAC,CAMD;AACA,KAAM,CAAAkF,oBAAoB,CAAIZ,SAAS,EAAK,CAC1ChJ,oBAAoB,CAACgJ,SAAS,CAAC,CAC/BlJ,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED;AACA,KAAM,CAAA+J,UAAU,CAAIpJ,GAAG,EAAK,CAC1B,GAAI,CAAAC,SAAS,CAAG,KAAK,CACrB,GAAIH,UAAU,CAACE,GAAG,GAAKA,GAAG,EAAIF,UAAU,CAACG,SAAS,GAAK,KAAK,CAAE,CAC5DA,SAAS,CAAG,MAAM,CACpB,CACAF,aAAa,CAAC,CAAEC,GAAG,CAAEC,SAAU,CAAC,CAAC,CACnC,CAAC,CAED;AACA,KAAM,CAAAoJ,cAAc,CAAIC,UAAU,EAAK,CACrC,GAAI,CAACxJ,UAAU,CAACE,GAAG,CAAE,MAAO,CAAAsJ,UAAU,CAEtC,MAAO,CAAC,GAAGA,UAAU,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CACpC,GAAI,CAAAC,MAAM,CAAGF,CAAC,CAAC1J,UAAU,CAACE,GAAG,CAAC,CAC9B,GAAI,CAAA2J,MAAM,CAAGF,CAAC,CAAC3J,UAAU,CAACE,GAAG,CAAC,CAE9B;AACA,GAAI,MAAO,CAAA0J,MAAM,GAAK,QAAQ,EAAI,MAAO,CAAAC,MAAM,GAAK,QAAQ,CAAE,CAC5D,MAAO,CAAA7J,UAAU,CAACG,SAAS,GAAK,KAAK,CAAGyJ,MAAM,CAAGC,MAAM,CAAGA,MAAM,CAAGD,MAAM,CAC3E,CAEA;AACA,GAAI,MAAO,CAAAA,MAAM,GAAK,QAAQ,EAAI,MAAO,CAAAC,MAAM,GAAK,QAAQ,CAAE,CAC5D,MAAO,CAAA7J,UAAU,CAACG,SAAS,GAAK,KAAK,CACjCyJ,MAAM,CAACE,aAAa,CAACD,MAAM,CAAC,CAC5BA,MAAM,CAACC,aAAa,CAACF,MAAM,CAAC,CAClC,CAEA;AACA,GAAIA,MAAM,EAAI,IAAI,EAAIC,MAAM,EAAI,IAAI,CAAE,MAAO,EAAC,CAC9C,GAAID,MAAM,EAAI,IAAI,CAAE,MAAO,CAAA5J,UAAU,CAACG,SAAS,GAAK,KAAK,CAAG,CAAC,CAAG,CAAC,CAAC,CAClE,GAAI0J,MAAM,EAAI,IAAI,CAAE,MAAO,CAAA7J,UAAU,CAACG,SAAS,GAAK,KAAK,CAAG,CAAC,CAAC,CAAG,CAAC,CAElE,MAAO,EAAC,CACV,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAA4J,WAAW,CAAGA,CAAA,GAAM,CACxB;AACA,KAAM,CAAAC,aAAa,CAAG,EAAE,CAExBxL,YAAY,CAAC4F,OAAO,CAACM,MAAM,EAAI,CAC7B,GAAIA,MAAM,CAACyC,OAAO,EAAIzC,MAAM,CAAC4C,SAAS,EAAI5C,MAAM,CAACiD,gBAAgB,CAAE,CACjE,KAAM,CAAEC,QAAQ,CAAEC,MAAM,CAAEC,KAAM,CAAC,CAAGpD,MAAM,CAACiD,gBAAgB,CAE3D;AACAC,QAAQ,CAACxD,OAAO,CAAC6F,OAAO,EAAI,CAC1BD,aAAa,CAACrB,IAAI,CAAC,CACjBtD,QAAQ,CAAEX,MAAM,CAACW,QAAQ,CACzB6E,IAAI,CAAE,SAAS,CACf1J,EAAE,CAAEyJ,OAAO,CAACE,UAAU,CACtBC,QAAQ,CAAEH,OAAO,CAACG,QAAQ,CAC1BC,QAAQ,CAAEJ,OAAO,CAACI,QAAQ,CAC1BC,MAAM,CAAEL,OAAO,CAACK,MAAM,CACtBC,YAAY,CAAEN,OAAO,CAACM,YAAY,CAClCC,UAAU,CAAE,EAAE,CACdC,UAAU,CAAE,EAAE,CACdC,SAAS,CAAE,EAAE,CACbC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,UAAU,CAAEZ,OAAO,CAACY,UACtB,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF;AACAhD,MAAM,CAACzD,OAAO,CAAC0G,KAAK,EAAI,CACtBd,aAAa,CAACrB,IAAI,CAAC,CACjBtD,QAAQ,CAAEX,MAAM,CAACW,QAAQ,CACzB6E,IAAI,CAAE,OAAO,CACb1J,EAAE,CAAEsK,KAAK,CAACC,QAAQ,CAClBX,QAAQ,CAAEU,KAAK,CAACV,QAAQ,CACxBC,QAAQ,CAAE,EAAE,CACZC,MAAM,CAAE,EAAE,CACVC,YAAY,CAAE,EAAE,CAChBC,UAAU,CAAEM,KAAK,CAACN,UAAU,CAC5BC,UAAU,CAAEK,KAAK,CAACL,UAAU,CAC5BC,SAAS,CAAE,EAAE,CACbC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,UAAU,CAAEC,KAAK,CAACD,UACpB,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF;AACA/C,KAAK,CAAC1D,OAAO,CAAC4G,IAAI,EAAI,CACpBhB,aAAa,CAACrB,IAAI,CAAC,CACjBtD,QAAQ,CAAEX,MAAM,CAACW,QAAQ,CACzB6E,IAAI,CAAE,MAAM,CACZ1J,EAAE,CAAEwK,IAAI,CAACC,OAAO,CAChBb,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,EAAE,CACZC,MAAM,CAAE,EAAE,CACVC,YAAY,CAAE,EAAE,CAChBC,UAAU,CAAE,EAAE,CACdC,UAAU,CAAE,EAAE,CACdC,SAAS,CAAEM,IAAI,CAACN,SAAS,CACzBC,SAAS,CAAEK,IAAI,CAACL,SAAS,CACzBC,QAAQ,CAAEI,IAAI,CAACJ,QAAQ,CACvBC,UAAU,CAAEG,IAAI,CAACH,UACnB,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEF,GAAIb,aAAa,CAAChG,MAAM,GAAK,CAAC,CAAE,CAC9BkH,KAAK,CAAC,iCAAiC,CAAC,CACxC,OACF,CAEA;AACA,KAAM,CAAAC,OAAO,CAAG,CACd,gBAAgB,CAChB,gBAAgB,CAChB,IAAI,CACJ,YAAY,CACZ,YAAY,CACZ,cAAc,CACd,cAAc,CACd,YAAY,CACZ,YAAY,CACZ,WAAW,CACX,WAAW,CACX,YAAY,CACZ,YAAY,CACb,CAED,KAAM,CAAAC,UAAU,CAAG,CACjBD,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC,CACjB,GAAGrB,aAAa,CAACsB,GAAG,CAACC,SAAS,EAAI,CAChCA,SAAS,CAAClG,QAAQ,CAClBkG,SAAS,CAACrB,IAAI,CACdqB,SAAS,CAAC/K,EAAE,EAAI,EAAE,CAClB+K,SAAS,CAACnB,QAAQ,EAAI,EAAE,CACxBmB,SAAS,CAAClB,QAAQ,EAAI,EAAE,CACxBkB,SAAS,CAACjB,MAAM,EAAI,EAAE,CACtBiB,SAAS,CAAChB,YAAY,EAAI,EAAE,CAC5BgB,SAAS,CAACf,UAAU,EAAI,EAAE,CAC1Be,SAAS,CAACd,UAAU,EAAI,EAAE,CAC1Bc,SAAS,CAACb,SAAS,EAAI,EAAE,CACzBa,SAAS,CAACZ,SAAS,EAAI,EAAE,CACzBY,SAAS,CAACX,QAAQ,EAAI,EAAE,CACxBW,SAAS,CAACV,UAAU,EAAI,EAAE,CAC3B,CAACQ,IAAI,CAAC,GAAG,CAAC,CAAC,CACb,CAACA,IAAI,CAAC,IAAI,CAAC,CAEZ;AACA,KAAM,CAAAG,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACL,UAAU,CAAC,CAAE,CAAElB,IAAI,CAAE,yBAA0B,CAAC,CAAC,CACxE,KAAM,CAAAwB,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxC,KAAM,CAAAC,GAAG,CAAGC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC,CACrCE,IAAI,CAACM,YAAY,CAAC,MAAM,CAAEH,GAAG,CAAC,CAC9BH,IAAI,CAACM,YAAY,CAAC,UAAU,CAAE,8BAA8B,GAAI,CAAA7G,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC6G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CACzGP,IAAI,CAACjL,KAAK,CAACyL,UAAU,CAAG,QAAQ,CAChCP,QAAQ,CAACQ,IAAI,CAACC,WAAW,CAACV,IAAI,CAAC,CAC/BA,IAAI,CAACW,KAAK,CAAC,CAAC,CACZV,QAAQ,CAACQ,IAAI,CAACG,WAAW,CAACZ,IAAI,CAAC,CACjC,CAAC,CAGH;AACE3Q,SAAS,CAAC,IAAM,CACd,GAAIyD,YAAY,CAACwF,MAAM,CAAG,CAAC,CAAE,CAC3B;AACAvF,eAAe,CAAC,EAAE,CAAC,CACnBpB,iBAAiB,CAAC,IAAI,CAAC,CACvBE,UAAU,CAAC,IAAI,CAAC,CAClB,CACF,CAAC,CAAE,CAACqC,yBAAyB,CAAC,CAAC,CAG/B;AACA7E,SAAS,CAAC,IAAM,CACd,GAAI6C,YAAY,EAAIM,kBAAkB,GAAK,SAAS,CAAE,CACpD;AACA6E,qBAAqB,CAAC,CAAC,CACzB,CACF,CAAC,CAAE,CAACnF,YAAY,CAAC,CAAC,CAElB;AACA7C,SAAS,CAAC,IAAM,CACd,GAAI,CAAAwR,iBAAiB,CAAG,IAAI,CAE5B,KAAM,CAAAC,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACF,GAAItL,SAAS,CAACC,WAAW,EAAID,SAAS,CAACC,WAAW,CAACC,KAAK,CAAE,CACxD,KAAM,CAAAC,UAAU,CAAG,KAAM,CAAAH,SAAS,CAACC,WAAW,CAACC,KAAK,CAAC,CAAEE,IAAI,CAAE,aAAc,CAAC,CAAC,CAE7EiL,iBAAiB,CAAGA,CAAA,GAAM,CACxBpO,qBAAqB,CAACkD,UAAU,CAACE,KAAK,CAAC,CACvC,GAAIF,UAAU,CAACE,KAAK,GAAK,SAAS,EAAI3D,YAAY,EAAIE,WAAW,GAAK,eAAe,CAAE,CACrFiF,qBAAqB,CAAC,CAAC,CACzB,CACF,CAAC,CAED1B,UAAU,CAACoL,gBAAgB,CAAC,QAAQ,CAAEF,iBAAiB,CAAC,CAC1D,CACF,CAAE,MAAO/K,GAAG,CAAE,CACZC,OAAO,CAACC,IAAI,CAAC,oCAAoC,CAAEF,GAAG,CAAC,CACzD,CACF,CAAC,CAEDgL,gBAAgB,CAAC,CAAC,CAElB,MAAO,IAAM,CACX,GAAID,iBAAiB,CAAE,CACrB,GAAI,CACF,KAAM,CAAAlL,UAAU,CAAGH,SAAS,CAACC,WAAW,CAACC,KAAK,CAAC,CAAEE,IAAI,CAAE,aAAc,CAAC,CAAC,CACvED,UAAU,CAACqL,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACC,mBAAmB,CAAC,QAAQ,CAAEL,iBAAiB,CAAC,CAAC,CAC1E,CAAE,MAAO/K,GAAG,CAAE,CACZC,OAAO,CAACC,IAAI,CAAC,qCAAqC,CAAEF,GAAG,CAAC,CAC1D,CACF,CACF,CAAC,CACH,CAAC,CAAE,CAAC5D,YAAY,CAAEE,WAAW,CAAC,CAAC,CAE/B;AACA/C,SAAS,CAAC,IAAM,CACd;AACA;AAAA,CACD,CAAE,CAACmC,iBAAiB,CAAEF,gBAAgB,CAAC,CAAC,CAIzC,mBACEZ,KAAA,CAACpB,SAAS,EAAC6R,SAAS,CAAC,eAAe,CAAAlM,QAAA,eAElCvE,KAAA,CAAChB,IAAI,EACH0R,SAAS,CAAEtQ,SAAU,CACrBuQ,QAAQ,CAAGC,CAAC,EAAKvQ,YAAY,CAACuQ,CAAC,CAAE,CACjCH,SAAS,CAAC,MAAM,CAAAlM,QAAA,eAEhBvE,KAAA,CAACf,GAAG,EAAC4R,QAAQ,CAAC,WAAW,CAACC,KAAK,CAAC,iBAAiB,CAAAvM,QAAA,eAC/CzE,IAAA,CAACjB,IAAI,EAAC4R,SAAS,CAAC,MAAM,CAAAlM,QAAA,cACpBvE,KAAA,CAACnB,IAAI,CAAC6F,IAAI,EAAC+L,SAAS,CAAC,MAAM,CAAAlM,QAAA,eACzBvE,KAAA,CAACjB,IAAI,CAACgS,KAAK,EAACN,SAAS,CAAC,MAAM,CAAAlM,QAAA,eAC1BzE,IAAA,CAACf,IAAI,CAACiS,KAAK,EAACP,SAAS,CAAC,MAAM,CAAAlM,QAAA,CAAC,gBAAc,CAAY,CAAC,cACxDvE,KAAA,CAACjB,IAAI,CAACkS,MAAM,EACVlJ,KAAK,CAAEzH,aAAc,CACrB4Q,QAAQ,CAAG3J,CAAC,EAAKhH,gBAAgB,CAACgH,CAAC,CAACI,MAAM,CAACI,KAAK,CAAE,CAAAxD,QAAA,eAElDzE,IAAA,WAAQiI,KAAK,CAAC,KAAK,CAAAxD,QAAA,CAAC,iCAA+B,CAAQ,CAAC,cAC5DzE,IAAA,WAAQiI,KAAK,CAAC,UAAU,CAAAxD,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC1CzE,IAAA,WAAQiI,KAAK,CAAC,QAAQ,CAAAxD,QAAA,CAAC,kBAAgB,CAAQ,CAAC,cAChDzE,IAAA,WAAQiI,KAAK,CAAC,OAAO,CAAAxD,QAAA,CAAC,OAAK,CAAQ,CAAC,EACzB,CAAC,EACJ,CAAC,cAGbvE,KAAA,QAAKyQ,SAAS,CAAC,qCAAqC,CAAAlM,QAAA,eAClDzE,IAAA,CAACV,cAAc,EACb+R,OAAO,CAAC,OAAO,CACfC,SAAS,CAAC,OAAO,CACjBC,OAAO,CAAElN,eAAgB,CACzBmN,SAAS,MAAA/M,QAAA,cAETzE,IAAA,QACE2Q,SAAS,CAAC,kBAAkB,CAC5BpM,KAAK,CAAE,CAAEkN,MAAM,CAAE,SAAS,CAAEC,OAAO,CAAE,cAAe,CAAE,CAAAjN,QAAA,cAEtDzE,IAAA,QACE2R,GAAG,CAAC,mBAAmB,CACvBC,GAAG,CAAC,yBAAyB,CAC7BrN,KAAK,CAAE,CAAEsN,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,MAAO,CAAE,CAC1C,CAAC,CACC,CAAC,CACQ,CAAC,cAGjB5R,KAAA,QAAKyQ,SAAS,CAAC,6BAA6B,CAAAlM,QAAA,eAC1CvE,KAAA,QAAKyQ,SAAS,CAAC,wDAAwD,CAAAlM,QAAA,eACrEzE,IAAA,SAAM2Q,SAAS,CAAC,MAAM,CAACpM,KAAK,CAAE,CAAEwN,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,KAAK,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAAxN,QAAA,CAAC,qBAE3F,CAAM,CAAC,cACLzE,IAAA,CAACV,cAAc,EACbgS,SAAS,CAAC,OAAO,CACjBY,KAAK,CAAE,CAAEC,IAAI,CAAE,GAAG,CAAEC,IAAI,CAAE,GAAI,CAAE,CAChCb,OAAO,cACLrR,KAAA,CAACX,OAAO,EAAC+E,EAAE,CAAC,mCAAmC,CAACC,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAAC,QAAA,eAC3EvE,KAAA,CAACX,OAAO,CAACmF,MAAM,EAACC,EAAE,CAAC,IAAI,CAAAF,QAAA,eACrBzE,IAAA,MAAG2Q,SAAS,CAAC,gCAAgC,CAAI,CAAC,8BAEpD,EAAgB,CAAC,cACjBzQ,KAAA,CAACX,OAAO,CAACqF,IAAI,EAAAH,QAAA,eACXvE,KAAA,QAAKyQ,SAAS,CAAC,MAAM,CAAAlM,QAAA,eACnBvE,KAAA,QAAKyQ,SAAS,CAAC,MAAM,CAAAlM,QAAA,eACnBzE,IAAA,MAAG2Q,SAAS,CAAC,oCAAoC,CAAI,CAAC,cACtD3Q,IAAA,WAAAyE,QAAA,CAAQ,eAAa,CAAQ,CAAC,EAC3B,CAAC,cACNvE,KAAA,QAAKqE,KAAK,CAAE,CAAEwN,QAAQ,CAAE,MAAM,CAAEE,KAAK,CAAE,SAAS,CAAEI,UAAU,CAAE,MAAO,CAAE,CAAA5N,QAAA,EAAC,kDAC3B,cAAAzE,IAAA,QAAI,CAAC,+CACT,cAAAA,IAAA,QAAI,CAAC,gDAE9C,EAAK,CAAC,EACH,CAAC,cAENE,KAAA,QAAKyQ,SAAS,CAAC,MAAM,CAAAlM,QAAA,eACnBvE,KAAA,QAAKyQ,SAAS,CAAC,MAAM,CAAAlM,QAAA,eACnBzE,IAAA,MAAG2Q,SAAS,CAAC,uCAAuC,CAAI,CAAC,cACzD3Q,IAAA,WAAAyE,QAAA,CAAQ,iBAAe,CAAQ,CAAC,EAC7B,CAAC,cACNvE,KAAA,QAAKqE,KAAK,CAAE,CAAEwN,QAAQ,CAAE,MAAM,CAAEE,KAAK,CAAE,SAAS,CAAEI,UAAU,CAAE,MAAO,CAAE,CAAA5N,QAAA,EAAC,sCACvC,cAAAzE,IAAA,QAAI,CAAC,mCACT,cAAAA,IAAA,QAAI,CAAC,qDAElC,EAAK,CAAC,EACH,CAAC,cAENE,KAAA,QAAKyQ,SAAS,CAAC,iCAAiC,CAACpM,KAAK,CAAE,CAAEwN,QAAQ,CAAE,MAAO,CAAE,CAAAtN,QAAA,eAC3EzE,IAAA,MAAG2Q,SAAS,CAAC,uBAAuB,CAAI,CAAC,cACzC3Q,IAAA,WAAAyE,QAAA,CAAQ,iBAAe,CAAQ,CAAC,yGAElC,EAAK,CAAC,EACM,CAAC,EACR,CACV,CAAAA,QAAA,cAEDzE,IAAA,SAAM2Q,SAAS,CAAC,mBAAmB,CAAAlM,QAAA,cACjCzE,IAAA,SAAM2Q,SAAS,CAAC,+BAA+B,CAC5CpM,KAAK,CAAE,CACLwN,QAAQ,CAAE,MAAM,CAChBN,MAAM,CAAE,MAAM,CACdQ,KAAK,CAAE,SAAS,CAChBP,OAAO,CAAE,aAAa,CACtBY,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBjM,QAAQ,CAAE,UAAU,CACpBkM,MAAM,CAAE,MAAM,CACdR,UAAU,CAAE,MACd,CAAE,CAAAvN,QAAA,CACJ,GAAC,CAAM,CAAC,CACL,CAAC,CACO,CAAC,EACd,CAAC,cACNvE,KAAA,QAAKyQ,SAAS,CAAC,2BAA2B,CAAAlM,QAAA,eACxCvE,KAAA,QACEyQ,SAAS,CAAC,oBAAoB,CAC9B8B,OAAO,CAAEA,CAAA,GAAM9O,4BAA4B,CAAC,CAACD,yBAAyB,CAAE,CACxEa,KAAK,CAAE,CACLsN,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdY,eAAe,CAAEhP,yBAAyB,CAAG,SAAS,CAAG,SAAS,CAClEiP,YAAY,CAAE,MAAM,CACpBrM,QAAQ,CAAE,UAAU,CACpBmL,MAAM,CAAE,SAAS,CACjBmB,UAAU,CAAE,4BAA4B,CACxCC,MAAM,CAAE,uBACV,CAAE,CAAApO,QAAA,eAEFzE,IAAA,QACE2Q,SAAS,CAAC,eAAe,CACzBpM,KAAK,CAAE,CACLsN,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdY,eAAe,CAAE,OAAO,CACxBC,YAAY,CAAE,KAAK,CACnBrM,QAAQ,CAAE,UAAU,CACpBwM,GAAG,CAAE,KAAK,CACVC,IAAI,CAAErP,yBAAyB,CAAG,MAAM,CAAG,KAAK,CAChDkP,UAAU,CAAE,gBAAgB,CAC5BI,SAAS,CAAE,2BACb,CAAE,CACH,CAAC,cACFhT,IAAA,SACEuE,KAAK,CAAE,CACL+B,QAAQ,CAAE,UAAU,CACpBwM,GAAG,CAAE,KAAK,CACVC,IAAI,CAAErP,yBAAyB,CAAG,KAAK,CAAG,MAAM,CAChDuP,SAAS,CAAE,kBAAkB,CAC7BlB,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KAAK,CACjBC,KAAK,CAAE,OAAO,CACdW,UAAU,CAAE,eAAe,CAC3BM,UAAU,CAAE,MACd,CAAE,CAAAzO,QAAA,CAEDf,yBAAyB,CAAG,IAAI,CAAG,KAAK,CACrC,CAAC,EACJ,CAAC,cACN1D,IAAA,UAAO2Q,SAAS,CAAC,YAAY,CAACpM,KAAK,CAAE,CAAEwN,QAAQ,CAAE,MAAO,CAAE,CAAAtN,QAAA,CACvDf,yBAAyB,CAAG,4BAA4B,CAAG,sBAAsB,CAC7E,CAAC,EACL,CAAC,EACH,CAAC,EAGH,CAAC,cAERxD,KAAA,QAAKyQ,SAAS,CAAC,MAAM,CAAAlM,QAAA,eACnBzE,IAAA,CAACf,IAAI,CAACiS,KAAK,EAAAzM,QAAA,CAAC,cAAY,CAAY,CAAC,cACrCvE,KAAA,QAAKyQ,SAAS,CAAC,mBAAmB,CAAAlM,QAAA,eAChCzE,IAAA,CAAChB,MAAM,EACLmU,OAAO,CAAEzR,YAAY,CAAG,SAAS,CAAG,iBAAkB,CACtD+Q,OAAO,CAAEhJ,YAAa,CACtB2J,QAAQ,CAAEhR,eAAgB,CAAAqC,QAAA,CAEzBrC,eAAe,cACdlC,KAAA,CAAAE,SAAA,EAAAqE,QAAA,eACEzE,IAAA,CAACX,OAAO,EAACsF,EAAE,CAAC,MAAM,CAAC0O,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC3I,IAAI,CAAC,QAAQ,CAAC,cAAY,MAAM,CAAE,CAAC,cACnF3K,IAAA,SAAM2Q,SAAS,CAAC,MAAM,CAAAlM,QAAA,CAAC,qBAAmB,CAAM,CAAC,EACjD,CAAC,CAEH/C,YAAY,CAAG,gBAAgB,CAAG,eACnC,CACK,CAAC,cACWxB,KAAA,QAAKyQ,SAAS,CAAC,sBAAsB,CAAAlM,QAAA,eACrDvE,KAAA,UAAOyQ,SAAS,CAAC,kBAAkB,CAAAlM,QAAA,EAAC,cAElC,cAAAzE,IAAA,UACEgO,IAAI,CAAC,MAAM,CACX2C,SAAS,CAAC,YAAY,CACtB4C,MAAM,CAAC,eAAe,CACtBnC,QAAQ,CAAE5J,gBAAiB,CAC3BgM,GAAG,CAAErP,YAAa,CAClBiP,QAAQ,CAAE1R,YAAa,CACvB+R,QAAQ,MACT,CAAC,EACG,CAAC,cACRvT,KAAA,UAAOyQ,SAAS,CAAC,yBAAyB,CAAAlM,QAAA,eACxCzE,IAAA,MAAG2Q,SAAS,CAAC,yBAAyB,CAAI,CAAC,4FAE7C,EAAO,CAAC,EACL,CAAC,EACL,CAAC,CAGLjP,YAAY,eACXxB,KAAA,QAAKyQ,SAAS,CAAC,sBAAsB,CAAAlM,QAAA,eACnCvE,KAAA,UAAOyQ,SAAS,CAAC,YAAY,CAAAlM,QAAA,eAC3BzE,IAAA,WAAAyE,QAAA,CAAQ,kBAAgB,CAAQ,CAAC,CAChCzC,kBAAkB,GAAK,SAAS,eAAIhC,IAAA,SAAM2Q,SAAS,CAAC,mBAAmB,CAAAlM,QAAA,CAAC,gBAAS,CAAM,CAAC,CACxFzC,kBAAkB,GAAK,QAAQ,eAAIhC,IAAA,SAAM2Q,SAAS,CAAC,kBAAkB,CAAAlM,QAAA,CAAC,eAAQ,CAAM,CAAC,CACrFzC,kBAAkB,GAAK,QAAQ,eAAIhC,IAAA,SAAM2Q,SAAS,CAAC,mBAAmB,CAAAlM,QAAA,CAAC,sBAAe,CAAM,CAAC,CAC7FzC,kBAAkB,GAAK,SAAS,eAAIhC,IAAA,SAAM2Q,SAAS,CAAC,qBAAqB,CAAAlM,QAAA,CAAC,WAAS,CAAM,CAAC,EACtF,CAAC,CACP,CAAC7C,WAAW,GAAK,eAAe,EAAI0H,MAAM,CAACC,IAAI,CAAC3I,gBAAgB,CAAC,CAACkH,MAAM,CAAG,CAAC,gBAC3E5H,KAAA,QAAKyQ,SAAS,CAAC,MAAM,CAAAlM,QAAA,eACnBvE,KAAA,UAAOyQ,SAAS,CAAC,YAAY,CAAAlM,QAAA,eAC3BzE,IAAA,WAAAyE,QAAA,CAAQ,mBAAiB,CAAQ,CAAC,IAAC,cAAAzE,IAAA,SAAM2Q,SAAS,CAAC,cAAc,CAAAlM,QAAA,CAAE7C,WAAW,CAAO,CAAC,EACjF,CAAC,CACP0H,MAAM,CAACC,IAAI,CAAC3I,gBAAgB,CAAC,CAACkH,MAAM,CAAG,CAAC,eACvC9H,IAAA,QAAK2Q,SAAS,CAAC,MAAM,CAAAlM,QAAA,cACnBvE,KAAA,UAAOyQ,SAAS,CAAC,YAAY,CAAAlM,QAAA,eAC3BzE,IAAA,WAAAyE,QAAA,CAAQ,0BAAwB,CAAQ,CAAC,IAAC,cAAAzE,IAAA,SAAM2Q,SAAS,CAAC,cAAc,CAAAlM,QAAA,CAAE4E,uBAAuB,CAAC,CAAC,CAAO,CAAC,EACtG,CAAC,CACL,CACN,EACE,CACN,CACAnH,aAAa,eACZhC,KAAA,CAACd,KAAK,EAAC+T,OAAO,CAAC,SAAS,CAACxC,SAAS,CAAC,WAAW,CAACpM,KAAK,CAAE,CAAEwN,QAAQ,CAAE,UAAW,CAAE,CAAAtN,QAAA,eAC7EzE,IAAA,CAACZ,KAAK,CAACsU,OAAO,EAAC/O,EAAE,CAAC,IAAI,CAAAF,QAAA,CAAC,uBAAqB,CAAe,CAAC,cAC5DzE,IAAA,QAAKuE,KAAK,CAAE,CAAEoP,UAAU,CAAE,UAAW,CAAE,CAAAlP,QAAA,CAAEvC,aAAa,CAAM,CAAC,cAC7DlC,IAAA,QAAK,CAAC,cACNA,IAAA,QAAK2Q,SAAS,CAAC,4BAA4B,CAAAlM,QAAA,cACzCzE,IAAA,CAAChB,MAAM,EAACmU,OAAO,CAAC,iBAAiB,CAACG,IAAI,CAAC,IAAI,CAACb,OAAO,CAAE5L,qBAAsB,CAAApC,QAAA,CAAC,uBAE5E,CAAQ,CAAC,CACN,CAAC,EACD,CACR,EACE,CACN,EACE,CAAC,CAEL/C,YAAY,eACXxB,KAAA,QAAKyQ,SAAS,CAAC,uBAAuB,CAAAlM,QAAA,eACpCzE,IAAA,CAACN,MAAM,EACLkU,KAAK,CAAE,KAAM,CACbJ,GAAG,CAAEtP,SAAU,CACf2P,gBAAgB,CAAC,YAAY,CAC7BlD,SAAS,CAAC,QAAQ,CAClBmD,gBAAgB,CAAE,CAChBjC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAE,GAAG,CACXiC,UAAU,CAAEjS,iBACd,CAAE,CACH,CAAC,CACDsC,QAAQ,eACPpE,IAAA,CAAChB,MAAM,EACLmU,OAAO,CAAC,mBAAmB,CAC3BV,OAAO,CAAE9I,uBAAwB,CACjCgH,SAAS,CAAC,WAAW,CACrB2C,IAAI,CAAC,IAAI,CAAA7O,QAAA,CACV,eAED,CAAQ,CACT,cACDzE,IAAA,CAAChB,MAAM,EACLmU,OAAO,CAAC,SAAS,CACjBV,OAAO,CAAE7J,aAAc,CACvB+H,SAAS,CAAC,MAAM,CAAAlM,QAAA,CACjB,eAED,CAAQ,CAAC,EACN,CACN,CAEA6E,MAAM,CAACC,IAAI,CAAC3I,gBAAgB,CAAC,CAACkH,MAAM,CAAG,CAAC,eACvC5H,KAAA,QAAKyQ,SAAS,CAAC,8BAA8B,CAAAlM,QAAA,eAC3CzE,IAAA,OAAAyE,QAAA,CAAI,UAAQ,CAAI,CAAC,cACjBzE,IAAA,QAAK2Q,SAAS,CAAC,eAAe,CAAAlM,QAAA,CAC3B6E,MAAM,CAAC0K,OAAO,CAACpT,gBAAgB,CAAC,CAACwO,GAAG,CAAC,CAAA6E,IAAA,CAAkBC,KAAK,OAAtB,CAAC9O,IAAI,CAAE+O,OAAO,CAAC,CAAAF,IAAA,oBACpD/T,KAAA,QAEEyQ,SAAS,CAAE,mBAAmBuD,KAAK,GAAKlT,iBAAiB,CAAG,UAAU,CAAG,EAAE,EAAG,CAC9EyR,OAAO,CAAEA,CAAA,GAAMxR,oBAAoB,CAACiT,KAAK,CAAE,CAAAzP,QAAA,eAE3CzE,IAAA,QACE2R,GAAG,CAAEwC,OAAQ,CACbvC,GAAG,CAAE,WAAWsC,KAAK,CAAG,CAAC,EAAG,CAC5BvD,SAAS,CAAC,eAAe,CAC1B,CAAC,CAEDvL,IAAI,CAACsD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,eACnCzI,KAAA,QAAKyQ,SAAS,CAAC,gBAAgB,CAACK,KAAK,CAAC,wDAAwD,CAAAvM,QAAA,eAC5FzE,IAAA,MAAG2Q,SAAS,CAAC,2BAA2B,CAAI,CAAC,cAC7C3Q,IAAA,UAAO2Q,SAAS,CAAC,MAAM,CAAAlM,QAAA,CAAC,MAAI,CAAO,CAAC,EACjC,CACN,cACDzE,IAAA,WACE2Q,SAAS,CAAC,oCAAoC,CAC9C8B,OAAO,CAAGhL,CAAC,EAAK,CACdA,CAAC,CAAC2M,eAAe,CAAC,CAAC,CACnB,KAAM,CAAAC,QAAQ,CAAG3T,UAAU,CAACoM,MAAM,CAAC,CAACwH,CAAC,CAAEhI,CAAC,GAAKA,CAAC,GAAK4H,KAAK,CAAC,CACzD,KAAM,CAAAK,cAAc,CAAG,CAAC,GAAG3T,gBAAgB,CAAC,CAC5C,KAAM,CAAA4T,cAAc,CAAG,CAAC,GAAG1T,gBAAgB,CAAC,CAC5C,MAAO,CAAAyT,cAAc,CAACnP,IAAI,CAAC,CAC3B,MAAO,CAAAoP,cAAc,CAACpP,IAAI,CAAC,CAC3BzE,aAAa,CAAC0T,QAAQ,CAAC,CACvBxT,mBAAmB,CAAC0T,cAAc,CAAC,CACnCxT,mBAAmB,CAACyT,cAAc,CAAC,CACnC,GAAIxT,iBAAiB,EAAIqT,QAAQ,CAACvM,MAAM,CAAE,CACxC7G,oBAAoB,CAACwT,IAAI,CAACC,GAAG,CAAC,CAAC,CAAEL,QAAQ,CAACvM,MAAM,CAAG,CAAC,CAAC,CAAC,CACxD,CACF,CAAE,CAAArD,QAAA,CACH,MAED,CAAQ,CAAC,GAlCJW,IAmCF,CAAC,EACP,CAAC,CACC,CAAC,cACNpF,IAAA,QAAK2Q,SAAS,CAAC,uBAAuB,CAAAlM,QAAA,CACnC6E,MAAM,CAACe,MAAM,CAACzJ,gBAAgB,CAAC,CAACI,iBAAiB,CAAC,eACjDhB,IAAA,QACE2R,GAAG,CAAErI,MAAM,CAACe,MAAM,CAACzJ,gBAAgB,CAAC,CAACI,iBAAiB,CAAE,CACxD4Q,GAAG,CAAC,iBAAiB,CACrBjB,SAAS,CAAC,yBAAyB,CACpC,CACF,CACE,CAAC,EACH,CACN,CAEAnP,KAAK,eAAIxB,IAAA,CAACZ,KAAK,EAAC+T,OAAO,CAAC,QAAQ,CAAA1O,QAAA,CAAEjD,KAAK,CAAQ,CAAC,cAEjDtB,KAAA,QAAKyQ,SAAS,CAAC,mBAAmB,CAAAlM,QAAA,eAChCzE,IAAA,CAAChB,MAAM,EACLmU,OAAO,CAAC,SAAS,CACjBV,OAAO,CAAE5I,aAAc,CACvBuJ,QAAQ,CAAE9J,MAAM,CAACC,IAAI,CAAC3I,gBAAgB,CAAC,CAACkH,MAAM,GAAK,CAAC,EAAIxG,OAAO,EAAIkB,eAAgB,CAAAiC,QAAA,CAElFnD,OAAO,cACNpB,KAAA,CAAAE,SAAA,EAAAqE,QAAA,eACEzE,IAAA,CAACX,OAAO,EAACsF,EAAE,CAAC,MAAM,CAAC0O,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC3I,IAAI,CAAC,QAAQ,CAAC,cAAY,MAAM,CAAE,CAAC,cACnF3K,IAAA,SAAM2Q,SAAS,CAAC,MAAM,CAAAlM,QAAA,CAAC,cAAY,CAAM,CAAC,EAC1C,CAAC,CAEH,sBACD,CACK,CAAC,cAETzE,IAAA,CAAChB,MAAM,EACLmU,OAAO,CAAC,SAAS,CACjBV,OAAO,CAAEpG,gBAAiB,CAC1B+G,QAAQ,CAAE9J,MAAM,CAACC,IAAI,CAAC3I,gBAAgB,CAAC,CAACkH,MAAM,GAAK,CAAC,EAAIxG,OAAO,EAAIkB,eAAgB,CAAAiC,QAAA,CAElFjC,eAAe,cACdtC,KAAA,CAAAE,SAAA,EAAAqE,QAAA,eACEzE,IAAA,CAACX,OAAO,EAACsF,EAAE,CAAC,MAAM,CAAC0O,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC3I,IAAI,CAAC,QAAQ,CAAC,cAAY,MAAM,CAAE,CAAC,cACnFzK,KAAA,SAAMyQ,SAAS,CAAC,MAAM,CAAAlM,QAAA,EAAC,aAAW,CAAC/B,cAAc,CAAC,GAAC,CAACQ,cAAc,EAAO,CAAC,EAC1E,CAAC,CAEH,oBACD,CACK,CAAC,cAETlD,IAAA,CAAChB,MAAM,EACLmU,OAAO,CAAC,WAAW,CACnBV,OAAO,CAAEvF,WAAY,CACrBkG,QAAQ,CAAE9R,OAAO,EAAIkB,eAAgB,CAAAiC,QAAA,CACtC,OAED,CAAQ,CAAC,EACN,CAAC,EACG,CAAC,CACR,CAAC,CAINnC,YAAY,CAACqS,IAAI,CAACnM,MAAM,OAAAoM,qBAAA,OAAI,CAAApM,MAAM,CAACyC,OAAO,EAAIzC,MAAM,CAAC4C,SAAS,EAAI,EAAAwJ,qBAAA,CAAApM,MAAM,CAACwD,eAAe,UAAA4I,qBAAA,iBAAtBA,qBAAA,CAAwB3I,KAAK,EAAG,CAAC,GAAC,eACnG/L,KAAA,CAACnB,IAAI,EAAC4R,SAAS,CAAC,MAAM,CAAAlM,QAAA,eACpBzE,IAAA,CAACjB,IAAI,CAAC2F,MAAM,EAAAD,QAAA,cACVvE,KAAA,QAAKyQ,SAAS,CAAC,mDAAmD,CAAAlM,QAAA,eAChEvE,KAAA,OAAIyQ,SAAS,CAAC,MAAM,CAAAlM,QAAA,eAClBzE,IAAA,MAAG2Q,SAAS,CAAC,mBAAmB,CAAI,CAAC,6BAEvC,EAAI,CAAC,cACL3Q,IAAA,QAAK2Q,SAAS,CAAC,cAAc,CAAAlM,QAAA,cAC3BvE,KAAA,CAAClB,MAAM,EACLmU,OAAO,CAAC,SAAS,CACjBG,IAAI,CAAC,IAAI,CACTb,OAAO,CAAE5E,WAAY,CACrBmD,KAAK,CAAC,uBAAuB,CAAAvM,QAAA,eAE7BzE,IAAA,MAAG2Q,SAAS,CAAC,sBAAsB,CAAI,CAAC,aAE1C,EAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACK,CAAC,cACdzQ,KAAA,CAACnB,IAAI,CAAC6F,IAAI,EAAAH,QAAA,eAENzE,IAAA,QAAK2Q,SAAS,CAAC,MAAM,CAAAlM,QAAA,cACnBvE,KAAA,QAAKyQ,SAAS,CAAC,wBAAwB,CAAAlM,QAAA,eACrCzE,IAAA,CAAChB,MAAM,EACLmU,OAAO,CAAEvP,oBAAoB,GAAK,KAAK,CAAG,SAAS,CAAG,iBAAkB,CACxE0P,IAAI,CAAC,IAAI,CACTb,OAAO,CAAEA,CAAA,GAAM5O,uBAAuB,CAAC,KAAK,CAAE,CAAAY,QAAA,CAC/C,gBAED,CAAQ,CAAC,cACTzE,IAAA,CAAChB,MAAM,EACLmU,OAAO,CAAEvP,oBAAoB,GAAK,UAAU,CAAG,QAAQ,CAAG,gBAAiB,CAC3E0P,IAAI,CAAC,IAAI,CACTb,OAAO,CAAEA,CAAA,GAAM5O,uBAAuB,CAAC,UAAU,CAAE,CAAAY,QAAA,CACpD,eAED,CAAQ,CAAC,cACTzE,IAAA,CAAChB,MAAM,EACLmU,OAAO,CAAEvP,oBAAoB,GAAK,QAAQ,CAAG,SAAS,CAAG,iBAAkB,CAC3E0P,IAAI,CAAC,IAAI,CACTb,OAAO,CAAEA,CAAA,GAAM5O,uBAAuB,CAAC,QAAQ,CAAE,CAAAY,QAAA,CAClD,aAED,CAAQ,CAAC,cACTzE,IAAA,CAAChB,MAAM,EACLmU,OAAO,CAAEvP,oBAAoB,GAAK,OAAO,CAAG,MAAM,CAAG,cAAe,CACpE0P,IAAI,CAAC,IAAI,CACTb,OAAO,CAAEA,CAAA,GAAM5O,uBAAuB,CAAC,OAAO,CAAE,CAAAY,QAAA,CACjD,YAED,CAAQ,CAAC,EACN,CAAC,CACH,CAAC,cAGNzE,IAAA,QAAK2Q,SAAS,CAAC,8BAA8B,CAAAlM,QAAA,cAC3CvE,KAAA,QAAKyQ,SAAS,CAAC,KAAK,CAAAlM,QAAA,eAClBzE,IAAA,QAAK2Q,SAAS,CAAC,UAAU,CAAAlM,QAAA,cACvBzE,IAAA,QAAK2Q,SAAS,CAAC,2BAA2B,CAAAlM,QAAA,cACxCvE,KAAA,QAAKyQ,SAAS,CAAC,4BAA4B,CAAAlM,QAAA,eACzCzE,IAAA,OAAI2Q,SAAS,CAAC,MAAM,CAAAlM,QAAA,CAAC,gBAAc,CAAI,CAAC,cACxCzE,IAAA,OAAI2Q,SAAS,CAAC,MAAM,CAAAlM,QAAA,CACjBnC,YAAY,CAACuS,MAAM,CAAC,CAACC,GAAG,CAAEtM,MAAM,QAAAuM,sBAAA,OAAK,CAAAD,GAAG,EAAI,EAAAC,sBAAA,CAAAvM,MAAM,CAACwD,eAAe,UAAA+I,sBAAA,iBAAtBA,sBAAA,CAAwBrJ,QAAQ,GAAI,CAAC,CAAC,GAAE,CAAC,CAAC,CACrF,CAAC,EACF,CAAC,CACH,CAAC,CACH,CAAC,cACN1L,IAAA,QAAK2Q,SAAS,CAAC,UAAU,CAAAlM,QAAA,cACvBzE,IAAA,QAAK2Q,SAAS,CAAC,4BAA4B,CAAAlM,QAAA,cACzCvE,KAAA,QAAKyQ,SAAS,CAAC,4BAA4B,CAAAlM,QAAA,eACzCzE,IAAA,OAAI2Q,SAAS,CAAC,MAAM,CAAAlM,QAAA,CAAC,cAAY,CAAI,CAAC,cACtCzE,IAAA,OAAI2Q,SAAS,CAAC,MAAM,CAAAlM,QAAA,CACjBnC,YAAY,CAACuS,MAAM,CAAC,CAACC,GAAG,CAAEtM,MAAM,QAAAwM,sBAAA,OAAK,CAAAF,GAAG,EAAI,EAAAE,sBAAA,CAAAxM,MAAM,CAACwD,eAAe,UAAAgJ,sBAAA,iBAAtBA,sBAAA,CAAwBrJ,MAAM,GAAI,CAAC,CAAC,GAAE,CAAC,CAAC,CACnF,CAAC,EACF,CAAC,CACH,CAAC,CACH,CAAC,cACN3L,IAAA,QAAK2Q,SAAS,CAAC,UAAU,CAAAlM,QAAA,cACvBzE,IAAA,QAAK2Q,SAAS,CAAC,yBAAyB,CAAAlM,QAAA,cACtCvE,KAAA,QAAKyQ,SAAS,CAAC,4BAA4B,CAAAlM,QAAA,eACzCzE,IAAA,OAAI2Q,SAAS,CAAC,MAAM,CAAAlM,QAAA,CAAC,aAAW,CAAI,CAAC,cACrCzE,IAAA,OAAI2Q,SAAS,CAAC,MAAM,CAAAlM,QAAA,CACjBnC,YAAY,CAACuS,MAAM,CAAC,CAACC,GAAG,CAAEtM,MAAM,QAAAyM,sBAAA,OAAK,CAAAH,GAAG,EAAI,EAAAG,sBAAA,CAAAzM,MAAM,CAACwD,eAAe,UAAAiJ,sBAAA,iBAAtBA,sBAAA,CAAwBrJ,KAAK,GAAI,CAAC,CAAC,GAAE,CAAC,CAAC,CAClF,CAAC,EACF,CAAC,CACH,CAAC,CACH,CAAC,cACN5L,IAAA,QAAK2Q,SAAS,CAAC,UAAU,CAAAlM,QAAA,cACvBzE,IAAA,QAAK2Q,SAAS,CAAC,4BAA4B,CAAAlM,QAAA,cACzCvE,KAAA,QAAKyQ,SAAS,CAAC,4BAA4B,CAAAlM,QAAA,eACzCzE,IAAA,OAAI2Q,SAAS,CAAC,MAAM,CAAAlM,QAAA,CAAC,kBAAgB,CAAI,CAAC,cAC1CzE,IAAA,OAAI2Q,SAAS,CAAC,MAAM,CAAAlM,QAAA,CACjBnC,YAAY,CAACuS,MAAM,CAAC,CAACC,GAAG,CAAEtM,MAAM,QAAA0M,sBAAA,OAAK,CAAAJ,GAAG,EAAI,EAAAI,sBAAA,CAAA1M,MAAM,CAACwD,eAAe,UAAAkJ,sBAAA,iBAAtBA,sBAAA,CAAwBjJ,KAAK,GAAI,CAAC,CAAC,GAAE,CAAC,CAAC,CAClF,CAAC,EACF,CAAC,CACH,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,cAGNjM,IAAA,QAAK2Q,SAAS,CAAC,4CAA4C,CAAAlM,QAAA,CACxD,CAAC,IAAM,CACN;AACA,KAAM,CAAAqJ,aAAa,CAAG,EAAE,CAExBxL,YAAY,CAAC4F,OAAO,CAACM,MAAM,EAAI,CAC7B,GAAIA,MAAM,CAACyC,OAAO,EAAIzC,MAAM,CAAC4C,SAAS,EAAI5C,MAAM,CAACiD,gBAAgB,CAAE,CACjE,KAAM,CAAEC,QAAQ,CAAEC,MAAM,CAAEC,KAAM,CAAC,CAAGpD,MAAM,CAACiD,gBAAgB,CAE3D;AACA,GAAI7H,oBAAoB,GAAK,KAAK,EAAIA,oBAAoB,GAAK,UAAU,CAAE,CACzE8H,QAAQ,CAACxD,OAAO,CAAC6F,OAAO,EAAI,CAC1BD,aAAa,CAACrB,IAAI,CAAC,CACjB,GAAGsB,OAAO,CACVC,IAAI,CAAE,SAAS,CACf7E,QAAQ,CAAEX,MAAM,CAACW,QAAQ,CACzB3I,aAAa,CAAE,UACjB,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAEA;AACA,GAAIoD,oBAAoB,GAAK,KAAK,EAAIA,oBAAoB,GAAK,QAAQ,CAAE,CACvE+H,MAAM,CAACzD,OAAO,CAAC0G,KAAK,EAAI,CACtBd,aAAa,CAACrB,IAAI,CAAC,CACjB,GAAGmC,KAAK,CACRZ,IAAI,CAAE,OAAO,CACb7E,QAAQ,CAAEX,MAAM,CAACW,QAAQ,CACzB3I,aAAa,CAAE,QACjB,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAEA;AACA,GAAIoD,oBAAoB,GAAK,KAAK,EAAIA,oBAAoB,GAAK,OAAO,CAAE,CACtEgI,KAAK,CAAC1D,OAAO,CAAC4G,IAAI,EAAI,CACpBhB,aAAa,CAACrB,IAAI,CAAC,CACjB,GAAGqC,IAAI,CACPd,IAAI,CAAE,MAAM,CACZ7E,QAAQ,CAAEX,MAAM,CAACW,QAAQ,CACzB3I,aAAa,CAAE,OACjB,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CACF,CACF,CAAC,CAAC,CAEF,GAAIsN,aAAa,CAAChG,MAAM,GAAK,CAAC,CAAE,CAC9B,mBACE5H,KAAA,QAAKyQ,SAAS,CAAC,kBAAkB,CAAAlM,QAAA,eAC/BzE,IAAA,QAAK2Q,SAAS,CAAC,MAAM,CAAAlM,QAAA,cACnBzE,IAAA,MAAG2Q,SAAS,CAAC,gCAAgC,CAAI,CAAC,CAC/C,CAAC,cACN3Q,IAAA,OAAI2Q,SAAS,CAAC,YAAY,CAAAlM,QAAA,CAAC,qBAAmB,CAAI,CAAC,cACnDzE,IAAA,MAAG2Q,SAAS,CAAC,iBAAiB,CAAAlM,QAAA,CAC3Bb,oBAAoB,GAAK,KAAK,CAC3B,mDAAmD,CACnD,MAAMA,oBAAoB,yCAAyC,CACtE,CAAC,EACD,CAAC,CAEV,CAEA,mBACE1D,KAAA,UAAOyQ,SAAS,CAAC,oCAAoC,CAAAlM,QAAA,eACnDzE,IAAA,UAAAyE,QAAA,cACEvE,KAAA,OAAAuE,QAAA,eACEzE,IAAA,OAAAyE,QAAA,CAAI,gBAAc,CAAI,CAAC,cACvBzE,IAAA,OAAAyE,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxBvE,KAAA,OACEqE,KAAK,CAAE,CAAEkN,MAAM,CAAE,SAAU,CAAE,CAC7BgB,OAAO,CAAEA,CAAA,GAAMrF,UAAU,CAAC,eAAe,CAAE,CAAA3I,QAAA,EAC5C,OACM,CAACX,UAAU,CAACE,GAAG,GAAK,eAAe,eACtChE,IAAA,MAAG2Q,SAAS,CAAE,eAAe7M,UAAU,CAACG,SAAS,GAAK,KAAK,CAAG,IAAI,CAAG,MAAM,OAAQ,CAAI,CACxF,EACC,CAAC,cACLjE,IAAA,OAAAyE,QAAA,CAAI,IAAE,CAAI,CAAC,CACV,CAACb,oBAAoB,GAAK,KAAK,EAAIA,oBAAoB,GAAK,UAAU,gBACrE1D,KAAA,CAAAE,SAAA,EAAAqE,QAAA,eACEvE,KAAA,OACEqE,KAAK,CAAE,CAAEkN,MAAM,CAAE,SAAU,CAAE,CAC7BgB,OAAO,CAAEA,CAAA,GAAMrF,UAAU,CAAC,UAAU,CAAE,CAAA3I,QAAA,EACvC,gBACY,CAACX,UAAU,CAACE,GAAG,GAAK,UAAU,eACvChE,IAAA,MAAG2Q,SAAS,CAAE,eAAe7M,UAAU,CAACG,SAAS,GAAK,KAAK,CAAG,IAAI,CAAG,MAAM,OAAQ,CAAI,CACxF,EACC,CAAC,cACL/D,KAAA,OACEqE,KAAK,CAAE,CAAEkN,MAAM,CAAE,SAAU,CAAE,CAC7BgB,OAAO,CAAEA,CAAA,GAAMrF,UAAU,CAAC,UAAU,CAAE,CAAA3I,QAAA,EACvC,aACY,CAACX,UAAU,CAACE,GAAG,GAAK,UAAU,eACvChE,IAAA,MAAG2Q,SAAS,CAAE,eAAe7M,UAAU,CAACG,SAAS,GAAK,KAAK,CAAG,IAAI,CAAG,MAAM,OAAQ,CAAI,CACxF,EACC,CAAC,cACL/D,KAAA,OACEqE,KAAK,CAAE,CAAEkN,MAAM,CAAE,SAAU,CAAE,CAC7BgB,OAAO,CAAEA,CAAA,GAAMrF,UAAU,CAAC,QAAQ,CAAE,CAAA3I,QAAA,EACrC,kBACc,CAACX,UAAU,CAACE,GAAG,GAAK,QAAQ,eACvChE,IAAA,MAAG2Q,SAAS,CAAE,eAAe7M,UAAU,CAACG,SAAS,GAAK,KAAK,CAAG,IAAI,CAAG,MAAM,OAAQ,CAAI,CACxF,EACC,CAAC,cACLjE,IAAA,OAAAyE,QAAA,CAAI,cAAY,CAAI,CAAC,EACrB,CACH,CACA,CAACb,oBAAoB,GAAK,KAAK,EAAIA,oBAAoB,GAAK,QAAQ,gBACnE1D,KAAA,CAAAE,SAAA,EAAAqE,QAAA,eACEvE,KAAA,OACEqE,KAAK,CAAE,CAAEkN,MAAM,CAAE,SAAU,CAAE,CAC7BgB,OAAO,CAAEA,CAAA,GAAMrF,UAAU,CAAC,YAAY,CAAE,CAAA3I,QAAA,EACzC,aACY,CAACX,UAAU,CAACE,GAAG,GAAK,YAAY,eACzChE,IAAA,MAAG2Q,SAAS,CAAE,eAAe7M,UAAU,CAACG,SAAS,GAAK,KAAK,CAAG,IAAI,CAAG,MAAM,OAAQ,CAAI,CACxF,EACC,CAAC,cACL/D,KAAA,OACEqE,KAAK,CAAE,CAAEkN,MAAM,CAAE,SAAU,CAAE,CAC7BgB,OAAO,CAAEA,CAAA,GAAMrF,UAAU,CAAC,UAAU,CAAE,CAAA3I,QAAA,EACvC,gBACY,CAACX,UAAU,CAACE,GAAG,GAAK,UAAU,eACvChE,IAAA,MAAG2Q,SAAS,CAAE,eAAe7M,UAAU,CAACG,SAAS,GAAK,KAAK,CAAG,IAAI,CAAG,MAAM,OAAQ,CAAI,CACxF,EACC,CAAC,cACLjE,IAAA,OAAAyE,QAAA,CAAI,YAAU,CAAI,CAAC,EACnB,CACH,CACA,CAACb,oBAAoB,GAAK,KAAK,EAAIA,oBAAoB,GAAK,OAAO,gBAClE1D,KAAA,CAAAE,SAAA,EAAAqE,QAAA,eACEvE,KAAA,OACEqE,KAAK,CAAE,CAAEkN,MAAM,CAAE,SAAU,CAAE,CAC7BgB,OAAO,CAAEA,CAAA,GAAMrF,UAAU,CAAC,WAAW,CAAE,CAAA3I,QAAA,EACxC,YACW,CAACX,UAAU,CAACE,GAAG,GAAK,WAAW,eACvChE,IAAA,MAAG2Q,SAAS,CAAE,eAAe7M,UAAU,CAACG,SAAS,GAAK,KAAK,CAAG,IAAI,CAAG,MAAM,OAAQ,CAAI,CACxF,EACC,CAAC,cACL/D,KAAA,OACEqE,KAAK,CAAE,CAAEkN,MAAM,CAAE,SAAU,CAAE,CAC7BgB,OAAO,CAAEA,CAAA,GAAMrF,UAAU,CAAC,WAAW,CAAE,CAAA3I,QAAA,EACxC,YACW,CAACX,UAAU,CAACE,GAAG,GAAK,WAAW,eACvChE,IAAA,MAAG2Q,SAAS,CAAE,eAAe7M,UAAU,CAACG,SAAS,GAAK,KAAK,CAAG,IAAI,CAAG,MAAM,OAAQ,CAAI,CACxF,EACC,CAAC,cACL/D,KAAA,OACEqE,KAAK,CAAE,CAAEkN,MAAM,CAAE,SAAU,CAAE,CAC7BgB,OAAO,CAAEA,CAAA,GAAMrF,UAAU,CAAC,UAAU,CAAE,CAAA3I,QAAA,EACvC,aACY,CAACX,UAAU,CAACE,GAAG,GAAK,UAAU,eACvChE,IAAA,MAAG2Q,SAAS,CAAE,eAAe7M,UAAU,CAACG,SAAS,GAAK,KAAK,CAAG,IAAI,CAAG,MAAM,OAAQ,CAAI,CACxF,EACC,CAAC,EACL,CACH,cACD/D,KAAA,OACEqE,KAAK,CAAE,CAAEkN,MAAM,CAAE,SAAU,CAAE,CAC7BgB,OAAO,CAAEA,CAAA,GAAMrF,UAAU,CAAC,YAAY,CAAE,CAAA3I,QAAA,EACzC,aACY,CAACX,UAAU,CAACE,GAAG,GAAK,YAAY,eACzChE,IAAA,MAAG2Q,SAAS,CAAE,eAAe7M,UAAU,CAACG,SAAS,GAAK,KAAK,CAAG,IAAI,CAAG,MAAM,OAAQ,CAAI,CACxF,EACC,CAAC,EACH,CAAC,CACA,CAAC,cACRjE,IAAA,UAAAyE,QAAA,CACG4I,cAAc,CAACS,aAAa,CAAC,CAACsB,GAAG,CAAC,CAACC,SAAS,CAAE6E,KAAK,gBAClDhU,KAAA,OAAAuE,QAAA,eAEEzE,IAAA,OAAIuE,KAAK,CAAE,CAAEsN,KAAK,CAAE,OAAO,CAAEsD,SAAS,CAAE,QAAS,CAAE,CAAA1Q,QAAA,CAChD,CAAC,IAAM,CACN,KAAM,CAAA+D,MAAM,CAAGlG,YAAY,CAAC8S,IAAI,CAACrI,CAAC,EAAIA,CAAC,CAAC5D,QAAQ,GAAKkG,SAAS,CAAClG,QAAQ,CAAC,CACxE,KAAM,CAAA2C,aAAa,CAAGtD,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEsD,aAAa,CAC3C,MAAO,CAAAA,aAAa,cAClB5L,KAAA,QACEyQ,SAAS,CAAC,2BAA2B,CACrC8B,OAAO,CAAEA,CAAA,GAAMtF,oBAAoB,CAAC,CAClCrB,aAAa,CAAEA,aAAa,CAC5B5K,cAAc,CAAEsH,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEtH,cAAc,CACtCmK,MAAM,CAAE7C,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAE6C,MAAM,CACtBlC,QAAQ,CAAEkG,SAAS,CAAClG,QACtB,CAAC,CAAE,CACH6H,KAAK,CAAC,+BAAqB,CAAAvM,QAAA,eAE3BzE,IAAA,QACE2R,GAAG,CAAE7F,aAAc,CACnB8F,GAAG,CAAC,UAAU,CACdjB,SAAS,CAAC,iBAAiB,CAC5B,CAAC,cACF3Q,IAAA,QAAK2Q,SAAS,CAAC,mBAAmB,CAAAlM,QAAA,CAAC,cAEnC,CAAK,CAAC,EACH,CAAC,cAENzE,IAAA,UAAO2Q,SAAS,CAAC,YAAY,CAAAlM,QAAA,CAAC,UAAQ,CAAO,CAC9C,CACH,CAAC,EAAE,CAAC,CACF,CAAC,cAGLzE,IAAA,OAAIuE,KAAK,CAAE,CAAEsN,KAAK,CAAE,OAAO,CAAEsD,SAAS,CAAE,QAAS,CAAE,CAAA1Q,QAAA,CAChD,CAAC,IAAM,CACN,KAAM,CAAA+D,MAAM,CAAGlG,YAAY,CAAC8S,IAAI,CAACrI,CAAC,EAAIA,CAAC,CAAC5D,QAAQ,GAAKkG,SAAS,CAAClG,QAAQ,CAAC,CACxE,KAAM,CAAAjI,cAAc,CAAGsH,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEtH,cAAc,CAC7C,KAAM,CAAA4K,aAAa,CAAGtD,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEsD,aAAa,CAE3C,MAAO,CAAA5K,cAAc,EAAIsH,MAAM,SAANA,MAAM,WAANA,MAAM,CAAE6C,MAAM,cACrCnL,KAAA,QACEyQ,SAAS,CAAC,2BAA2B,CACrC8B,OAAO,CAAEA,CAAA,GAAMtF,oBAAoB,CAAC,CAClCrB,aAAa,CAAEA,aAAa,CAC5B5K,cAAc,CAAEA,cAAc,CAC9BmK,MAAM,CAAE7C,MAAM,CAAC6C,MAAM,CACrBlC,QAAQ,CAAEkG,SAAS,CAAClG,QACtB,CAAC,CAAE,CACH6H,KAAK,CAAC,+BAAqB,CAAAvM,QAAA,eAE3BzE,IAAA,QACE2R,GAAG,CAAEzQ,cAAe,CACpB0Q,GAAG,CAAC,WAAW,CACfjB,SAAS,CAAC,iBAAiB,CAC5B,CAAC,cACF3Q,IAAA,QAAK2Q,SAAS,CAAC,mBAAmB,CAAAlM,QAAA,CAAC,cAEnC,CAAK,CAAC,EACH,CAAC,cAENzE,IAAA,UAAO2Q,SAAS,CAAC,YAAY,CAAAlM,QAAA,CAAC,oBAAkB,CAAO,CACxD,CACH,CAAC,EAAE,CAAC,CACF,CAAC,cACLzE,IAAA,OAAAyE,QAAA,cACEzE,IAAA,SAAM2Q,SAAS,CAAE,SACftB,SAAS,CAAC7O,aAAa,GAAK,UAAU,CAAG,WAAW,CACpD6O,SAAS,CAAC7O,aAAa,GAAK,QAAQ,CAAG,YAAY,CAAG,SAAS,EAC9D,CAAAiE,QAAA,CACA4K,SAAS,CAACrB,IAAI,CACX,CAAC,CACL,CAAC,cACLhO,IAAA,OAAAyE,QAAA,CAAK4K,SAAS,CAACpB,UAAU,EAAIoB,SAAS,CAACR,QAAQ,EAAIQ,SAAS,CAACN,OAAO,EAAImF,KAAK,CAAG,CAAC,CAAK,CAAC,CAGtF,CAACtQ,oBAAoB,GAAK,KAAK,EAAIA,oBAAoB,GAAK,UAAU,gBACrE1D,KAAA,CAAAE,SAAA,EAAAqE,QAAA,eACEzE,IAAA,OAAAyE,QAAA,CAAK4K,SAAS,CAACnB,QAAQ,CAAGmB,SAAS,CAACnB,QAAQ,CAAC9G,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAAK,CAAC,cACrEpH,IAAA,OAAAyE,QAAA,CAAK4K,SAAS,CAAClB,QAAQ,CAAGkB,SAAS,CAAClB,QAAQ,CAAC/G,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAAK,CAAC,cACrEpH,IAAA,OAAAyE,QAAA,CAAK4K,SAAS,CAACjB,MAAM,CAAGiB,SAAS,CAACjB,MAAM,CAAChH,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAAK,CAAC,cACjEpH,IAAA,OAAAyE,QAAA,CAAK4K,SAAS,CAAChB,YAAY,EAAI,KAAK,CAAK,CAAC,EAC1C,CACH,CAGA,CAACzK,oBAAoB,GAAK,KAAK,EAAIA,oBAAoB,GAAK,QAAQ,gBACnE1D,KAAA,CAAAE,SAAA,EAAAqE,QAAA,eACEzE,IAAA,OAAAyE,QAAA,CAAK4K,SAAS,CAACf,UAAU,EAAI,KAAK,CAAK,CAAC,cACxCtO,IAAA,OAAAyE,QAAA,CAAK4K,SAAS,CAACnB,QAAQ,CAAGmB,SAAS,CAACnB,QAAQ,CAAC9G,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAAK,CAAC,cACrEpH,IAAA,OAAAyE,QAAA,CAAK4K,SAAS,CAACd,UAAU,EAAI,KAAK,CAAK,CAAC,EACxC,CACH,CAGA,CAAC3K,oBAAoB,GAAK,KAAK,EAAIA,oBAAoB,GAAK,OAAO,gBAClE1D,KAAA,CAAAE,SAAA,EAAAqE,QAAA,eACEzE,IAAA,OAAAyE,QAAA,CAAK4K,SAAS,CAACb,SAAS,EAAI,KAAK,CAAK,CAAC,cACvCxO,IAAA,OAAAyE,QAAA,CAAK4K,SAAS,CAACZ,SAAS,EAAI,KAAK,CAAK,CAAC,cACvCzO,IAAA,OAAAyE,QAAA,CAAK4K,SAAS,CAACX,QAAQ,CAAGW,SAAS,CAACX,QAAQ,CAACtH,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAAK,CAAC,EACrE,CACH,cAEDpH,IAAA,OAAAyE,QAAA,CAAK4K,SAAS,CAACV,UAAU,CAAG,CAACU,SAAS,CAACV,UAAU,CAAG,GAAG,EAAEvH,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CAAG,KAAK,CAAK,CAAC,GAtGhF,GAAGiI,SAAS,CAAClG,QAAQ,IAAIkG,SAAS,CAAC7O,aAAa,IAAI0T,KAAK,EAuG9D,CACL,CAAC,CACG,CAAC,EACH,CAAC,CAEZ,CAAC,EAAE,CAAC,CACD,CAAC,EACG,CAAC,EACV,CACP,CAGA1R,eAAe,eACdxC,IAAA,QAAK2Q,SAAS,CAAC,8BAA8B,CAAAlM,QAAA,cAC3CvE,KAAA,QAAKyQ,SAAS,CAAC,2BAA2B,CAAAlM,QAAA,eACxCzE,IAAA,QAAK2Q,SAAS,CAAC,MAAM,CAAAlM,QAAA,cACnBzE,IAAA,CAACX,OAAO,EAACgU,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC3I,IAAI,CAAC,QAAQ,CAAE,CAAC,CACnD,CAAC,cACNzK,KAAA,QAAAuE,QAAA,eACEvE,KAAA,OAAIyQ,SAAS,CAAC,MAAM,CAAAlM,QAAA,EAAC,qBAAmB,CAAC/B,cAAc,CAAC,GAAC,CAACQ,cAAc,EAAK,CAAC,cAC9ElD,IAAA,QAAK2Q,SAAS,CAAC,UAAU,CAACpM,KAAK,CAAE,CAAEuN,MAAM,CAAE,MAAO,CAAE,CAAArN,QAAA,cAClDzE,IAAA,QACE2Q,SAAS,CAAC,cAAc,CACxBhG,IAAI,CAAC,aAAa,CAClBpG,KAAK,CAAE,CAAEsN,KAAK,CAAE,GAAInP,cAAc,CAAGQ,cAAc,CAAI,GAAG,GAAI,CAAE,CAChE,gBAAeR,cAAe,CAC9B,gBAAc,GAAG,CACjB,gBAAeQ,cAAe,CAC/B,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CACN,CAOA,CAACV,eAAe,EAAIF,YAAY,CAACwF,MAAM,CAAG,CAAC,EAAI,CAAC,IAAM,CACrD,KAAM,CAAAuN,WAAW,CAAG/S,YAAY,CAACwF,MAAM,CACvC,KAAM,CAAAwN,gBAAgB,CAAGhT,YAAY,CAACwK,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC9B,OAAO,CAAC,CAACnD,MAAM,CACnE,KAAM,CAAAyN,YAAY,CAAGjT,YAAY,CAACwK,MAAM,CAACC,CAAC,EAAI,CAACA,CAAC,CAAC9B,OAAO,CAAC,CAACnD,MAAM,CAEhE,GAAI,CAAA0N,YAAY,CAAG,OAAO,CAC1B,GAAI,CAAAC,UAAU,CAAG,EAAE,CAEnB,GAAInT,YAAY,CAACqS,IAAI,CAACnM,MAAM,EAAIA,MAAM,CAACuD,wBAAwB,CAAC,CAAE,CAChE;AACA;AACA,KAAM,CAAA2J,aAAa,CAAGpT,YAAY,CAACwK,MAAM,CAACC,CAAC,EAAI,CAACA,CAAC,CAAC1B,MAAM,CAAC,CAACvD,MAAM,CAChE,KAAM,CAAA6N,iBAAiB,CAAGN,WAAW,CAAG,CAAC,CAAIK,aAAa,CAAGL,WAAW,CAAI,GAAG,CAAG,CAAC,CAEnF,GAAIA,WAAW,CAAG,CAAC,CAAE,CACnB,GAAIM,iBAAiB,GAAK,CAAC,CAAE,CAC3B;AACAH,YAAY,CAAG,SAAS,CAC1B,CAAC,IAAM,IAAIG,iBAAiB,GAAK,GAAG,CAAE,CACpC;AACAH,YAAY,CAAG,QAAQ,CACzB,CAAC,IAAM,CACL;AACAA,YAAY,CAAG,SAAS,CACxBC,UAAU,CAAG,sBAAsB,CACrC,CACF,CACF,CAAC,IAAM,CACL;AACA,GAAIF,YAAY,GAAK,CAAC,CAAE,CACtB;AACAC,YAAY,CAAG,SAAS,CAC1B,CAAC,IAAM,IAAIF,gBAAgB,GAAK,CAAC,CAAE,CACjC;AACAE,YAAY,CAAG,QAAQ,CACzB,CAAC,IAAM,CACL;AACAA,YAAY,CAAG,SAAS,CAC1B,CACF,CAEA,mBACExV,IAAA,QAAK2Q,SAAS,CAAC,4BAA4B,CAAAlM,QAAA,cACzCvE,KAAA,CAACd,KAAK,EAAC+T,OAAO,CAAEqC,YAAa,CAAC7E,SAAS,CAAE8E,UAAW,CAAAhR,QAAA,eAClDzE,IAAA,MAAG2Q,SAAS,CAAC,0BAA0B,CAAI,CAAC,aAClC,CAACrO,YAAY,CAACwF,MAAM,CAAC,UAC/B,CAACpE,yBAAyB,cACxBxD,KAAA,CAAAE,SAAA,EAAAqE,QAAA,EACGnC,YAAY,CAACwK,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC9B,OAAO,EAAI8B,CAAC,CAAC3B,SAAS,CAAC,CAACtD,MAAM,CAAC,yBAC3D,CAACxF,YAAY,CAACwK,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC9B,OAAO,EAAI,CAAC8B,CAAC,CAAC3B,SAAS,CAAC,CAACtD,MAAM,CAAC,4BAC5D,CAACxF,YAAY,CAACwK,MAAM,CAACC,CAAC,EAAI,CAACA,CAAC,CAAC9B,OAAO,CAAC,CAACnD,MAAM,CAAC,UAC/C,EAAE,CAAC,cAEH5H,KAAA,CAAAE,SAAA,EAAAqE,QAAA,EACGnC,YAAY,CAACwK,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC9B,OAAO,CAAC,CAACnD,MAAM,CAAC,iCAC5C,CAACxF,YAAY,CAACwK,MAAM,CAACC,CAAC,EAAI,CAACA,CAAC,CAAC9B,OAAO,CAAC,CAACnD,MAAM,CAAC,UAC/C,EAAE,CACH,EACI,CAAC,CACL,CAAC,CAEV,CAAC,EAAE,CAAC,CAIH,CAACtF,eAAe,EAAIF,YAAY,CAACwF,MAAM,CAAG,CAAC,EAAIxF,YAAY,CAACqS,IAAI,CAACnM,MAAM,EAAIA,MAAM,CAACuD,wBAAwB,CAAC,eAC1G/L,IAAA,QAAK2Q,SAAS,CAAC,yBAAyB,CAAAlM,QAAA,cACtCvE,KAAA,CAACnB,IAAI,EAAA0F,QAAA,eACHzE,IAAA,CAACjB,IAAI,CAAC2F,MAAM,EAAAD,QAAA,cACVvE,KAAA,QAAKyQ,SAAS,CAAC,mDAAmD,CAAAlM,QAAA,eAChEzE,IAAA,OAAI2Q,SAAS,CAAC,MAAM,CAAAlM,QAAA,CAAC,yBAAuB,CAAI,CAAC,cACjDvE,KAAA,QAAKyQ,SAAS,CAAC,gBAAgB,CAAAlM,QAAA,eAC7BzE,IAAA,CAAChB,MAAM,EACLmU,OAAO,CAAE3P,WAAW,GAAK,KAAK,CAAG,SAAS,CAAG,iBAAkB,CAC/D8P,IAAI,CAAC,IAAI,CACT3C,SAAS,CAAC,MAAM,CAChB8B,OAAO,CAAEA,CAAA,GAAMhP,cAAc,CAAC,KAAK,CAAE,CAAAgB,QAAA,CACtC,iBAED,CAAQ,CAAC,cACTzE,IAAA,CAAChB,MAAM,EACLmU,OAAO,CAAE3P,WAAW,GAAK,MAAM,CAAG,SAAS,CAAG,iBAAkB,CAChE8P,IAAI,CAAC,IAAI,CACT3C,SAAS,CAAC,MAAM,CAChB8B,OAAO,CAAEA,CAAA,GAAMhP,cAAc,CAAC,MAAM,CAAE,CAAAgB,QAAA,CACvC,uBAED,CAAQ,CAAC,cACTzE,IAAA,CAAChB,MAAM,EACLmU,OAAO,CAAE3P,WAAW,GAAK,UAAU,CAAG,QAAQ,CAAG,gBAAiB,CAClE8P,IAAI,CAAC,IAAI,CACTb,OAAO,CAAEA,CAAA,GAAMhP,cAAc,CAAC,UAAU,CAAE,CAAAgB,QAAA,CAC3C,2BAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACK,CAAC,cACdzE,IAAA,CAACjB,IAAI,CAAC6F,IAAI,EAAAH,QAAA,cACRzE,IAAA,QAAK2Q,SAAS,CAAC,kBAAkB,CAAAlM,QAAA,cAC/BvE,KAAA,UAAOyQ,SAAS,CAAC,qBAAqB,CAAAlM,QAAA,eACpCzE,IAAA,UAAAyE,QAAA,cACEvE,KAAA,OAAAuE,QAAA,eACEzE,IAAA,OAAAyE,QAAA,CAAI,OAAK,CAAI,CAAC,cACdzE,IAAA,OAAAyE,QAAA,CAAI,kBAAgB,CAAI,CAAC,EACvB,CAAC,CACA,CAAC,cACRzE,IAAA,UAAAyE,QAAA,CACGnC,YAAY,CACVwK,MAAM,CAACtE,MAAM,EAAI,CAChB,GAAIhF,WAAW,GAAK,MAAM,CAAE,MAAO,CAAAgF,MAAM,CAAC6C,MAAM,CAChD,GAAI7H,WAAW,GAAK,UAAU,CAAE,MAAO,CAACgF,MAAM,CAAC6C,MAAM,CACrD,MAAO,KAAI,CAAE;AACf,CAAC,CAAC,CACD+D,GAAG,CAAC,CAAC5G,MAAM,CAAE0L,KAAK,GAAK,CACtB,KAAM,CAAA/K,QAAQ,CAAGX,MAAM,CAACW,QAAQ,CAChC,KAAM,CAAAkC,MAAM,CAAG7C,MAAM,CAAC6C,MAAM,CAE5B;AACA,GAAI,CAAAuK,YAAY,CAAG,IAAI,CACvB,GAAI,CAAArJ,SAAS,CAAG,IAAI,CAEpB,GAAI3J,mBAAmB,CAACuG,QAAQ,CAAC,CAAE,CACjC;AACAyM,YAAY,CAAGhT,mBAAmB,CAACuG,QAAQ,CAAC,CAAC2C,aAAa,CAC1DS,SAAS,CAAG3J,mBAAmB,CAACuG,QAAQ,CAAC,CACzC5D,OAAO,CAAC8B,GAAG,CAAC,wBAAwB,CAAE8B,QAAQ,CAAE,WAAW,CAAE,CAAC,CAACyM,YAAY,CAAC,CAC9E,CAAC,IAAM,IAAIhV,gBAAgB,CAACuI,QAAQ,CAAC,CAAE,CACrC;AACAyM,YAAY,CAAGhV,gBAAgB,CAACuI,QAAQ,CAAC,CACzCoD,SAAS,CAAG,CACVT,aAAa,CAAE8J,YAAY,CAC3B1U,cAAc,CAAE,IAAI,CACpBE,OAAO,CAAE,IAAI,CACbiK,MAAM,CAAEA,MACV,CAAC,CACD9F,OAAO,CAAC8B,GAAG,CAAC,0BAA0B,CAAE8B,QAAQ,CAAE,WAAW,CAAE,CAAC,CAACyM,YAAY,CAAC,CAChF,CAAC,IAAM,CACLrQ,OAAO,CAAC8B,GAAG,CAAC,0BAA0B,CAAE8B,QAAQ,CAAC,CACnD,CAEA,mBACEjJ,KAAA,OAAAuE,QAAA,eACEzE,IAAA,OAAAyE,QAAA,cACEvE,KAAA,QAAKyQ,SAAS,CAAC,2BAA2B,CAAAlM,QAAA,EACvCmR,YAAY,cACX5V,IAAA,QACE2R,GAAG,CAAEiE,YAAa,CAClBhE,GAAG,CAAE,aAAasC,KAAK,CAAG,CAAC,EAAG,CAC9BvD,SAAS,CAAC,oBAAoB,CAC9BpM,KAAK,CAAE,CACLsN,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACd+D,SAAS,CAAE,OAAO,CAClBpE,MAAM,CAAE,SACV,CAAE,CACFgB,OAAO,CAAEA,CAAA,GAAMtF,oBAAoB,CAACZ,SAAS,CAAE,CAC/CyE,KAAK,CAAC,yBAAyB,CAChC,CAAC,cAEFhR,IAAA,QACE2Q,SAAS,CAAC,qEAAqE,CAC/EpM,KAAK,CAAE,CACLsN,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdY,eAAe,CAAE,SAAS,CAC1BG,MAAM,CAAE,mBACV,CAAE,CAAApO,QAAA,cAEFzE,IAAA,UAAO2Q,SAAS,CAAC,YAAY,CAAAlM,QAAA,CAAC,UAAQ,CAAO,CAAC,CAC3C,CACN,cACDzE,IAAA,UAAO2Q,SAAS,CAAC,YAAY,CAAAlM,QAAA,CAAE0E,QAAQ,CAAQ,CAAC,EAC7C,CAAC,CACJ,CAAC,cACLnJ,IAAA,OAAAyE,QAAA,cACEzE,IAAA,SAAM2Q,SAAS,CAAE,SAAStF,MAAM,CAAG,YAAY,CAAG,WAAW,EAAG,CAAA5G,QAAA,CAC7D4G,MAAM,CAAG,MAAM,CAAG,UAAU,CACzB,CAAC,CACL,CAAC,GArCElC,QAsCL,CAAC,CAET,CAAC,CAAC,CACC,CAAC,EACH,CAAC,CACL,CAAC,CAKG,CAAC,EACR,CAAC,CACJ,CACN,EACE,CAAC,cAENnJ,IAAA,CAACb,GAAG,EAAC4R,QAAQ,CAAC,OAAO,CAACC,KAAK,CAAC,iBAAiB,CAAAvM,QAAA,cAC3CzE,IAAA,CAACJ,oBAAoB,GAAE,CAAC,CACrB,CAAC,cAENI,IAAA,CAACb,GAAG,EAAC4R,QAAQ,CAAC,aAAa,CAACC,KAAK,CAAC,aAAa,CAAAvM,QAAA,cAC7CzE,IAAA,CAACjB,IAAI,EAAA0F,QAAA,cACHvE,KAAA,CAACnB,IAAI,CAAC6F,IAAI,EAAAH,QAAA,eACRzE,IAAA,OAAAyE,QAAA,CAAI,yBAAuB,CAAI,CAAC,cAChCzE,IAAA,MAAAyE,QAAA,CAAG,kIAGH,CAAG,CAAC,cAEJzE,IAAA,OAAAyE,QAAA,CAAI,aAAW,CAAI,CAAC,cACpBzE,IAAA,MAAAyE,QAAA,CAAG,2JAGH,CAAG,CAAC,cACJvE,KAAA,OAAAuE,QAAA,eACEzE,IAAA,OAAAyE,QAAA,CAAI,4BAA0B,CAAI,CAAC,cACnCzE,IAAA,OAAAyE,QAAA,CAAI,sBAAoB,CAAI,CAAC,cAC7BzE,IAAA,OAAAyE,QAAA,CAAI,QAAM,CAAI,CAAC,cACfzE,IAAA,OAAAyE,QAAA,CAAI,+CAA6C,CAAI,CAAC,EACpD,CAAC,cAELzE,IAAA,OAAAyE,QAAA,CAAI,qBAAmB,CAAI,CAAC,cAC5BzE,IAAA,MAAAyE,QAAA,CAAG,sNAIH,CAAG,CAAC,cACJvE,KAAA,OAAAuE,QAAA,eACEzE,IAAA,OAAAyE,QAAA,CAAI,kBAAgB,CAAI,CAAC,cACzBzE,IAAA,OAAAyE,QAAA,CAAI,aAAW,CAAI,CAAC,cACpBzE,IAAA,OAAAyE,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxBzE,IAAA,OAAAyE,QAAA,CAAI,qBAAmB,CAAI,CAAC,cAC5BzE,IAAA,OAAAyE,QAAA,CAAI,mBAAiB,CAAI,CAAC,EACxB,CAAC,cAELzE,IAAA,OAAAyE,QAAA,CAAI,UAAQ,CAAI,CAAC,cACjBzE,IAAA,MAAAyE,QAAA,CAAG,mKAGH,CAAG,CAAC,cACJvE,KAAA,OAAAuE,QAAA,eACEzE,IAAA,OAAAyE,QAAA,CAAI,6DAA2D,CAAI,CAAC,cACpEzE,IAAA,OAAAyE,QAAA,CAAI,kEAAgE,CAAI,CAAC,cACzEzE,IAAA,OAAAyE,QAAA,CAAI,qEAAmE,CAAI,CAAC,EAC1E,CAAC,cAELzE,IAAA,OAAAyE,QAAA,CAAI,8BAA4B,CAAI,CAAC,cACrCzE,IAAA,MAAAyE,QAAA,CAAG,yJAGH,CAAG,CAAC,cACJvE,KAAA,OAAAuE,QAAA,eACEzE,IAAA,OAAAyE,QAAA,CAAI,qCAAmC,CAAI,CAAC,cAC5CzE,IAAA,OAAAyE,QAAA,CAAI,iCAA+B,CAAI,CAAC,cACxCzE,IAAA,OAAAyE,QAAA,CAAI,8CAA4C,CAAI,CAAC,cACrDzE,IAAA,OAAAyE,QAAA,CAAI,6CAA2C,CAAI,CAAC,EAClD,CAAC,cAELzE,IAAA,OAAAyE,QAAA,CAAI,wBAAsB,CAAI,CAAC,cAC/BvE,KAAA,OAAAuE,QAAA,eACEvE,KAAA,OAAAuE,QAAA,eAAIzE,IAAA,WAAAyE,QAAA,CAAQ,oBAAkB,CAAQ,CAAC,mCAAgC,EAAI,CAAC,cAC5EvE,KAAA,OAAAuE,QAAA,eAAIzE,IAAA,WAAAyE,QAAA,CAAQ,sBAAoB,CAAQ,CAAC,gDAA6C,EAAI,CAAC,cAC3FvE,KAAA,OAAAuE,QAAA,eAAIzE,IAAA,WAAAyE,QAAA,CAAQ,eAAa,CAAQ,CAAC,+CAA4C,EAAI,CAAC,cACnFvE,KAAA,OAAAuE,QAAA,eAAIzE,IAAA,WAAAyE,QAAA,CAAQ,iBAAe,CAAQ,CAAC,2DAAwD,EAAI,CAAC,EAC/F,CAAC,cAELvE,KAAA,QAAKyQ,SAAS,CAAC,kBAAkB,CAAAlM,QAAA,eAC/BvE,KAAA,OAAAuE,QAAA,eAAIzE,IAAA,MAAG2Q,SAAS,CAAC,yBAAyB,CAAI,CAAC,kCAA+B,EAAI,CAAC,cACnF3Q,IAAA,MAAAyE,QAAA,cAAGzE,IAAA,WAAAyE,QAAA,CAAQ,+BAA6B,CAAQ,CAAC,CAAG,CAAC,cACrDvE,KAAA,OAAIyQ,SAAS,CAAC,MAAM,CAAAlM,QAAA,eAClBvE,KAAA,OAAAuE,QAAA,eAAIzE,IAAA,WAAAyE,QAAA,CAAQ,SAAO,CAAQ,CAAC,+DAAkD,EAAI,CAAC,cACnFvE,KAAA,OAAAuE,QAAA,eAAIzE,IAAA,WAAAyE,QAAA,CAAQ,SAAO,CAAQ,CAAC,6EAA2D,EAAI,CAAC,cAC5FvE,KAAA,OAAAuE,QAAA,eAAIzE,IAAA,WAAAyE,QAAA,CAAQ,UAAQ,CAAQ,CAAC,yEAAuD,EAAI,CAAC,EACvF,CAAC,cACLvE,KAAA,MAAAuE,QAAA,eAAGzE,IAAA,WAAAyE,QAAA,CAAQ,oBAAkB,CAAQ,CAAC,8FAA2F,EAAG,CAAC,EAClI,CAAC,cAENzE,IAAA,OAAAyE,QAAA,CAAI,wBAAsB,CAAI,CAAC,cAC/BvE,KAAA,OAAAuE,QAAA,eACEzE,IAAA,OAAAyE,QAAA,CAAI,kEAAgE,CAAI,CAAC,cACzEzE,IAAA,OAAAyE,QAAA,CAAI,sDAAoD,CAAI,CAAC,cAC7DzE,IAAA,OAAAyE,QAAA,CAAI,8EAA4E,CAAI,CAAC,cACrFzE,IAAA,OAAAyE,QAAA,CAAI,8CAA4C,CAAI,CAAC,cACrDzE,IAAA,OAAAyE,QAAA,CAAI,+CAA6C,CAAI,CAAC,EACpD,CAAC,cAELzE,IAAA,OAAAyE,QAAA,CAAI,yBAAuB,CAAI,CAAC,cAChCzE,IAAA,MAAAyE,QAAA,CAAG,yDAEH,CAAG,CAAC,cACJvE,KAAA,OAAAuE,QAAA,eACEvE,KAAA,OAAAuE,QAAA,eAAIzE,IAAA,WAAAyE,QAAA,CAAQ,UAAQ,CAAQ,CAAC,6CAA0C,EAAI,CAAC,cAC5EvE,KAAA,OAAAuE,QAAA,eAAIzE,IAAA,WAAAyE,QAAA,CAAQ,KAAG,CAAQ,CAAC,+CAA4C,EAAI,CAAC,cACzEvE,KAAA,OAAAuE,QAAA,eAAIzE,IAAA,WAAAyE,QAAA,CAAQ,MAAI,CAAQ,CAAC,kEAA+D,EAAI,CAAC,cAC7FvE,KAAA,OAAAuE,QAAA,eAAIzE,IAAA,WAAAyE,QAAA,CAAQ,MAAI,CAAQ,CAAC,mDAAgD,EAAI,CAAC,cAC9EvE,KAAA,OAAAuE,QAAA,eAAIzE,IAAA,WAAAyE,QAAA,CAAQ,eAAa,CAAQ,CAAC,wCAAqC,EAAI,CAAC,EAC1E,CAAC,cAELvE,KAAA,QAAKyQ,SAAS,CAAC,kBAAkB,CAAAlM,QAAA,eAC/BvE,KAAA,OAAAuE,QAAA,eAAIzE,IAAA,MAAG2Q,SAAS,CAAC,yBAAyB,CAAI,CAAC,8BAA2B,EAAI,CAAC,cAC/EzQ,KAAA,MAAGyQ,SAAS,CAAC,MAAM,CAAAlM,QAAA,eACjBzE,IAAA,WAAAyE,QAAA,CAAQ,sBAAoB,CAAQ,CAAC,0MAGvC,EAAG,CAAC,EACD,CAAC,cAENzE,IAAA,MAAAyE,QAAA,CAAG,oHAGH,CAAG,CAAC,EACK,CAAC,CACR,CAAC,CACJ,CAAC,EACF,CAAC,cA6CPvE,KAAA,CAACV,KAAK,EACJ2S,IAAI,CAAE/O,cAAe,CACrB0S,MAAM,CAAEA,CAAA,GAAMzS,iBAAiB,CAAC,KAAK,CAAE,CACvCiQ,IAAI,CAAC,IAAI,CACTyC,QAAQ,MAAAtR,QAAA,eAERzE,IAAA,CAACR,KAAK,CAACkF,MAAM,EAACsR,WAAW,MAAAvR,QAAA,cACvBvE,KAAA,CAACV,KAAK,CAACyW,KAAK,EAAAxR,QAAA,eACVzE,IAAA,MAAG2Q,SAAS,CAAC,mBAAmB,CAAI,CAAC,cAC1B,CAAC,CAAArN,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAE6F,QAAQ,gBACrCjJ,KAAA,UAAOyQ,SAAS,CAAC,YAAY,CAAAlM,QAAA,EAAC,IAAE,CAACnB,iBAAiB,CAAC6F,QAAQ,EAAQ,CACpE,EACU,CAAC,CACF,CAAC,cACfnJ,IAAA,CAACR,KAAK,CAACoF,IAAI,EAAAH,QAAA,CACRnB,iBAAiB,eAChBpD,KAAA,QAAKyQ,SAAS,CAAC,aAAa,CAAAlM,QAAA,eAC1BvE,KAAA,QAAKyQ,SAAS,CAAC,MAAM,CAAAlM,QAAA,eACnBvE,KAAA,OAAIyQ,SAAS,CAAC,MAAM,CAAAlM,QAAA,eAClBzE,IAAA,MAAG2Q,SAAS,CAAC,oBAAoB,CAAI,CAAC,iBAExC,EAAI,CAAC,cACL3Q,IAAA,QACE2R,GAAG,CAAErO,iBAAiB,CAACwI,aAAc,CACrC8F,GAAG,CAAC,gBAAgB,CACpBjB,SAAS,CAAC,WAAW,CACrBpM,KAAK,CAAE,CACL2R,SAAS,CAAE,OAAO,CAClBvD,YAAY,CAAE,KAAK,CACnBE,MAAM,CAAE,mBAAmB,CAC3BG,SAAS,CAAE,2BACb,CAAE,CACH,CAAC,EACC,CAAC,CACL1P,iBAAiB,CAACpC,cAAc,EAAIoC,iBAAiB,CAAC+H,MAAM,eAC3DnL,KAAA,QAAKyQ,SAAS,CAAC,MAAM,CAAAlM,QAAA,eACnBvE,KAAA,OAAIyQ,SAAS,CAAC,MAAM,CAAAlM,QAAA,eAClBzE,IAAA,MAAG2Q,SAAS,CAAC,oBAAoB,CAAI,CAAC,sCAExC,EAAI,CAAC,cACL3Q,IAAA,QACE2R,GAAG,CAAErO,iBAAiB,CAACpC,cAAe,CACtC0Q,GAAG,CAAC,iBAAiB,CACrBjB,SAAS,CAAC,WAAW,CACrBpM,KAAK,CAAE,CACL2R,SAAS,CAAE,OAAO,CAClBvD,YAAY,CAAE,KAAK,CACnBE,MAAM,CAAE,mBAAmB,CAC3BG,SAAS,CAAE,2BACb,CAAE,CACH,CAAC,EACC,CACN,CACA,CAAC1P,iBAAiB,CAAC+H,MAAM,eACxBrL,IAAA,QAAK2Q,SAAS,CAAC,MAAM,CAAAlM,QAAA,cACnBvE,KAAA,CAACd,KAAK,EAAC+T,OAAO,CAAC,MAAM,CAAA1O,QAAA,eACnBzE,IAAA,MAAG2Q,SAAS,CAAC,yBAAyB,CAAI,CAAC,yFAE7C,EAAO,CAAC,CACL,CACN,EACE,CACN,CACS,CAAC,cACb3Q,IAAA,CAACR,KAAK,CAAC2W,MAAM,EAAA1R,QAAA,cACXzE,IAAA,CAAChB,MAAM,EACLmU,OAAO,CAAC,WAAW,CACnBV,OAAO,CAAEA,CAAA,GAAMpP,iBAAiB,CAAC,KAAK,CAAE,CAAAoB,QAAA,CACzC,OAED,CAAQ,CAAC,CACG,CAAC,EACV,CAAC,EACC,CAAC,CAEhB,CAAC,CAED;AACA,KAAM,CAAA2R,MAAM,CAAG;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,GAAI,MAAO,CAAA3G,QAAQ,GAAK,WAAW,CAAE,CACnC,KAAM,CAAA4G,UAAU,CAAG5G,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC,CAElD2G,UAAU,CAACC,SAAS,CAAGF,MAAM,CAC7B3G,QAAQ,CAAC8G,IAAI,CAACrG,WAAW,CAACmG,UAAU,CAAC,CACvC,CAEA,cAAe,CAAAhW,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}