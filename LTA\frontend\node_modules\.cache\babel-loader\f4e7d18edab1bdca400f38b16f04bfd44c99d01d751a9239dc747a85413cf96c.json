{"ast": null, "code": "import React,{useState,useRef,useEffect}from'react';import axios from'axios';import{Container,<PERSON>,Col,Card,<PERSON><PERSON>,Form,Alert,Spinner,Tab,Ta<PERSON>,OverlayTrigger,Popover}from'react-bootstrap';import{<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>}from'react-leaflet';import'leaflet/dist/leaflet.css';import L from'leaflet';import Webcam from'react-webcam';import'./RoadInfrastructure.css';import useResponsive from'../hooks/useResponsive';import{validateUploadFile,showFileValidationError}from'../utils/fileValidation';// Fix the marker icon issue with Leaflet in React\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";delete L.Icon.Default.prototype._getIconUrl;L.Icon.Default.mergeOptions({iconRetinaUrl:require('leaflet/dist/images/marker-icon-2x.png'),iconUrl:require('leaflet/dist/images/marker-icon.png'),shadowUrl:require('leaflet/dist/images/marker-shadow.png')});function RoadInfrastructure(){const[selectedClasses,setSelectedClasses]=useState([]);const[videoFile,setVideoFile]=useState(null);const[videoPreview,setVideoPreview]=useState(null);const[imageFile,setImageFile]=useState(null);const[imagePreview,setImagePreview]=useState(null);const[processedImage,setProcessedImage]=useState(null);const[loading,setLoading]=useState(false);const[error,setError]=useState(null);const[detectionResults,setDetectionResults]=useState(null);const[cameraActive,setCameraActive]=useState(false);const[coordinates,setCoordinates]=useState('Not Available');const[inputSource,setInputSource]=useState('video');const[activeTab,setActiveTab]=useState('detection');const[cameraOrientation,setCameraOrientation]=useState('environment');const[isProcessing,setIsProcessing]=useState(false);const[shouldStop,setShouldStop]=useState(false);const[isBuffering,setIsBuffering]=useState(false);const[isPlaying,setIsPlaying]=useState(false);const[frameBuffer,setFrameBuffer]=useState([]);const[currentFrameIndex,setCurrentFrameIndex]=useState(0);const BUFFER_SIZE=10;// Number of frames to buffer before playback\nconst PLAYBACK_FPS=15;// Playback frame rate\nconst[liveDistinctTable,setLiveDistinctTable]=useState([]);const[liveContinuousTable,setLiveContinuousTable]=useState([]);const[classNames,setClassNames]=useState([]);const webcamRef=useRef(null);const fileInputRef=useRef(null);const{isMobile}=useResponsive();const eventSourceRef=useRef(null);// Create the popover content for road infrastructure\nconst reminderPopover=/*#__PURE__*/_jsxs(Popover,{id:\"reminder-popover\",style:{maxWidth:'300px'},children:[/*#__PURE__*/_jsx(Popover.Header,{as:\"h3\",children:\"\\uD83D\\uDCF8 Image Upload Guidelines\"}),/*#__PURE__*/_jsxs(Popover.Body,{children:[/*#__PURE__*/_jsx(\"p\",{style:{marginBottom:'10px'},children:\"Please ensure your uploaded images are:\"}),/*#__PURE__*/_jsxs(\"ul\",{style:{marginBottom:'0',paddingLeft:'20px'},children:[/*#__PURE__*/_jsx(\"li\",{children:\"Focused directly on the road surface\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Well-lit and clear\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Showing the entire area of concern\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Taken from a reasonable distance to capture context\"})]})]})]});// Road infrastructure classes (from the Python model)\nconst roadInfraClasses=['Hot Thermoplastic Paint-edge_line-','Water-Based Kerb Paint','Single W Metal Beam Crash Barrier','Hot Thermoplastic Paint-lane_line-','Rubber Speed Breaker','YNM Informatory Sign Boards','Cold Plastic Rumble Marking Paint','Raised Pavement Markers'];// Find user's location\nuseEffect(()=>{if(navigator.geolocation){navigator.geolocation.getCurrentPosition(position=>{const{latitude,longitude}=position.coords;setCoordinates(`${latitude}, ${longitude}`);},err=>{console.error(\"Error getting location:\",err);});}},[]);// Handle input source change\nuseEffect(()=>{// Reset relevant states when input source changes\nsetImagePreview(null);setVideoPreview(null);setProcessedImage(null);setDetectionResults(null);setError('');if(inputSource==='camera'){setCameraActive(true);}else{setCameraActive(false);}if(fileInputRef.current){fileInputRef.current.value='';}},[inputSource]);// Handle file input change for video\nconst handleVideoChange=e=>{const file=e.target.files[0];if(file){// Validate video file first\nconst validation=validateUploadFile(file,'video','road_infrastructure_detection');if(!validation.isValid){showFileValidationError(validation.errorMessage,setError);// Clear the file input\nif(e.target){e.target.value='';}return;}// Clear any previous errors\nsetError('');setVideoFile(file);setVideoPreview(URL.createObjectURL(file));setProcessedImage(null);setDetectionResults(null);}};// Handle file input change for image\nconst handleImageChange=e=>{const file=e.target.files[0];if(file){// Validate image file first\nconst validation=validateUploadFile(file,'image','road_infrastructure_detection');if(!validation.isValid){showFileValidationError(validation.errorMessage,setError);// Clear the file input\nif(e.target){e.target.value='';}return;}// Clear any previous errors\nsetError('');setImageFile(file);// Create preview\nconst reader=new FileReader();reader.onloadend=()=>{setImagePreview(reader.result);};reader.readAsDataURL(file);// Reset results\nsetProcessedImage(null);setDetectionResults(null);}};// Handle camera capture\nconst handleCapture=()=>{if(webcamRef.current){const imageSrc=webcamRef.current.getScreenshot();if(imageSrc){setImagePreview(imageSrc);setImageFile(null);setProcessedImage(null);setDetectionResults(null);setError('');}}};// Toggle camera\nconst toggleCamera=()=>{setCameraActive(!cameraActive);if(!cameraActive){// Update coordinates when camera is activated\nif(navigator.geolocation){navigator.geolocation.getCurrentPosition(position=>{const{latitude,longitude}=position.coords;setCoordinates(`${latitude}, ${longitude}`);},err=>{console.error(\"Error getting location:\",err);});}}};// Toggle camera orientation (front/back) for mobile devices\nconst toggleCameraOrientation=()=>{setCameraOrientation(prev=>prev==='environment'?'user':'environment');};// Check if we have media for detection\nconst hasMediaForDetection=()=>{if(inputSource==='video')return!!videoPreview;if(inputSource==='image'||inputSource==='camera')return!!imagePreview;return false;};// Reset detection\nconst handleReset=()=>{setShouldStop(true);setVideoFile(null);setVideoPreview(null);setImageFile(null);setImagePreview(null);setProcessedImage(null);setDetectionResults(null);setError('');setIsProcessing(false);if(fileInputRef.current){fileInputRef.current.value='';}};// Process image/video for detection\nconst handleDetect=async()=>{if(!hasMediaForDetection()||selectedClasses.length===0){setError('Please select input media and at least one detection class');return;}console.log('Starting detection process...');console.log('Selected classes:',selectedClasses);console.log('Input source:',inputSource);console.log('Coordinates:',coordinates);setLoading(true);setError('');setProcessedImage(null);try{const formData=new FormData();formData.append('type','road_infra');formData.append('selectedClasses',JSON.stringify(selectedClasses));formData.append('coordinates',coordinates);// Handle different input sources\nif(inputSource==='video'&&videoFile){// Video processing setup\nconsole.log('Processing video file:',videoFile.name);formData.append('video',videoFile);setIsProcessing(true);setShouldStop(false);setIsBuffering(true);setIsPlaying(false);setFrameBuffer([]);setCurrentFrameIndex(0);}else if((inputSource==='image'||inputSource==='camera')&&imagePreview){// Image/camera processing setup\nconsole.log('Processing image/camera input');const blob=await(await fetch(imagePreview)).blob();formData.append('image',blob,'capture.jpg');}console.log('Sending request to backend...');// Send the request to the backend\nconst uploadResponse=await axios.post('/api/road-infrastructure/detect',formData,{headers:{'Content-Type':'multipart/form-data'}});console.log('Upload response:',uploadResponse.data);if(!uploadResponse.data.success){throw new Error(uploadResponse.data.message);}// Handle different responses based on input type\nif(inputSource==='video'){// For video, establish SSE connection for streaming results\nconst eventSource=new EventSource('/api/road-infrastructure/detect');eventSourceRef.current=eventSource;eventSource.onmessage=event=>{if(eventSourceRef.current===null)return;// If stopped, ignore\nconst data=JSON.parse(event.data);console.log('Received frame data:',data);if(data.success===false){setError(data.message||'Detection failed');eventSource.close();eventSourceRef.current=null;setIsProcessing(false);setLoading(false);return;}if(data.frame&&typeof data.frame==='string'&&data.frame.length>1000){setFrameBuffer(prev=>{const newBuffer=[...prev,data.frame];if(newBuffer.length>=BUFFER_SIZE&&!isPlaying){setIsBuffering(false);setIsPlaying(true);}return newBuffer;});}else if(data.frame&&data.frame.length<=1000){console.warn('Received a frame, but it is too short to be valid. Skipping.');}// Update detection results\nif(data.detections){setDetectionResults(prev=>({...prev,total_frames:data.total_frames,processed_frames:data.frame_count,detections:[...((prev===null||prev===void 0?void 0:prev.detections)||[]),...data.detections],continuous_lengths:data.continuous_lengths,output_path:data.output_path}));}// Update live tables\nif(data.live_distinct_table)setLiveDistinctTable(data.live_distinct_table);if(data.live_continuous_table)setLiveContinuousTable(data.live_continuous_table);if(data.class_names&&Array.isArray(data.class_names)){setClassNames(data.class_names);}// Check if this is the final message\nif(data.tracked_objects){eventSource.close();eventSourceRef.current=null;setIsProcessing(false);setLoading(false);}if(data.stopped_early!==undefined){console.log('Processing ended:',data.stopped_early?'stopped early':'completed');setIsProcessing(false);setIsBuffering(false);setLoading(false);if(data.output_path){// setError(`Processing ${data.stopped_early ? 'stopped' : 'completed'}. Video saved to: ${data.output_path}`);\n}}};eventSource.onerror=error=>{console.error('EventSource error:',error);eventSource.close();eventSourceRef.current=null;setIsProcessing(false);setLoading(false);};// Handle stop request\nif(shouldStop){eventSource.close();eventSourceRef.current=null;setIsProcessing(false);setLoading(false);}}else if(inputSource==='image'||inputSource==='camera'){// For image/camera, process the direct response\nconsole.log('Processing image response:',uploadResponse.data);// Set the processed image\nif(uploadResponse.data.frame){setProcessedImage(uploadResponse.data.frame);}// Set detection results\nif(uploadResponse.data.detections){setDetectionResults({detections:uploadResponse.data.detections,total_frames:1,processed_frames:1});}setLoading(false);}}catch(error){var _error$response,_error$response$data;console.error('Detection error:',error);setError(((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.message)||error.message||'An error occurred during detection. Please try again.');setLoading(false);setIsProcessing(false);}};// Playback timer: play frames from buffer at fixed FPS\nuseEffect(()=>{let playbackInterval;if(isPlaying&&frameBuffer.length>0){playbackInterval=setInterval(()=>{setCurrentFrameIndex(prev=>{if(prev<frameBuffer.length-1){return prev+1;}else{setIsPlaying(false);// Stop at the end\nreturn prev;}});},1000/PLAYBACK_FPS);}return()=>{if(playbackInterval)clearInterval(playbackInterval);};},[isPlaying,frameBuffer]);// Update processedImage when currentFrameIndex changes\nuseEffect(()=>{if(frameBuffer.length>0&&currentFrameIndex<frameBuffer.length){setProcessedImage(frameBuffer[currentFrameIndex]);}},[currentFrameIndex,frameBuffer]);// Playback controls\nconst handlePlayPause=()=>setIsPlaying(p=>!p);const handleRewind=()=>setCurrentFrameIndex(i=>Math.max(i-5,0));const handleForward=()=>setCurrentFrameIndex(i=>Math.min(i+5,frameBuffer.length-1));const handleSliderChange=e=>setCurrentFrameIndex(Number(e.target.value));// Group detections by class\nconst getClassCounts=()=>{if(!detectionResults||!detectionResults.detections)return{};return detectionResults.detections.reduce((acc,det)=>{acc[det.class]=(acc[det.class]||0)+1;return acc;},{});};// Add this new function after the getClassCounts function (around line 380-390)\nconst stopProcessing=async()=>{try{// Close the EventSource connection\nif(eventSourceRef.current){eventSourceRef.current.close();eventSourceRef.current=null;}// Send stop signal to backend\nconst response=await axios.post('/api/road-infrastructure/stop_processing');console.log('Stop processing response:',response.data);// Update UI state\nsetIsProcessing(false);setShouldStop(true);setIsBuffering(false);setIsPlaying(false);setLoading(false);// Keep the last frame and tables visible\nif(frameBuffer.length>0){setProcessedImage(frameBuffer[frameBuffer.length-1]);}return true;}catch(error){console.error('Error stopping processing:',error);setError('Failed to stop processing: '+error.message);setLoading(false);return false;}};// Then modify the existing handleStopProcessing function (around line 399-407)\nconst handleStopProcessing=async()=>{if(isProcessing){const stopped=await stopProcessing();setLoading(false);if(stopped){setError('Processing stopped. Video has been saved.');}}};return/*#__PURE__*/_jsxs(Container,{fluid:true,className:\"mt-3\",children:[/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Card,{className:\"mb-3 shadow-sm\",children:[/*#__PURE__*/_jsx(Card.Header,{className:\"bg-primary text-white py-2\",children:/*#__PURE__*/_jsx(\"h6\",{className:\"mb-0\",children:\"Detection Settings\"})}),/*#__PURE__*/_jsxs(Card.Body,{className:\"py-3\",children:[/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{className:\"mb-1\",children:\"Select Infrastructure Classes to Detect\"}),/*#__PURE__*/_jsx(Form.Control,{as:\"select\",multiple:true,value:selectedClasses,onChange:e=>setSelectedClasses([...e.target.selectedOptions].map(opt=>opt.value)),style:{height:'120px'},children:(classNames.length>0?classNames:['Hot Thermoplastic Paint-edge_line-','Water-Based Kerb Paint','Single W Metal Beam Crash Barrier','Hot Thermoplastic Paint-lane_line-','Rubber Speed Breaker','YNM Informatory Sign Boards','Cold Plastic Rumble Marking Paint','Raised Pavement Markers']).map(cls=>/*#__PURE__*/_jsx(\"option\",{value:cls,children:cls},cls))})]}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{className:\"mb-1\",children:\"Input Source\"}),/*#__PURE__*/_jsxs(Form.Select,{value:inputSource,onChange:e=>setInputSource(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"video\",children:\"Video\"}),/*#__PURE__*/_jsx(\"option\",{value:\"image\",children:\"Image\"}),/*#__PURE__*/_jsx(\"option\",{value:\"camera\",children:\"Live Camera\"})]})]}),/*#__PURE__*/_jsx(OverlayTrigger,{trigger:\"click\",placement:\"right\",overlay:reminderPopover,rootClose:true,children:/*#__PURE__*/_jsx(\"div\",{className:\"sticky-note-icon mb-2\",style:{cursor:'pointer',display:'inline-block'},children:/*#__PURE__*/_jsx(\"img\",{src:\"/remindericon.svg\",alt:\"Image Upload Guidelines\",style:{width:'28px',height:'28px'}})})}),inputSource==='video'&&/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Upload Video\"}),/*#__PURE__*/_jsx(Form.Control,{type:\"file\",accept:\"video/*\",onChange:handleVideoChange,ref:fileInputRef}),videoPreview&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-3\",children:/*#__PURE__*/_jsx(\"video\",{src:videoPreview,controls:true,style:{maxWidth:'100%',maxHeight:'300px'}})})]}),inputSource==='image'&&/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Upload Image\"}),/*#__PURE__*/_jsx(Form.Control,{type:\"file\",accept:\"image/*\",onChange:handleImageChange,ref:fileInputRef}),imagePreview&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-3\",children:/*#__PURE__*/_jsx(\"img\",{src:imagePreview,alt:\"Preview\",style:{maxWidth:'100%',maxHeight:'300px'}})})]}),inputSource==='camera'&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mt-3\",children:[/*#__PURE__*/_jsx(Button,{variant:cameraActive?\"danger\":\"info\",onClick:toggleCamera,className:\"mb-2\",children:cameraActive?'Stop Camera':'Start Camera'}),cameraActive&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"webcam-container mb-3\",children:[/*#__PURE__*/_jsx(Webcam,{audio:false,ref:webcamRef,screenshotFormat:\"image/jpeg\",width:\"100%\",height:\"auto\",videoConstraints:{width:640,height:480,facingMode:cameraOrientation}}),isMobile&&/*#__PURE__*/_jsx(Button,{variant:\"outline-secondary\",onClick:toggleCameraOrientation,className:\"mt-2 mb-2\",size:\"sm\",children:\"Rotate Camera\"})]}),/*#__PURE__*/_jsx(Button,{variant:\"success\",onClick:handleCapture,children:\"Capture Photo\"})]}),imagePreview&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-3\",children:/*#__PURE__*/_jsx(\"img\",{src:imagePreview,alt:\"Captured\",style:{maxWidth:'100%',maxHeight:'300px'}})})]}),error&&/*#__PURE__*/_jsx(Alert,{variant:\"danger\",children:error}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex gap-2 mt-3\",children:[/*#__PURE__*/_jsx(Button,{variant:\"primary\",onClick:handleDetect,disabled:loading||!hasMediaForDetection()||selectedClasses.length===0||isProcessing,children:loading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Spinner,{as:\"span\",animation:\"border\",size:\"sm\",role:\"status\",\"aria-hidden\":\"true\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ms-2\",children:\"Processing...\"})]}):\"Detect Infrastructure\"}),/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:isProcessing?handleStopProcessing:handleReset,disabled:loading&&!isProcessing,children:isProcessing?\"Stop Processing\":\"Reset\"})]})]})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:processedImage?/*#__PURE__*/_jsxs(Card,{className:\"mb-4 shadow-sm\",children:[/*#__PURE__*/_jsx(Card.Header,{className:\"bg-success text-white\",children:/*#__PURE__*/_jsx(\"h5\",{className:\"mb-0\",children:\"Detection Results\"})}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"processed-image-container mb-3\",children:[isBuffering&&/*#__PURE__*/_jsxs(\"div\",{className:\"processing-overlay\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",role:\"status\",children:/*#__PURE__*/_jsx(\"span\",{className:\"visually-hidden\",children:\"Buffering video...\"})}),/*#__PURE__*/_jsx(\"span\",{style:{color:'white',marginLeft:10},children:\"Buffering video...\"})]}),/*#__PURE__*/_jsx(\"img\",{src:processedImage?processedImage.startsWith('data:')?processedImage:`data:image/jpeg;base64,${processedImage}`:'',alt:\"Processed\",style:{maxWidth:'100%'},onError:e=>{e.target.onerror=null;e.target.src='';setError('Failed to display processed frame.');}}),isProcessing&&!isBuffering&&/*#__PURE__*/_jsx(\"div\",{className:\"processing-overlay\",children:/*#__PURE__*/_jsx(Spinner,{animation:\"border\",role:\"status\",children:/*#__PURE__*/_jsx(\"span\",{className:\"visually-hidden\",children:\"Processing...\"})})}),frameBuffer.length>0&&!isBuffering&&/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',marginTop:10,gap:10},children:[/*#__PURE__*/_jsx(\"button\",{onClick:handleRewind,disabled:currentFrameIndex===0,children:\"\\u23EA\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handlePlayPause,children:isPlaying?'⏸️ Pause':'▶️ Play'}),/*#__PURE__*/_jsx(\"button\",{onClick:handleForward,disabled:currentFrameIndex>=frameBuffer.length-1,children:\"\\u23E9\"}),/*#__PURE__*/_jsx(\"input\",{type:\"range\",min:0,max:frameBuffer.length-1,value:currentFrameIndex,onChange:handleSliderChange,style:{flex:1}}),/*#__PURE__*/_jsxs(\"span\",{style:{minWidth:60},children:[currentFrameIndex+1,\" / \",frameBuffer.length]})]})]}),detectionResults&&detectionResults.detections&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Detection Summary\"}),/*#__PURE__*/_jsx(\"div\",{className:\"table-responsive\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"table table-striped\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Infrastructure Type\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Count\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:Object.entries(getClassCounts()).map(_ref=>{let[cls,count]=_ref;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:cls}),/*#__PURE__*/_jsx(\"td\",{children:count})]},cls);})})]})}),detectionResults.continuous_lengths&&Object.keys(detectionResults.continuous_lengths).length>0&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Cumulative Lengths (Continuous Classes)\"}),/*#__PURE__*/_jsx(\"div\",{className:\"table-responsive\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"table table-striped\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Infrastructure Type\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Cumulative Length (km)\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:Object.entries(detectionResults.continuous_lengths).map(_ref2=>{let[cls,length]=_ref2;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:cls}),/*#__PURE__*/_jsx(\"td\",{children:length})]},cls);})})]})})]})]}),liveDistinctTable.length>0&&/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:20},children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Live Discrete Detections\"}),/*#__PURE__*/_jsx(\"div\",{className:\"table-responsive\",style:{maxHeight:200,overflowY:'auto'},children:/*#__PURE__*/_jsxs(\"table\",{className:\"table table-striped table-sm\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Class\"}),/*#__PURE__*/_jsx(\"th\",{children:\"GPS\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Frame\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Second\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:liveDistinctTable.map(row=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:row.ID}),/*#__PURE__*/_jsx(\"td\",{children:row.Class}),/*#__PURE__*/_jsx(\"td\",{children:row.GPS?`${row.GPS[0].toFixed(6)}, ${row.GPS[1].toFixed(6)}`:'-'}),/*#__PURE__*/_jsx(\"td\",{children:row.Frame}),/*#__PURE__*/_jsx(\"td\",{children:row.Second})]},row.ID))})]})})]}),liveContinuousTable.length>0&&/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:20},children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Live Continuous (Cumulative) Data\"}),/*#__PURE__*/_jsx(\"div\",{className:\"table-responsive\",style:{maxHeight:150,overflowY:'auto'},children:/*#__PURE__*/_jsxs(\"table\",{className:\"table table-striped table-sm\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Class\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Cumulative Length (km)\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:liveContinuousTable.map(row=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:row.Class}),/*#__PURE__*/_jsx(\"td\",{children:row['Cumulative Length (km)']})]},row.Class))})]})})]})]})]}):/*#__PURE__*/_jsxs(Card,{className:\"mb-4 shadow-sm\",children:[/*#__PURE__*/_jsx(Card.Header,{className:\"bg-info text-white\",children:/*#__PURE__*/_jsx(\"h5\",{className:\"mb-0\",children:\"Instructions\"})}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(\"ol\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"Select one or more infrastructure types to detect\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Choose your input source (video, image, or camera)\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Upload media or capture a photo\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Click \\\"Detect Infrastructure\\\" to analyze\"})]}),/*#__PURE__*/_jsx(\"p\",{children:\"Detection will identify and highlight road infrastructure features such as:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"Pavement markings\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Road signs\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Safety barriers\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Road edge lines\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Lane markings\"})]})]})]})})]}),/*#__PURE__*/_jsxs(Tabs,{activeKey:activeTab,onSelect:k=>setActiveTab(k),className:\"mt-4\",children:[/*#__PURE__*/_jsx(Tab,{eventKey:\"detection\",title:\"Detection\",children:/*#__PURE__*/_jsx(Card,{className:\"shadow-sm\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h4\",{children:\"About Road Infrastructure Analysis\"}),/*#__PURE__*/_jsx(\"p\",{children:\"The Road Infrastructure Analysis module uses computer vision to detect, classify, and analyze various road infrastructure elements. This helps in infrastructure inventory management and maintenance planning.\"}),/*#__PURE__*/_jsx(\"h5\",{children:\"Detectable Infrastructure Types\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Cold Plastic Rumble Marking Paint\"}),\" - Textured road markings that provide tactile and auditory warnings\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Raised Pavement Markers\"}),\" - Reflective or non-reflective markers installed on roadways\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Rubber Speed Breaker\"}),\" - Traffic calming devices to reduce vehicle speeds\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"SW_Beam_Crash_Barrier\"}),\" - Safety barriers to prevent vehicles from veering off the road\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Water-Based Kerb Paint\"}),\" - Paint used for road edge visibility and demarcation\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"YNM Informatory Sign Boards\"}),\" - Road signs providing information to road users\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"HTP-edge_line\"}),\" - High-performance thermoplastic road edge line markings\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"HTP-lane_line\"}),\" - High-performance thermoplastic lane separators\"]})]}),/*#__PURE__*/_jsx(\"h5\",{children:\"How The System Works\"}),/*#__PURE__*/_jsx(\"p\",{children:\"The system uses a trained YOLOv8 object detection model to identify infrastructure elements in images or video. For each detection, the system records:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"The type of infrastructure element\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Confidence score of the detection\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Physical dimensions (where applicable)\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Geolocation (when available from device GPS)\"})]}),/*#__PURE__*/_jsx(\"h5\",{children:\"Use Cases\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"Road infrastructure inventory management\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Monitoring road marking conditions\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Planning maintenance and replacement schedules\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Assessing compliance with safety standards\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Calculating infrastructure density and distribution\"})]})]})})}),/*#__PURE__*/_jsx(Tab,{eventKey:\"information\",title:\"Information\",children:/*#__PURE__*/_jsx(Card,{className:\"shadow-sm\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h4\",{children:\"About Road Infrastructure Analysis\"}),/*#__PURE__*/_jsx(\"p\",{children:\"The Road Infrastructure Analysis module uses computer vision to detect, classify, and analyze various road infrastructure elements. This helps in infrastructure inventory management and maintenance planning.\"}),/*#__PURE__*/_jsx(\"h5\",{children:\"Detectable Infrastructure Types\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Cold Plastic Rumble Marking Paint\"}),\" - Textured road markings that provide tactile and auditory warnings\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Raised Pavement Markers\"}),\" - Reflective or non-reflective markers installed on roadways\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Rubber Speed Breaker\"}),\" - Traffic calming devices to reduce vehicle speeds\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"SW_Beam_Crash_Barrier\"}),\" - Safety barriers to prevent vehicles from veering off the road\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Water-Based Kerb Paint\"}),\" - Paint used for road edge visibility and demarcation\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"YNM Informatory Sign Boards\"}),\" - Road signs providing information to road users\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"HTP-edge_line\"}),\" - High-performance thermoplastic road edge line markings\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"HTP-lane_line\"}),\" - High-performance thermoplastic lane separators\"]})]}),/*#__PURE__*/_jsx(\"h5\",{children:\"How The System Works\"}),/*#__PURE__*/_jsx(\"p\",{children:\"The system uses a trained YOLOv8 object detection model to identify infrastructure elements in images or video. For each detection, the system records:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"The type of infrastructure element\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Confidence score of the detection\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Physical dimensions (where applicable)\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Geolocation (when available from device GPS)\"})]}),/*#__PURE__*/_jsx(\"h5\",{children:\"Use Cases\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"Road infrastructure inventory management\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Monitoring road marking conditions\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Planning maintenance and replacement schedules\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Assessing compliance with safety standards\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Calculating infrastructure density and distribution\"})]})]})})})]})]});}export default RoadInfrastructure;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "axios", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Form", "<PERSON><PERSON>", "Spinner", "Tab", "Tabs", "OverlayTrigger", "Popover", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "L", "Webcam", "useResponsive", "validateUploadFile", "showFileValidationError", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "require", "iconUrl", "shadowUrl", "RoadInfrastructure", "selectedClasses", "setSelectedClasses", "videoFile", "setVideoFile", "videoPreview", "setVideoPreview", "imageFile", "setImageFile", "imagePreview", "setImagePreview", "processedImage", "setProcessedImage", "loading", "setLoading", "error", "setError", "detectionResults", "setDetectionResults", "cameraActive", "setCameraActive", "coordinates", "setCoordinates", "inputSource", "setInputSource", "activeTab", "setActiveTab", "cameraOrientation", "setCameraOrientation", "isProcessing", "setIsProcessing", "shouldStop", "setShouldStop", "isBuffering", "setIsBuffering", "isPlaying", "setIsPlaying", "frameBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentFrameIndex", "setCurrentFrameIndex", "BUFFER_SIZE", "PLAYBACK_FPS", "liveDistinctTable", "setLiveDistinctTable", "liveContinuousTable", "setLiveContinuousTable", "classNames", "setClassNames", "webcamRef", "fileInputRef", "isMobile", "eventSourceRef", "reminderPopover", "id", "style", "max<PERSON><PERSON><PERSON>", "children", "Header", "as", "Body", "marginBottom", "paddingLeft", "roadInfraClasses", "navigator", "geolocation", "getCurrentPosition", "position", "latitude", "longitude", "coords", "err", "console", "current", "value", "handleVideoChange", "e", "file", "target", "files", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errorMessage", "URL", "createObjectURL", "handleImageChange", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleCapture", "imageSrc", "getScreenshot", "toggleCamera", "toggleCameraOrientation", "prev", "hasMediaForDetection", "handleReset", "handleDetect", "length", "log", "formData", "FormData", "append", "JSON", "stringify", "name", "blob", "fetch", "uploadResponse", "post", "headers", "data", "success", "Error", "message", "eventSource", "EventSource", "onmessage", "event", "parse", "close", "frame", "new<PERSON>uffer", "warn", "detections", "total_frames", "processed_frames", "frame_count", "continuous_lengths", "output_path", "live_distinct_table", "live_continuous_table", "class_names", "Array", "isArray", "tracked_objects", "stopped_early", "undefined", "onerror", "_error$response", "_error$response$data", "response", "playbackInterval", "setInterval", "clearInterval", "handlePlayPause", "p", "handleRewind", "i", "Math", "max", "handleForward", "min", "handleSliderChange", "Number", "getClassCounts", "reduce", "acc", "det", "class", "stopProcessing", "handleStopProcessing", "stopped", "fluid", "className", "md", "Group", "Label", "Control", "multiple", "onChange", "selectedOptions", "map", "opt", "height", "cls", "Select", "trigger", "placement", "overlay", "rootClose", "cursor", "display", "src", "alt", "width", "type", "accept", "ref", "controls", "maxHeight", "variant", "onClick", "audio", "screenshotFormat", "videoConstraints", "facingMode", "size", "disabled", "animation", "role", "color", "marginLeft", "startsWith", "onError", "alignItems", "marginTop", "gap", "flex", "min<PERSON><PERSON><PERSON>", "Object", "entries", "_ref", "count", "keys", "_ref2", "overflowY", "row", "ID", "Class", "GPS", "toFixed", "<PERSON>ame", "Second", "active<PERSON><PERSON>", "onSelect", "k", "eventKey", "title"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/pages/RoadInfrastructure.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport { Container, Row, Col, Card, Button, Form, Alert, Spinner, Tab, Tabs, OverlayTrigger, Popover } from 'react-bootstrap';\r\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';\r\nimport 'leaflet/dist/leaflet.css';\r\nimport L from 'leaflet';\r\nimport Webcam from 'react-webcam';\r\nimport './RoadInfrastructure.css';\r\nimport useResponsive from '../hooks/useResponsive';\r\nimport { validateUploadFile, showFileValidationError } from '../utils/fileValidation';\r\n\r\n// Fix the marker icon issue with Leaflet in React\r\ndelete L.Icon.Default.prototype._getIconUrl;\r\nL.Icon.Default.mergeOptions({\r\n  iconRetinaUrl: require('leaflet/dist/images/marker-icon-2x.png'),\r\n  iconUrl: require('leaflet/dist/images/marker-icon.png'),\r\n  shadowUrl: require('leaflet/dist/images/marker-shadow.png'),\r\n});\r\n\r\nfunction RoadInfrastructure() {\r\n  const [selectedClasses, setSelectedClasses] = useState([]);\r\n  const [videoFile, setVideoFile] = useState(null);\r\n  const [videoPreview, setVideoPreview] = useState(null);\r\n  const [imageFile, setImageFile] = useState(null);\r\n  const [imagePreview, setImagePreview] = useState(null);\r\n  const [processedImage, setProcessedImage] = useState(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  const [detectionResults, setDetectionResults] = useState(null);\r\n  const [cameraActive, setCameraActive] = useState(false);\r\n  const [coordinates, setCoordinates] = useState('Not Available');\r\n  const [inputSource, setInputSource] = useState('video');\r\n  const [activeTab, setActiveTab] = useState('detection');\r\n  const [cameraOrientation, setCameraOrientation] = useState('environment');\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n  const [shouldStop, setShouldStop] = useState(false);\r\n  const [isBuffering, setIsBuffering] = useState(false);\r\n  const [isPlaying, setIsPlaying] = useState(false);\r\n  const [frameBuffer, setFrameBuffer] = useState([]);\r\n  const [currentFrameIndex, setCurrentFrameIndex] = useState(0);\r\n  const BUFFER_SIZE = 10; // Number of frames to buffer before playback\r\n  const PLAYBACK_FPS = 15; // Playback frame rate\r\n  const [liveDistinctTable, setLiveDistinctTable] = useState([]);\r\n  const [liveContinuousTable, setLiveContinuousTable] = useState([]);\r\n  const [classNames, setClassNames] = useState([]);\r\n  \r\n  const webcamRef = useRef(null);\r\n  const fileInputRef = useRef(null);\r\n  const { isMobile } = useResponsive();\r\n  const eventSourceRef = useRef(null);\r\n\r\n  // Create the popover content for road infrastructure\r\n  const reminderPopover = (\r\n    <Popover id=\"reminder-popover\" style={{ maxWidth: '300px' }}>\r\n      <Popover.Header as=\"h3\">📸 Image Upload Guidelines</Popover.Header>\r\n      <Popover.Body>\r\n        <p style={{ marginBottom: '10px' }}>\r\n          Please ensure your uploaded images are:\r\n        </p>\r\n        <ul style={{ marginBottom: '0', paddingLeft: '20px' }}>\r\n          <li>Focused directly on the road surface</li>\r\n          <li>Well-lit and clear</li>\r\n          <li>Showing the entire area of concern</li>\r\n          <li>Taken from a reasonable distance to capture context</li>\r\n        </ul>\r\n      </Popover.Body>\r\n    </Popover>\r\n  );\r\n\r\n  // Road infrastructure classes (from the Python model)\r\n  const roadInfraClasses = [\r\n    'Hot Thermoplastic Paint-edge_line-',\r\n    'Water-Based Kerb Paint',\r\n    'Single W Metal Beam Crash Barrier',\r\n    'Hot Thermoplastic Paint-lane_line-',\r\n    'Rubber Speed Breaker',\r\n    'YNM Informatory Sign Boards',\r\n    'Cold Plastic Rumble Marking Paint',\r\n    'Raised Pavement Markers'\r\n  ];\r\n\r\n  // Find user's location\r\n  useEffect(() => {\r\n    if (navigator.geolocation) {\r\n      navigator.geolocation.getCurrentPosition(\r\n        (position) => {\r\n          const { latitude, longitude } = position.coords;\r\n          setCoordinates(`${latitude}, ${longitude}`);\r\n        },\r\n        (err) => {\r\n          console.error(\"Error getting location:\", err);\r\n        }\r\n      );\r\n    }\r\n  }, []);\r\n\r\n  // Handle input source change\r\n  useEffect(() => {\r\n    // Reset relevant states when input source changes\r\n    setImagePreview(null);\r\n    setVideoPreview(null);\r\n    setProcessedImage(null);\r\n    setDetectionResults(null);\r\n    setError('');\r\n    \r\n    if (inputSource === 'camera') {\r\n      setCameraActive(true);\r\n    } else {\r\n      setCameraActive(false);\r\n    }\r\n    \r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  }, [inputSource]);\r\n\r\n  // Handle file input change for video\r\n  const handleVideoChange = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      // Validate video file first\r\n      const validation = validateUploadFile(file, 'video', 'road_infrastructure_detection');\r\n      if (!validation.isValid) {\r\n        showFileValidationError(validation.errorMessage, setError);\r\n        // Clear the file input\r\n        if (e.target) {\r\n          e.target.value = '';\r\n        }\r\n        return;\r\n      }\r\n\r\n      // Clear any previous errors\r\n      setError('');\r\n\r\n      setVideoFile(file);\r\n      setVideoPreview(URL.createObjectURL(file));\r\n      setProcessedImage(null);\r\n      setDetectionResults(null);\r\n    }\r\n  };\r\n\r\n  // Handle file input change for image\r\n  const handleImageChange = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      // Validate image file first\r\n      const validation = validateUploadFile(file, 'image', 'road_infrastructure_detection');\r\n      if (!validation.isValid) {\r\n        showFileValidationError(validation.errorMessage, setError);\r\n        // Clear the file input\r\n        if (e.target) {\r\n          e.target.value = '';\r\n        }\r\n        return;\r\n      }\r\n\r\n      // Clear any previous errors\r\n      setError('');\r\n\r\n      setImageFile(file);\r\n\r\n      // Create preview\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => {\r\n        setImagePreview(reader.result);\r\n      };\r\n      reader.readAsDataURL(file);\r\n\r\n      // Reset results\r\n      setProcessedImage(null);\r\n      setDetectionResults(null);\r\n    }\r\n  };\r\n\r\n  // Handle camera capture\r\n  const handleCapture = () => {\r\n    if (webcamRef.current) {\r\n      const imageSrc = webcamRef.current.getScreenshot();\r\n      if (imageSrc) {\r\n        setImagePreview(imageSrc);\r\n        setImageFile(null);\r\n        setProcessedImage(null);\r\n        setDetectionResults(null);\r\n        setError('');\r\n      }\r\n    }\r\n  };\r\n\r\n  // Toggle camera\r\n  const toggleCamera = () => {\r\n    setCameraActive(!cameraActive);\r\n    if (!cameraActive) {\r\n      // Update coordinates when camera is activated\r\n      if (navigator.geolocation) {\r\n        navigator.geolocation.getCurrentPosition(\r\n          (position) => {\r\n            const { latitude, longitude } = position.coords;\r\n            setCoordinates(`${latitude}, ${longitude}`);\r\n          },\r\n          (err) => {\r\n            console.error(\"Error getting location:\", err);\r\n          }\r\n        );\r\n      }\r\n    }\r\n  };\r\n\r\n  // Toggle camera orientation (front/back) for mobile devices\r\n  const toggleCameraOrientation = () => {\r\n    setCameraOrientation(prev => prev === 'environment' ? 'user' : 'environment');\r\n  };\r\n\r\n  // Check if we have media for detection\r\n  const hasMediaForDetection = () => {\r\n    if (inputSource === 'video') return !!videoPreview;\r\n    if (inputSource === 'image' || inputSource === 'camera') return !!imagePreview;\r\n    return false;\r\n  };\r\n\r\n  // Reset detection\r\n  const handleReset = () => {\r\n    setShouldStop(true);\r\n    setVideoFile(null);\r\n    setVideoPreview(null);\r\n    setImageFile(null);\r\n    setImagePreview(null);\r\n    setProcessedImage(null);\r\n    setDetectionResults(null);\r\n    setError('');\r\n    setIsProcessing(false);\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  // Process image/video for detection\r\n  const handleDetect = async () => {\r\n    if (!hasMediaForDetection() || selectedClasses.length === 0) {\r\n      setError('Please select input media and at least one detection class');\r\n      return;\r\n    }\r\n\r\n    console.log('Starting detection process...');\r\n    console.log('Selected classes:', selectedClasses);\r\n    console.log('Input source:', inputSource);\r\n    console.log('Coordinates:', coordinates);\r\n\r\n    setLoading(true);\r\n    setError('');\r\n    setProcessedImage(null);\r\n\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('type', 'road_infra');\r\n      formData.append('selectedClasses', JSON.stringify(selectedClasses));\r\n      formData.append('coordinates', coordinates);\r\n\r\n      // Handle different input sources\r\n      if (inputSource === 'video' && videoFile) {\r\n        // Video processing setup\r\n        console.log('Processing video file:', videoFile.name);\r\n        formData.append('video', videoFile);\r\n        setIsProcessing(true);\r\n        setShouldStop(false);\r\n        setIsBuffering(true);\r\n        setIsPlaying(false);\r\n        setFrameBuffer([]);\r\n        setCurrentFrameIndex(0);\r\n      } else if ((inputSource === 'image' || inputSource === 'camera') && imagePreview) {\r\n        // Image/camera processing setup\r\n        console.log('Processing image/camera input');\r\n        const blob = await (await fetch(imagePreview)).blob();\r\n        formData.append('image', blob, 'capture.jpg');\r\n      }\r\n\r\n      console.log('Sending request to backend...');\r\n      \r\n      // Send the request to the backend\r\n      const uploadResponse = await axios.post('/api/road-infrastructure/detect', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data'\r\n        }\r\n      });\r\n      \r\n      console.log('Upload response:', uploadResponse.data);\r\n      \r\n      if (!uploadResponse.data.success) {\r\n        throw new Error(uploadResponse.data.message);\r\n      }\r\n      \r\n      // Handle different responses based on input type\r\n      if (inputSource === 'video') {\r\n        // For video, establish SSE connection for streaming results\r\n        const eventSource = new EventSource('/api/road-infrastructure/detect');\r\n        eventSourceRef.current = eventSource;\r\n        \r\n        eventSource.onmessage = (event) => {\r\n          if (eventSourceRef.current === null) return; // If stopped, ignore\r\n          const data = JSON.parse(event.data);\r\n          console.log('Received frame data:', data);\r\n          \r\n          if (data.success === false) {\r\n            setError(data.message || 'Detection failed');\r\n            eventSource.close();\r\n            eventSourceRef.current = null;\r\n            setIsProcessing(false);\r\n            setLoading(false);\r\n            return;\r\n          }\r\n          \r\n          if (data.frame && typeof data.frame === 'string' && data.frame.length > 1000) {\r\n            setFrameBuffer(prev => {\r\n              const newBuffer = [...prev, data.frame];\r\n              if (newBuffer.length >= BUFFER_SIZE && !isPlaying) {\r\n                setIsBuffering(false);\r\n                setIsPlaying(true);\r\n              }\r\n              return newBuffer;\r\n            });\r\n          } else if (data.frame && data.frame.length <= 1000) {\r\n            console.warn('Received a frame, but it is too short to be valid. Skipping.');\r\n          }\r\n          \r\n          // Update detection results\r\n          if (data.detections) {\r\n            setDetectionResults(prev => ({\r\n              ...prev,\r\n              total_frames: data.total_frames,\r\n              processed_frames: data.frame_count,\r\n              detections: [...(prev?.detections || []), ...data.detections],\r\n              continuous_lengths: data.continuous_lengths,\r\n              output_path: data.output_path\r\n            }));\r\n          }\r\n          \r\n          // Update live tables\r\n          if (data.live_distinct_table) setLiveDistinctTable(data.live_distinct_table);\r\n          if (data.live_continuous_table) setLiveContinuousTable(data.live_continuous_table);\r\n          \r\n          if (data.class_names && Array.isArray(data.class_names)) {\r\n            setClassNames(data.class_names);\r\n          }\r\n          \r\n          // Check if this is the final message\r\n          if (data.tracked_objects) {\r\n            eventSource.close();\r\n            eventSourceRef.current = null;\r\n            setIsProcessing(false);\r\n            setLoading(false);\r\n          }\r\n\r\n          if (data.stopped_early !== undefined) {\r\n            console.log('Processing ended:', data.stopped_early ? 'stopped early' : 'completed');\r\n            setIsProcessing(false);\r\n            setIsBuffering(false);\r\n            setLoading(false);\r\n            if (data.output_path) {\r\n              // setError(`Processing ${data.stopped_early ? 'stopped' : 'completed'}. Video saved to: ${data.output_path}`);\r\n            }\r\n          }\r\n        };\r\n        \r\n        eventSource.onerror = (error) => {\r\n          console.error('EventSource error:', error);\r\n          eventSource.close();\r\n          eventSourceRef.current = null;\r\n          setIsProcessing(false);\r\n          setLoading(false);\r\n        };\r\n        \r\n        // Handle stop request\r\n        if (shouldStop) {\r\n          eventSource.close();\r\n          eventSourceRef.current = null;\r\n          setIsProcessing(false);\r\n          setLoading(false);\r\n        }\r\n      } else if (inputSource === 'image' || inputSource === 'camera') {\r\n        // For image/camera, process the direct response\r\n        console.log('Processing image response:', uploadResponse.data);\r\n        \r\n        // Set the processed image\r\n        if (uploadResponse.data.frame) {\r\n          setProcessedImage(uploadResponse.data.frame);\r\n        }\r\n        \r\n        // Set detection results\r\n        if (uploadResponse.data.detections) {\r\n          setDetectionResults({\r\n            detections: uploadResponse.data.detections,\r\n            total_frames: 1,\r\n            processed_frames: 1\r\n          });\r\n        }\r\n        \r\n        setLoading(false);\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error('Detection error:', error);\r\n      setError(\r\n        error.response?.data?.message || \r\n        error.message ||\r\n        'An error occurred during detection. Please try again.'\r\n      );\r\n      setLoading(false);\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  // Playback timer: play frames from buffer at fixed FPS\r\n  useEffect(() => {\r\n    let playbackInterval;\r\n    if (isPlaying && frameBuffer.length > 0) {\r\n      playbackInterval = setInterval(() => {\r\n        setCurrentFrameIndex(prev => {\r\n          if (prev < frameBuffer.length - 1) {\r\n            return prev + 1;\r\n          } else {\r\n            setIsPlaying(false); // Stop at the end\r\n            return prev;\r\n          }\r\n        });\r\n      }, 1000 / PLAYBACK_FPS);\r\n    }\r\n    return () => {\r\n      if (playbackInterval) clearInterval(playbackInterval);\r\n    };\r\n  }, [isPlaying, frameBuffer]);\r\n\r\n  // Update processedImage when currentFrameIndex changes\r\n  useEffect(() => {\r\n    if (frameBuffer.length > 0 && currentFrameIndex < frameBuffer.length) {\r\n      setProcessedImage(frameBuffer[currentFrameIndex]);\r\n    }\r\n  }, [currentFrameIndex, frameBuffer]);\r\n\r\n  // Playback controls\r\n  const handlePlayPause = () => setIsPlaying(p => !p);\r\n  const handleRewind = () => setCurrentFrameIndex(i => Math.max(i - 5, 0));\r\n  const handleForward = () => setCurrentFrameIndex(i => Math.min(i + 5, frameBuffer.length - 1));\r\n  const handleSliderChange = (e) => setCurrentFrameIndex(Number(e.target.value));\r\n\r\n  // Group detections by class\r\n  const getClassCounts = () => {\r\n    if (!detectionResults || !detectionResults.detections) return {};\r\n    \r\n    return detectionResults.detections.reduce((acc, det) => {\r\n      acc[det.class] = (acc[det.class] || 0) + 1;\r\n      return acc;\r\n    }, {});\r\n  };\r\n\r\n  // Add this new function after the getClassCounts function (around line 380-390)\r\n  const stopProcessing = async () => {\r\n    try {\r\n      // Close the EventSource connection\r\n      if (eventSourceRef.current) {\r\n        eventSourceRef.current.close();\r\n        eventSourceRef.current = null;\r\n      }\r\n\r\n      // Send stop signal to backend\r\n      const response = await axios.post('/api/road-infrastructure/stop_processing');\r\n      console.log('Stop processing response:', response.data);\r\n\r\n      // Update UI state\r\n      setIsProcessing(false);\r\n      setShouldStop(true);\r\n      setIsBuffering(false);\r\n      setIsPlaying(false);\r\n      setLoading(false);\r\n\r\n      // Keep the last frame and tables visible\r\n      if (frameBuffer.length > 0) {\r\n        setProcessedImage(frameBuffer[frameBuffer.length - 1]);\r\n      }\r\n\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Error stopping processing:', error);\r\n      setError('Failed to stop processing: ' + error.message);\r\n      setLoading(false);\r\n      return false;\r\n    }\r\n  };\r\n\r\n  // Then modify the existing handleStopProcessing function (around line 399-407)\r\n  const handleStopProcessing = async () => {\r\n    if (isProcessing) {\r\n      const stopped = await stopProcessing();\r\n      setLoading(false);\r\n      if (stopped) {\r\n        setError('Processing stopped. Video has been saved.');\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Container fluid className=\"mt-3\">\r\n      <Row>\r\n        <Col md={6}>\r\n          <Card className=\"mb-3 shadow-sm\">\r\n            <Card.Header className=\"bg-primary text-white py-2\">\r\n              <h6 className=\"mb-0\">Detection Settings</h6>\r\n            </Card.Header>\r\n            <Card.Body className=\"py-3\">\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label className=\"mb-1\">Select Infrastructure Classes to Detect</Form.Label>\r\n                <Form.Control\r\n                  as=\"select\"\r\n                  multiple\r\n                  value={selectedClasses}\r\n                  onChange={(e) => setSelectedClasses([...e.target.selectedOptions].map(opt => opt.value))}\r\n                  style={{ height: '120px' }}\r\n                >\r\n                  {(classNames.length > 0 ? classNames : [\r\n                    'Hot Thermoplastic Paint-edge_line-',\r\n                    'Water-Based Kerb Paint',\r\n                    'Single W Metal Beam Crash Barrier',\r\n                    'Hot Thermoplastic Paint-lane_line-',\r\n                    'Rubber Speed Breaker',\r\n                    'YNM Informatory Sign Boards',\r\n                    'Cold Plastic Rumble Marking Paint',\r\n                    'Raised Pavement Markers'\r\n                  ]).map((cls) => (\r\n                    <option key={cls} value={cls}>{cls}</option>\r\n                  ))}\r\n                </Form.Control>\r\n              </Form.Group>\r\n\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label className=\"mb-1\">Input Source</Form.Label>\r\n                <Form.Select\r\n                  value={inputSource}\r\n                  onChange={(e) => setInputSource(e.target.value)}\r\n                >\r\n                  <option value=\"video\">Video</option>\r\n                  <option value=\"image\">Image</option>\r\n                  <option value=\"camera\">Live Camera</option>\r\n                </Form.Select>\r\n              </Form.Group>\r\n\r\n              {/* Sticky note reminder */}\r\n              <OverlayTrigger \r\n                trigger=\"click\" \r\n                placement=\"right\" \r\n                overlay={reminderPopover}\r\n                rootClose\r\n              >\r\n                <div \r\n                  className=\"sticky-note-icon mb-2\"\r\n                  style={{ cursor: 'pointer', display: 'inline-block' }}\r\n                >\r\n                  <img \r\n                    src=\"/remindericon.svg\" \r\n                    alt=\"Image Upload Guidelines\" \r\n                    style={{ width: '28px', height: '28px' }}\r\n                  />\r\n                </div>\r\n              </OverlayTrigger>\r\n\r\n              {inputSource === 'video' && (\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Upload Video</Form.Label>\r\n                  <Form.Control \r\n                    type=\"file\" \r\n                    accept=\"video/*\"\r\n                    onChange={handleVideoChange}\r\n                    ref={fileInputRef}\r\n                  />\r\n                  {videoPreview && (\r\n                    <div className=\"mt-3\">\r\n                      <video \r\n                        src={videoPreview} \r\n                        controls \r\n                        style={{ maxWidth: '100%', maxHeight: '300px' }} \r\n                      />\r\n                    </div>\r\n                  )}\r\n                </Form.Group>\r\n              )}\r\n\r\n              {inputSource === 'image' && (\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Upload Image</Form.Label>\r\n                  <Form.Control \r\n                    type=\"file\" \r\n                    accept=\"image/*\"\r\n                    onChange={handleImageChange}\r\n                    ref={fileInputRef}\r\n                  />\r\n                  {imagePreview && (\r\n                    <div className=\"mt-3\">\r\n                      <img \r\n                        src={imagePreview} \r\n                        alt=\"Preview\" \r\n                        style={{ maxWidth: '100%', maxHeight: '300px' }} \r\n                      />\r\n                    </div>\r\n                  )}\r\n                </Form.Group>\r\n              )}\r\n\r\n              {inputSource === 'camera' && (\r\n                <div className=\"text-center mt-3\">\r\n                  <Button \r\n                    variant={cameraActive ? \"danger\" : \"info\"} \r\n                    onClick={toggleCamera}\r\n                    className=\"mb-2\"\r\n                  >\r\n                    {cameraActive ? 'Stop Camera' : 'Start Camera'}\r\n                  </Button>\r\n                  \r\n                  {cameraActive && (\r\n                    <>\r\n                      <div className=\"webcam-container mb-3\">\r\n                        <Webcam\r\n                          audio={false}\r\n                          ref={webcamRef}\r\n                          screenshotFormat=\"image/jpeg\"\r\n                          width=\"100%\"\r\n                          height=\"auto\"\r\n                          videoConstraints={{\r\n                            width: 640,\r\n                            height: 480,\r\n                            facingMode: cameraOrientation\r\n                          }}\r\n                        />\r\n                        {isMobile && (\r\n                          <Button \r\n                            variant=\"outline-secondary\" \r\n                            onClick={toggleCameraOrientation}\r\n                            className=\"mt-2 mb-2\"\r\n                            size=\"sm\"\r\n                          >\r\n                            Rotate Camera\r\n                          </Button>\r\n                        )}\r\n                      </div>\r\n                      <Button \r\n                        variant=\"success\" \r\n                        onClick={handleCapture}\r\n                      >\r\n                        Capture Photo\r\n                      </Button>\r\n                    </>\r\n                  )}\r\n                  \r\n                  {imagePreview && (\r\n                    <div className=\"mt-3\">\r\n                      <img \r\n                        src={imagePreview} \r\n                        alt=\"Captured\" \r\n                        style={{ maxWidth: '100%', maxHeight: '300px' }} \r\n                      />\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {error && <Alert variant=\"danger\">{error}</Alert>}\r\n              <div className=\"d-flex gap-2 mt-3\">\r\n                <Button \r\n                  variant=\"primary\" \r\n                  onClick={handleDetect}\r\n                  disabled={loading || \r\n                    !hasMediaForDetection() ||\r\n                    selectedClasses.length === 0 ||\r\n                    isProcessing}\r\n                >\r\n                  {loading ? (\r\n                    <>\r\n                      <Spinner\r\n                        as=\"span\"\r\n                        animation=\"border\"\r\n                        size=\"sm\"\r\n                        role=\"status\"\r\n                        aria-hidden=\"true\"\r\n                      />\r\n                      <span className=\"ms-2\">Processing...</span>\r\n                    </>\r\n                  ) : (\r\n                    \"Detect Infrastructure\"\r\n                  )}\r\n                </Button>\r\n              \r\n                <Button \r\n                  variant=\"secondary\" \r\n                  onClick={isProcessing ? handleStopProcessing : handleReset}\r\n                  disabled={loading && !isProcessing}\r\n                >\r\n                  {isProcessing ? \"Stop Processing\" : \"Reset\"}\r\n                </Button>\r\n              </div>\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n        \r\n        <Col md={6}>\r\n          {processedImage ? (\r\n            <Card className=\"mb-4 shadow-sm\">\r\n              <Card.Header className=\"bg-success text-white\">\r\n                <h5 className=\"mb-0\">Detection Results</h5>\r\n              </Card.Header>\r\n              <Card.Body>\r\n                <div className=\"processed-image-container mb-3\">\r\n                  {isBuffering && (\r\n                    <div className=\"processing-overlay\">\r\n                      <Spinner animation=\"border\" role=\"status\">\r\n                        <span className=\"visually-hidden\">Buffering video...</span>\r\n                      </Spinner>\r\n                      <span style={{ color: 'white', marginLeft: 10 }}>Buffering video...</span>\r\n                    </div>\r\n                  )}\r\n                  <img\r\n                    src={processedImage ? (processedImage.startsWith('data:') ? processedImage : `data:image/jpeg;base64,${processedImage}`) : ''}\r\n                    alt=\"Processed\"\r\n                    style={{ maxWidth: '100%' }}\r\n                    onError={(e) => { e.target.onerror = null; e.target.src = ''; setError('Failed to display processed frame.'); }}\r\n                  />\r\n                  {isProcessing && !isBuffering && (\r\n                    <div className=\"processing-overlay\">\r\n                      <Spinner animation=\"border\" role=\"status\">\r\n                        <span className=\"visually-hidden\">Processing...</span>\r\n                      </Spinner>\r\n                    </div>\r\n                  )}\r\n                  {/* Playback controls */}\r\n                  {frameBuffer.length > 0 && !isBuffering && (\r\n                    <div style={{ display: 'flex', alignItems: 'center', marginTop: 10, gap: 10 }}>\r\n                      <button onClick={handleRewind} disabled={currentFrameIndex === 0}>⏪</button>\r\n                      <button onClick={handlePlayPause}>{isPlaying ? '⏸️ Pause' : '▶️ Play'}</button>\r\n                      <button onClick={handleForward} disabled={currentFrameIndex >= frameBuffer.length - 1}>⏩</button>\r\n                      <input\r\n                        type=\"range\"\r\n                        min={0}\r\n                        max={frameBuffer.length - 1}\r\n                        value={currentFrameIndex}\r\n                        onChange={handleSliderChange}\r\n                        style={{ flex: 1 }}\r\n                      />\r\n                      <span style={{ minWidth: 60 }}>{currentFrameIndex + 1} / {frameBuffer.length}</span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n                \r\n                {detectionResults && detectionResults.detections && (\r\n                  <>\r\n                    <h5>Detection Summary</h5>\r\n                    <div className=\"table-responsive\">\r\n                      <table className=\"table table-striped\">\r\n                        <thead>\r\n                          <tr>\r\n                            <th>Infrastructure Type</th>\r\n                            <th>Count</th>\r\n                          </tr>\r\n                        </thead>\r\n                        <tbody>\r\n                          {Object.entries(getClassCounts()).map(([cls, count]) => (\r\n                            <tr key={cls}>\r\n                              <td>{cls}</td>\r\n                              <td>{count}</td>\r\n                            </tr>\r\n                          ))}\r\n                        </tbody>\r\n                      </table>\r\n                    </div>\r\n                    \r\n                    {detectionResults.continuous_lengths && Object.keys(detectionResults.continuous_lengths).length > 0 && (\r\n                      <>\r\n                        <h5>Cumulative Lengths (Continuous Classes)</h5>\r\n                        <div className=\"table-responsive\">\r\n                          <table className=\"table table-striped\">\r\n                            <thead>\r\n                              <tr>\r\n                                <th>Infrastructure Type</th>\r\n                                <th>Cumulative Length (km)</th>\r\n                              </tr>\r\n                            </thead>\r\n                            <tbody>\r\n                              {Object.entries(detectionResults.continuous_lengths).map(([cls, length]) => (\r\n                                <tr key={cls}>\r\n                                  <td>{cls}</td>\r\n                                  <td>{length}</td>\r\n                                </tr>\r\n                              ))}\r\n                            </tbody>\r\n                          </table>\r\n                        </div>\r\n                      </>\r\n                    )}\r\n                  </>\r\n                )}\r\n                {/* Live Discrete Table */}\r\n                {liveDistinctTable.length > 0 && (\r\n                  <div style={{ marginTop: 20 }}>\r\n                    <h5>Live Discrete Detections</h5>\r\n                    <div className=\"table-responsive\" style={{ maxHeight: 200, overflowY: 'auto' }}>\r\n                      <table className=\"table table-striped table-sm\">\r\n                        <thead>\r\n                          <tr>\r\n                            <th>ID</th>\r\n                            <th>Class</th>\r\n                            <th>GPS</th>\r\n                            <th>Frame</th>\r\n                            <th>Second</th>\r\n                          </tr>\r\n                        </thead>\r\n                        <tbody>\r\n                          {liveDistinctTable.map(row => (\r\n                            <tr key={row.ID}>\r\n                              <td>{row.ID}</td>\r\n                              <td>{row.Class}</td>\r\n                              <td>{row.GPS ? `${row.GPS[0].toFixed(6)}, ${row.GPS[1].toFixed(6)}` : '-'}</td>\r\n                              <td>{row.Frame}</td>\r\n                              <td>{row.Second}</td>\r\n                            </tr>\r\n                          ))}\r\n                        </tbody>\r\n                      </table>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n                {/* Live Continuous Table */}\r\n                {liveContinuousTable.length > 0 && (\r\n                  <div style={{ marginTop: 20 }}>\r\n                    <h5>Live Continuous (Cumulative) Data</h5>\r\n                    <div className=\"table-responsive\" style={{ maxHeight: 150, overflowY: 'auto' }}>\r\n                      <table className=\"table table-striped table-sm\">\r\n                        <thead>\r\n                          <tr>\r\n                            <th>Class</th>\r\n                            <th>Cumulative Length (km)</th>\r\n                          </tr>\r\n                        </thead>\r\n                        <tbody>\r\n                          {liveContinuousTable.map(row => (\r\n                            <tr key={row.Class}>\r\n                              <td>{row.Class}</td>\r\n                              <td>{row['Cumulative Length (km)']}</td>\r\n                            </tr>\r\n                          ))}\r\n                        </tbody>\r\n                      </table>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </Card.Body>\r\n            </Card>\r\n          ) : (\r\n            <Card className=\"mb-4 shadow-sm\">\r\n              <Card.Header className=\"bg-info text-white\">\r\n                <h5 className=\"mb-0\">Instructions</h5>\r\n              </Card.Header>\r\n              <Card.Body>\r\n                <ol>\r\n                  <li>Select one or more infrastructure types to detect</li>\r\n                  <li>Choose your input source (video, image, or camera)</li>\r\n                  <li>Upload media or capture a photo</li>\r\n                  <li>Click \"Detect Infrastructure\" to analyze</li>\r\n                </ol>\r\n                <p>Detection will identify and highlight road infrastructure features such as:</p>\r\n                <ul>\r\n                  <li>Pavement markings</li>\r\n                  <li>Road signs</li>\r\n                  <li>Safety barriers</li>\r\n                  <li>Road edge lines</li>\r\n                  <li>Lane markings</li>\r\n                </ul>\r\n              </Card.Body>\r\n            </Card>\r\n          )}\r\n        </Col>\r\n      </Row>\r\n\r\n      <Tabs\r\n        activeKey={activeTab}\r\n        onSelect={(k) => setActiveTab(k)}\r\n        className=\"mt-4\"\r\n      >\r\n        <Tab eventKey=\"detection\" title=\"Detection\">\r\n          <Card className=\"shadow-sm\">\r\n            <Card.Body>\r\n              <h4>About Road Infrastructure Analysis</h4>\r\n              <p>\r\n                The Road Infrastructure Analysis module uses computer vision to detect, classify, and \r\n                analyze various road infrastructure elements. This helps in infrastructure inventory \r\n                management and maintenance planning.\r\n              </p>\r\n              \r\n              <h5>Detectable Infrastructure Types</h5>\r\n              <ul>\r\n                <li><strong>Cold Plastic Rumble Marking Paint</strong> - Textured road markings that provide tactile and auditory warnings</li>\r\n                <li><strong>Raised Pavement Markers</strong> - Reflective or non-reflective markers installed on roadways</li>\r\n                <li><strong>Rubber Speed Breaker</strong> - Traffic calming devices to reduce vehicle speeds</li>\r\n                <li><strong>SW_Beam_Crash_Barrier</strong> - Safety barriers to prevent vehicles from veering off the road</li>\r\n                <li><strong>Water-Based Kerb Paint</strong> - Paint used for road edge visibility and demarcation</li>\r\n                <li><strong>YNM Informatory Sign Boards</strong> - Road signs providing information to road users</li>\r\n                <li><strong>HTP-edge_line</strong> - High-performance thermoplastic road edge line markings</li>\r\n                <li><strong>HTP-lane_line</strong> - High-performance thermoplastic lane separators</li>\r\n              </ul>\r\n              \r\n              <h5>How The System Works</h5>\r\n              <p>\r\n                The system uses a trained YOLOv8 object detection model to identify infrastructure elements \r\n                in images or video. For each detection, the system records:\r\n              </p>\r\n              <ul>\r\n                <li>The type of infrastructure element</li>\r\n                <li>Confidence score of the detection</li>\r\n                <li>Physical dimensions (where applicable)</li>\r\n                <li>Geolocation (when available from device GPS)</li>\r\n              </ul>\r\n              \r\n              <h5>Use Cases</h5>\r\n              <ul>\r\n                <li>Road infrastructure inventory management</li>\r\n                <li>Monitoring road marking conditions</li>\r\n                <li>Planning maintenance and replacement schedules</li>\r\n                <li>Assessing compliance with safety standards</li>\r\n                <li>Calculating infrastructure density and distribution</li>\r\n              </ul>\r\n            </Card.Body>\r\n          </Card>\r\n        </Tab>\r\n        \r\n        <Tab eventKey=\"information\" title=\"Information\">\r\n          <Card className=\"shadow-sm\">\r\n            <Card.Body>\r\n              <h4>About Road Infrastructure Analysis</h4>\r\n              <p>\r\n                The Road Infrastructure Analysis module uses computer vision to detect, classify, and \r\n                analyze various road infrastructure elements. This helps in infrastructure inventory \r\n                management and maintenance planning.\r\n              </p>\r\n              \r\n              <h5>Detectable Infrastructure Types</h5>\r\n              <ul>\r\n                <li><strong>Cold Plastic Rumble Marking Paint</strong> - Textured road markings that provide tactile and auditory warnings</li>\r\n                <li><strong>Raised Pavement Markers</strong> - Reflective or non-reflective markers installed on roadways</li>\r\n                <li><strong>Rubber Speed Breaker</strong> - Traffic calming devices to reduce vehicle speeds</li>\r\n                <li><strong>SW_Beam_Crash_Barrier</strong> - Safety barriers to prevent vehicles from veering off the road</li>\r\n                <li><strong>Water-Based Kerb Paint</strong> - Paint used for road edge visibility and demarcation</li>\r\n                <li><strong>YNM Informatory Sign Boards</strong> - Road signs providing information to road users</li>\r\n                <li><strong>HTP-edge_line</strong> - High-performance thermoplastic road edge line markings</li>\r\n                <li><strong>HTP-lane_line</strong> - High-performance thermoplastic lane separators</li>\r\n              </ul>\r\n              \r\n              <h5>How The System Works</h5>\r\n              <p>\r\n                The system uses a trained YOLOv8 object detection model to identify infrastructure elements \r\n                in images or video. For each detection, the system records:\r\n              </p>\r\n              <ul>\r\n                <li>The type of infrastructure element</li>\r\n                <li>Confidence score of the detection</li>\r\n                <li>Physical dimensions (where applicable)</li>\r\n                <li>Geolocation (when available from device GPS)</li>\r\n              </ul>\r\n              \r\n              <h5>Use Cases</h5>\r\n              <ul>\r\n                <li>Road infrastructure inventory management</li>\r\n                <li>Monitoring road marking conditions</li>\r\n                <li>Planning maintenance and replacement schedules</li>\r\n                <li>Assessing compliance with safety standards</li>\r\n                <li>Calculating infrastructure density and distribution</li>\r\n              </ul>\r\n            </Card.Body>\r\n          </Card>\r\n        </Tab>\r\n      </Tabs>\r\n    </Container>\r\n  );\r\n}\r\n\r\nexport default RoadInfrastructure;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,MAAM,CAAEC,IAAI,CAAEC,KAAK,CAAEC,OAAO,CAAEC,GAAG,CAAEC,IAAI,CAAEC,cAAc,CAAEC,OAAO,KAAQ,iBAAiB,CAC7H,OAASC,YAAY,CAAEC,SAAS,CAAEC,MAAM,CAAEC,KAAK,KAAQ,eAAe,CACtE,MAAO,0BAA0B,CACjC,MAAO,CAAAC,CAAC,KAAM,SAAS,CACvB,MAAO,CAAAC,MAAM,KAAM,cAAc,CACjC,MAAO,0BAA0B,CACjC,MAAO,CAAAC,aAAa,KAAM,wBAAwB,CAClD,OAASC,kBAAkB,CAAEC,uBAAuB,KAAQ,yBAAyB,CAErF;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACA,MAAO,CAAAV,CAAC,CAACW,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW,CAC3Cd,CAAC,CAACW,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC,CAC1BC,aAAa,CAAEC,OAAO,CAAC,wCAAwC,CAAC,CAChEC,OAAO,CAAED,OAAO,CAAC,qCAAqC,CAAC,CACvDE,SAAS,CAAEF,OAAO,CAAC,uCAAuC,CAC5D,CAAC,CAAC,CAEF,QAAS,CAAAG,kBAAkBA,CAAA,CAAG,CAC5B,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAG1C,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC2C,SAAS,CAAEC,YAAY,CAAC,CAAG5C,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAAC6C,YAAY,CAAEC,eAAe,CAAC,CAAG9C,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAAC+C,SAAS,CAAEC,YAAY,CAAC,CAAGhD,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAACiD,YAAY,CAAEC,eAAe,CAAC,CAAGlD,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACmD,cAAc,CAAEC,iBAAiB,CAAC,CAAGpD,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACqD,OAAO,CAAEC,UAAU,CAAC,CAAGtD,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACuD,KAAK,CAAEC,QAAQ,CAAC,CAAGxD,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACyD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG1D,QAAQ,CAAC,IAAI,CAAC,CAC9D,KAAM,CAAC2D,YAAY,CAAEC,eAAe,CAAC,CAAG5D,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC6D,WAAW,CAAEC,cAAc,CAAC,CAAG9D,QAAQ,CAAC,eAAe,CAAC,CAC/D,KAAM,CAAC+D,WAAW,CAAEC,cAAc,CAAC,CAAGhE,QAAQ,CAAC,OAAO,CAAC,CACvD,KAAM,CAACiE,SAAS,CAAEC,YAAY,CAAC,CAAGlE,QAAQ,CAAC,WAAW,CAAC,CACvD,KAAM,CAACmE,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGpE,QAAQ,CAAC,aAAa,CAAC,CACzE,KAAM,CAACqE,YAAY,CAAEC,eAAe,CAAC,CAAGtE,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACuE,UAAU,CAAEC,aAAa,CAAC,CAAGxE,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACyE,WAAW,CAAEC,cAAc,CAAC,CAAG1E,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAAC2E,SAAS,CAAEC,YAAY,CAAC,CAAG5E,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC6E,WAAW,CAAEC,cAAc,CAAC,CAAG9E,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC+E,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGhF,QAAQ,CAAC,CAAC,CAAC,CAC7D,KAAM,CAAAiF,WAAW,CAAG,EAAE,CAAE;AACxB,KAAM,CAAAC,YAAY,CAAG,EAAE,CAAE;AACzB,KAAM,CAACC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGpF,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACqF,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGtF,QAAQ,CAAC,EAAE,CAAC,CAClE,KAAM,CAACuF,UAAU,CAAEC,aAAa,CAAC,CAAGxF,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAAAyF,SAAS,CAAGxF,MAAM,CAAC,IAAI,CAAC,CAC9B,KAAM,CAAAyF,YAAY,CAAGzF,MAAM,CAAC,IAAI,CAAC,CACjC,KAAM,CAAE0F,QAAS,CAAC,CAAGrE,aAAa,CAAC,CAAC,CACpC,KAAM,CAAAsE,cAAc,CAAG3F,MAAM,CAAC,IAAI,CAAC,CAEnC;AACA,KAAM,CAAA4F,eAAe,cACnBjE,KAAA,CAACb,OAAO,EAAC+E,EAAE,CAAC,kBAAkB,CAACC,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAAC,QAAA,eAC1DvE,IAAA,CAACX,OAAO,CAACmF,MAAM,EAACC,EAAE,CAAC,IAAI,CAAAF,QAAA,CAAC,sCAA0B,CAAgB,CAAC,cACnErE,KAAA,CAACb,OAAO,CAACqF,IAAI,EAAAH,QAAA,eACXvE,IAAA,MAAGqE,KAAK,CAAE,CAAEM,YAAY,CAAE,MAAO,CAAE,CAAAJ,QAAA,CAAC,yCAEpC,CAAG,CAAC,cACJrE,KAAA,OAAImE,KAAK,CAAE,CAAEM,YAAY,CAAE,GAAG,CAAEC,WAAW,CAAE,MAAO,CAAE,CAAAL,QAAA,eACpDvE,IAAA,OAAAuE,QAAA,CAAI,sCAAoC,CAAI,CAAC,cAC7CvE,IAAA,OAAAuE,QAAA,CAAI,oBAAkB,CAAI,CAAC,cAC3BvE,IAAA,OAAAuE,QAAA,CAAI,oCAAkC,CAAI,CAAC,cAC3CvE,IAAA,OAAAuE,QAAA,CAAI,qDAAmD,CAAI,CAAC,EAC1D,CAAC,EACO,CAAC,EACR,CACV,CAED;AACA,KAAM,CAAAM,gBAAgB,CAAG,CACvB,oCAAoC,CACpC,wBAAwB,CACxB,mCAAmC,CACnC,oCAAoC,CACpC,sBAAsB,CACtB,6BAA6B,CAC7B,mCAAmC,CACnC,yBAAyB,CAC1B,CAED;AACArG,SAAS,CAAC,IAAM,CACd,GAAIsG,SAAS,CAACC,WAAW,CAAE,CACzBD,SAAS,CAACC,WAAW,CAACC,kBAAkB,CACrCC,QAAQ,EAAK,CACZ,KAAM,CAAEC,QAAQ,CAAEC,SAAU,CAAC,CAAGF,QAAQ,CAACG,MAAM,CAC/ChD,cAAc,CAAC,GAAG8C,QAAQ,KAAKC,SAAS,EAAE,CAAC,CAC7C,CAAC,CACAE,GAAG,EAAK,CACPC,OAAO,CAACzD,KAAK,CAAC,yBAAyB,CAAEwD,GAAG,CAAC,CAC/C,CACF,CAAC,CACH,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACA7G,SAAS,CAAC,IAAM,CACd;AACAgD,eAAe,CAAC,IAAI,CAAC,CACrBJ,eAAe,CAAC,IAAI,CAAC,CACrBM,iBAAiB,CAAC,IAAI,CAAC,CACvBM,mBAAmB,CAAC,IAAI,CAAC,CACzBF,QAAQ,CAAC,EAAE,CAAC,CAEZ,GAAIO,WAAW,GAAK,QAAQ,CAAE,CAC5BH,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,IAAM,CACLA,eAAe,CAAC,KAAK,CAAC,CACxB,CAEA,GAAI8B,YAAY,CAACuB,OAAO,CAAE,CACxBvB,YAAY,CAACuB,OAAO,CAACC,KAAK,CAAG,EAAE,CACjC,CACF,CAAC,CAAE,CAACnD,WAAW,CAAC,CAAC,CAEjB;AACA,KAAM,CAAAoD,iBAAiB,CAAIC,CAAC,EAAK,CAC/B,KAAM,CAAAC,IAAI,CAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAC9B,GAAIF,IAAI,CAAE,CACR;AACA,KAAM,CAAAG,UAAU,CAAGjG,kBAAkB,CAAC8F,IAAI,CAAE,OAAO,CAAE,+BAA+B,CAAC,CACrF,GAAI,CAACG,UAAU,CAACC,OAAO,CAAE,CACvBjG,uBAAuB,CAACgG,UAAU,CAACE,YAAY,CAAElE,QAAQ,CAAC,CAC1D;AACA,GAAI4D,CAAC,CAACE,MAAM,CAAE,CACZF,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAG,EAAE,CACrB,CACA,OACF,CAEA;AACA1D,QAAQ,CAAC,EAAE,CAAC,CAEZZ,YAAY,CAACyE,IAAI,CAAC,CAClBvE,eAAe,CAAC6E,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC,CAAC,CAC1CjE,iBAAiB,CAAC,IAAI,CAAC,CACvBM,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CACF,CAAC,CAED;AACA,KAAM,CAAAmE,iBAAiB,CAAIT,CAAC,EAAK,CAC/B,KAAM,CAAAC,IAAI,CAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAC9B,GAAIF,IAAI,CAAE,CACR;AACA,KAAM,CAAAG,UAAU,CAAGjG,kBAAkB,CAAC8F,IAAI,CAAE,OAAO,CAAE,+BAA+B,CAAC,CACrF,GAAI,CAACG,UAAU,CAACC,OAAO,CAAE,CACvBjG,uBAAuB,CAACgG,UAAU,CAACE,YAAY,CAAElE,QAAQ,CAAC,CAC1D;AACA,GAAI4D,CAAC,CAACE,MAAM,CAAE,CACZF,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAG,EAAE,CACrB,CACA,OACF,CAEA;AACA1D,QAAQ,CAAC,EAAE,CAAC,CAEZR,YAAY,CAACqE,IAAI,CAAC,CAElB;AACA,KAAM,CAAAS,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,SAAS,CAAG,IAAM,CACvB9E,eAAe,CAAC4E,MAAM,CAACG,MAAM,CAAC,CAChC,CAAC,CACDH,MAAM,CAACI,aAAa,CAACb,IAAI,CAAC,CAE1B;AACAjE,iBAAiB,CAAC,IAAI,CAAC,CACvBM,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CACF,CAAC,CAED;AACA,KAAM,CAAAyE,aAAa,CAAGA,CAAA,GAAM,CAC1B,GAAI1C,SAAS,CAACwB,OAAO,CAAE,CACrB,KAAM,CAAAmB,QAAQ,CAAG3C,SAAS,CAACwB,OAAO,CAACoB,aAAa,CAAC,CAAC,CAClD,GAAID,QAAQ,CAAE,CACZlF,eAAe,CAACkF,QAAQ,CAAC,CACzBpF,YAAY,CAAC,IAAI,CAAC,CAClBI,iBAAiB,CAAC,IAAI,CAAC,CACvBM,mBAAmB,CAAC,IAAI,CAAC,CACzBF,QAAQ,CAAC,EAAE,CAAC,CACd,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAA8E,YAAY,CAAGA,CAAA,GAAM,CACzB1E,eAAe,CAAC,CAACD,YAAY,CAAC,CAC9B,GAAI,CAACA,YAAY,CAAE,CACjB;AACA,GAAI6C,SAAS,CAACC,WAAW,CAAE,CACzBD,SAAS,CAACC,WAAW,CAACC,kBAAkB,CACrCC,QAAQ,EAAK,CACZ,KAAM,CAAEC,QAAQ,CAAEC,SAAU,CAAC,CAAGF,QAAQ,CAACG,MAAM,CAC/ChD,cAAc,CAAC,GAAG8C,QAAQ,KAAKC,SAAS,EAAE,CAAC,CAC7C,CAAC,CACAE,GAAG,EAAK,CACPC,OAAO,CAACzD,KAAK,CAAC,yBAAyB,CAAEwD,GAAG,CAAC,CAC/C,CACF,CAAC,CACH,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAAwB,uBAAuB,CAAGA,CAAA,GAAM,CACpCnE,oBAAoB,CAACoE,IAAI,EAAIA,IAAI,GAAK,aAAa,CAAG,MAAM,CAAG,aAAa,CAAC,CAC/E,CAAC,CAED;AACA,KAAM,CAAAC,oBAAoB,CAAGA,CAAA,GAAM,CACjC,GAAI1E,WAAW,GAAK,OAAO,CAAE,MAAO,CAAC,CAAClB,YAAY,CAClD,GAAIkB,WAAW,GAAK,OAAO,EAAIA,WAAW,GAAK,QAAQ,CAAE,MAAO,CAAC,CAACd,YAAY,CAC9E,MAAO,MAAK,CACd,CAAC,CAED;AACA,KAAM,CAAAyF,WAAW,CAAGA,CAAA,GAAM,CACxBlE,aAAa,CAAC,IAAI,CAAC,CACnB5B,YAAY,CAAC,IAAI,CAAC,CAClBE,eAAe,CAAC,IAAI,CAAC,CACrBE,YAAY,CAAC,IAAI,CAAC,CAClBE,eAAe,CAAC,IAAI,CAAC,CACrBE,iBAAiB,CAAC,IAAI,CAAC,CACvBM,mBAAmB,CAAC,IAAI,CAAC,CACzBF,QAAQ,CAAC,EAAE,CAAC,CACZc,eAAe,CAAC,KAAK,CAAC,CACtB,GAAIoB,YAAY,CAACuB,OAAO,CAAE,CACxBvB,YAAY,CAACuB,OAAO,CAACC,KAAK,CAAG,EAAE,CACjC,CACF,CAAC,CAED;AACA,KAAM,CAAAyB,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CAACF,oBAAoB,CAAC,CAAC,EAAIhG,eAAe,CAACmG,MAAM,GAAK,CAAC,CAAE,CAC3DpF,QAAQ,CAAC,4DAA4D,CAAC,CACtE,OACF,CAEAwD,OAAO,CAAC6B,GAAG,CAAC,+BAA+B,CAAC,CAC5C7B,OAAO,CAAC6B,GAAG,CAAC,mBAAmB,CAAEpG,eAAe,CAAC,CACjDuE,OAAO,CAAC6B,GAAG,CAAC,eAAe,CAAE9E,WAAW,CAAC,CACzCiD,OAAO,CAAC6B,GAAG,CAAC,cAAc,CAAEhF,WAAW,CAAC,CAExCP,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CACZJ,iBAAiB,CAAC,IAAI,CAAC,CAEvB,GAAI,CACF,KAAM,CAAA0F,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAE,YAAY,CAAC,CACrCF,QAAQ,CAACE,MAAM,CAAC,iBAAiB,CAAEC,IAAI,CAACC,SAAS,CAACzG,eAAe,CAAC,CAAC,CACnEqG,QAAQ,CAACE,MAAM,CAAC,aAAa,CAAEnF,WAAW,CAAC,CAE3C;AACA,GAAIE,WAAW,GAAK,OAAO,EAAIpB,SAAS,CAAE,CACxC;AACAqE,OAAO,CAAC6B,GAAG,CAAC,wBAAwB,CAAElG,SAAS,CAACwG,IAAI,CAAC,CACrDL,QAAQ,CAACE,MAAM,CAAC,OAAO,CAAErG,SAAS,CAAC,CACnC2B,eAAe,CAAC,IAAI,CAAC,CACrBE,aAAa,CAAC,KAAK,CAAC,CACpBE,cAAc,CAAC,IAAI,CAAC,CACpBE,YAAY,CAAC,KAAK,CAAC,CACnBE,cAAc,CAAC,EAAE,CAAC,CAClBE,oBAAoB,CAAC,CAAC,CAAC,CACzB,CAAC,IAAM,IAAI,CAACjB,WAAW,GAAK,OAAO,EAAIA,WAAW,GAAK,QAAQ,GAAKd,YAAY,CAAE,CAChF;AACA+D,OAAO,CAAC6B,GAAG,CAAC,+BAA+B,CAAC,CAC5C,KAAM,CAAAO,IAAI,CAAG,KAAM,CAAC,KAAM,CAAAC,KAAK,CAACpG,YAAY,CAAC,EAAEmG,IAAI,CAAC,CAAC,CACrDN,QAAQ,CAACE,MAAM,CAAC,OAAO,CAAEI,IAAI,CAAE,aAAa,CAAC,CAC/C,CAEApC,OAAO,CAAC6B,GAAG,CAAC,+BAA+B,CAAC,CAE5C;AACA,KAAM,CAAAS,cAAc,CAAG,KAAM,CAAAnJ,KAAK,CAACoJ,IAAI,CAAC,iCAAiC,CAAET,QAAQ,CAAE,CACnFU,OAAO,CAAE,CACP,cAAc,CAAE,qBAClB,CACF,CAAC,CAAC,CAEFxC,OAAO,CAAC6B,GAAG,CAAC,kBAAkB,CAAES,cAAc,CAACG,IAAI,CAAC,CAEpD,GAAI,CAACH,cAAc,CAACG,IAAI,CAACC,OAAO,CAAE,CAChC,KAAM,IAAI,CAAAC,KAAK,CAACL,cAAc,CAACG,IAAI,CAACG,OAAO,CAAC,CAC9C,CAEA;AACA,GAAI7F,WAAW,GAAK,OAAO,CAAE,CAC3B;AACA,KAAM,CAAA8F,WAAW,CAAG,GAAI,CAAAC,WAAW,CAAC,iCAAiC,CAAC,CACtElE,cAAc,CAACqB,OAAO,CAAG4C,WAAW,CAEpCA,WAAW,CAACE,SAAS,CAAIC,KAAK,EAAK,CACjC,GAAIpE,cAAc,CAACqB,OAAO,GAAK,IAAI,CAAE,OAAQ;AAC7C,KAAM,CAAAwC,IAAI,CAAGR,IAAI,CAACgB,KAAK,CAACD,KAAK,CAACP,IAAI,CAAC,CACnCzC,OAAO,CAAC6B,GAAG,CAAC,sBAAsB,CAAEY,IAAI,CAAC,CAEzC,GAAIA,IAAI,CAACC,OAAO,GAAK,KAAK,CAAE,CAC1BlG,QAAQ,CAACiG,IAAI,CAACG,OAAO,EAAI,kBAAkB,CAAC,CAC5CC,WAAW,CAACK,KAAK,CAAC,CAAC,CACnBtE,cAAc,CAACqB,OAAO,CAAG,IAAI,CAC7B3C,eAAe,CAAC,KAAK,CAAC,CACtBhB,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA,GAAImG,IAAI,CAACU,KAAK,EAAI,MAAO,CAAAV,IAAI,CAACU,KAAK,GAAK,QAAQ,EAAIV,IAAI,CAACU,KAAK,CAACvB,MAAM,CAAG,IAAI,CAAE,CAC5E9D,cAAc,CAAC0D,IAAI,EAAI,CACrB,KAAM,CAAA4B,SAAS,CAAG,CAAC,GAAG5B,IAAI,CAAEiB,IAAI,CAACU,KAAK,CAAC,CACvC,GAAIC,SAAS,CAACxB,MAAM,EAAI3D,WAAW,EAAI,CAACN,SAAS,CAAE,CACjDD,cAAc,CAAC,KAAK,CAAC,CACrBE,YAAY,CAAC,IAAI,CAAC,CACpB,CACA,MAAO,CAAAwF,SAAS,CAClB,CAAC,CAAC,CACJ,CAAC,IAAM,IAAIX,IAAI,CAACU,KAAK,EAAIV,IAAI,CAACU,KAAK,CAACvB,MAAM,EAAI,IAAI,CAAE,CAClD5B,OAAO,CAACqD,IAAI,CAAC,8DAA8D,CAAC,CAC9E,CAEA;AACA,GAAIZ,IAAI,CAACa,UAAU,CAAE,CACnB5G,mBAAmB,CAAC8E,IAAI,GAAK,CAC3B,GAAGA,IAAI,CACP+B,YAAY,CAAEd,IAAI,CAACc,YAAY,CAC/BC,gBAAgB,CAAEf,IAAI,CAACgB,WAAW,CAClCH,UAAU,CAAE,CAAC,IAAI,CAAA9B,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE8B,UAAU,GAAI,EAAE,CAAC,CAAE,GAAGb,IAAI,CAACa,UAAU,CAAC,CAC7DI,kBAAkB,CAAEjB,IAAI,CAACiB,kBAAkB,CAC3CC,WAAW,CAAElB,IAAI,CAACkB,WACpB,CAAC,CAAC,CAAC,CACL,CAEA;AACA,GAAIlB,IAAI,CAACmB,mBAAmB,CAAExF,oBAAoB,CAACqE,IAAI,CAACmB,mBAAmB,CAAC,CAC5E,GAAInB,IAAI,CAACoB,qBAAqB,CAAEvF,sBAAsB,CAACmE,IAAI,CAACoB,qBAAqB,CAAC,CAElF,GAAIpB,IAAI,CAACqB,WAAW,EAAIC,KAAK,CAACC,OAAO,CAACvB,IAAI,CAACqB,WAAW,CAAC,CAAE,CACvDtF,aAAa,CAACiE,IAAI,CAACqB,WAAW,CAAC,CACjC,CAEA;AACA,GAAIrB,IAAI,CAACwB,eAAe,CAAE,CACxBpB,WAAW,CAACK,KAAK,CAAC,CAAC,CACnBtE,cAAc,CAACqB,OAAO,CAAG,IAAI,CAC7B3C,eAAe,CAAC,KAAK,CAAC,CACtBhB,UAAU,CAAC,KAAK,CAAC,CACnB,CAEA,GAAImG,IAAI,CAACyB,aAAa,GAAKC,SAAS,CAAE,CACpCnE,OAAO,CAAC6B,GAAG,CAAC,mBAAmB,CAAEY,IAAI,CAACyB,aAAa,CAAG,eAAe,CAAG,WAAW,CAAC,CACpF5G,eAAe,CAAC,KAAK,CAAC,CACtBI,cAAc,CAAC,KAAK,CAAC,CACrBpB,UAAU,CAAC,KAAK,CAAC,CACjB,GAAImG,IAAI,CAACkB,WAAW,CAAE,CACpB;AAAA,CAEJ,CACF,CAAC,CAEDd,WAAW,CAACuB,OAAO,CAAI7H,KAAK,EAAK,CAC/ByD,OAAO,CAACzD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1CsG,WAAW,CAACK,KAAK,CAAC,CAAC,CACnBtE,cAAc,CAACqB,OAAO,CAAG,IAAI,CAC7B3C,eAAe,CAAC,KAAK,CAAC,CACtBhB,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED;AACA,GAAIiB,UAAU,CAAE,CACdsF,WAAW,CAACK,KAAK,CAAC,CAAC,CACnBtE,cAAc,CAACqB,OAAO,CAAG,IAAI,CAC7B3C,eAAe,CAAC,KAAK,CAAC,CACtBhB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,IAAM,IAAIS,WAAW,GAAK,OAAO,EAAIA,WAAW,GAAK,QAAQ,CAAE,CAC9D;AACAiD,OAAO,CAAC6B,GAAG,CAAC,4BAA4B,CAAES,cAAc,CAACG,IAAI,CAAC,CAE9D;AACA,GAAIH,cAAc,CAACG,IAAI,CAACU,KAAK,CAAE,CAC7B/G,iBAAiB,CAACkG,cAAc,CAACG,IAAI,CAACU,KAAK,CAAC,CAC9C,CAEA;AACA,GAAIb,cAAc,CAACG,IAAI,CAACa,UAAU,CAAE,CAClC5G,mBAAmB,CAAC,CAClB4G,UAAU,CAAEhB,cAAc,CAACG,IAAI,CAACa,UAAU,CAC1CC,YAAY,CAAE,CAAC,CACfC,gBAAgB,CAAE,CACpB,CAAC,CAAC,CACJ,CAEAlH,UAAU,CAAC,KAAK,CAAC,CACnB,CAEF,CAAE,MAAOC,KAAK,CAAE,KAAA8H,eAAA,CAAAC,oBAAA,CACdtE,OAAO,CAACzD,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAAC,CACxCC,QAAQ,CACN,EAAA6H,eAAA,CAAA9H,KAAK,CAACgI,QAAQ,UAAAF,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgB5B,IAAI,UAAA6B,oBAAA,iBAApBA,oBAAA,CAAsB1B,OAAO,GAC7BrG,KAAK,CAACqG,OAAO,EACb,uDACF,CAAC,CACDtG,UAAU,CAAC,KAAK,CAAC,CACjBgB,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED;AACApE,SAAS,CAAC,IAAM,CACd,GAAI,CAAAsL,gBAAgB,CACpB,GAAI7G,SAAS,EAAIE,WAAW,CAAC+D,MAAM,CAAG,CAAC,CAAE,CACvC4C,gBAAgB,CAAGC,WAAW,CAAC,IAAM,CACnCzG,oBAAoB,CAACwD,IAAI,EAAI,CAC3B,GAAIA,IAAI,CAAG3D,WAAW,CAAC+D,MAAM,CAAG,CAAC,CAAE,CACjC,MAAO,CAAAJ,IAAI,CAAG,CAAC,CACjB,CAAC,IAAM,CACL5D,YAAY,CAAC,KAAK,CAAC,CAAE;AACrB,MAAO,CAAA4D,IAAI,CACb,CACF,CAAC,CAAC,CACJ,CAAC,CAAE,IAAI,CAAGtD,YAAY,CAAC,CACzB,CACA,MAAO,IAAM,CACX,GAAIsG,gBAAgB,CAAEE,aAAa,CAACF,gBAAgB,CAAC,CACvD,CAAC,CACH,CAAC,CAAE,CAAC7G,SAAS,CAAEE,WAAW,CAAC,CAAC,CAE5B;AACA3E,SAAS,CAAC,IAAM,CACd,GAAI2E,WAAW,CAAC+D,MAAM,CAAG,CAAC,EAAI7D,iBAAiB,CAAGF,WAAW,CAAC+D,MAAM,CAAE,CACpExF,iBAAiB,CAACyB,WAAW,CAACE,iBAAiB,CAAC,CAAC,CACnD,CACF,CAAC,CAAE,CAACA,iBAAiB,CAAEF,WAAW,CAAC,CAAC,CAEpC;AACA,KAAM,CAAA8G,eAAe,CAAGA,CAAA,GAAM/G,YAAY,CAACgH,CAAC,EAAI,CAACA,CAAC,CAAC,CACnD,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM7G,oBAAoB,CAAC8G,CAAC,EAAIC,IAAI,CAACC,GAAG,CAACF,CAAC,CAAG,CAAC,CAAE,CAAC,CAAC,CAAC,CACxE,KAAM,CAAAG,aAAa,CAAGA,CAAA,GAAMjH,oBAAoB,CAAC8G,CAAC,EAAIC,IAAI,CAACG,GAAG,CAACJ,CAAC,CAAG,CAAC,CAAEjH,WAAW,CAAC+D,MAAM,CAAG,CAAC,CAAC,CAAC,CAC9F,KAAM,CAAAuD,kBAAkB,CAAI/E,CAAC,EAAKpC,oBAAoB,CAACoH,MAAM,CAAChF,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAC,CAAC,CAE9E;AACA,KAAM,CAAAmF,cAAc,CAAGA,CAAA,GAAM,CAC3B,GAAI,CAAC5I,gBAAgB,EAAI,CAACA,gBAAgB,CAAC6G,UAAU,CAAE,MAAO,CAAC,CAAC,CAEhE,MAAO,CAAA7G,gBAAgB,CAAC6G,UAAU,CAACgC,MAAM,CAAC,CAACC,GAAG,CAAEC,GAAG,GAAK,CACtDD,GAAG,CAACC,GAAG,CAACC,KAAK,CAAC,CAAG,CAACF,GAAG,CAACC,GAAG,CAACC,KAAK,CAAC,EAAI,CAAC,EAAI,CAAC,CAC1C,MAAO,CAAAF,GAAG,CACZ,CAAC,CAAE,CAAC,CAAC,CAAC,CACR,CAAC,CAED;AACA,KAAM,CAAAG,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CACF;AACA,GAAI9G,cAAc,CAACqB,OAAO,CAAE,CAC1BrB,cAAc,CAACqB,OAAO,CAACiD,KAAK,CAAC,CAAC,CAC9BtE,cAAc,CAACqB,OAAO,CAAG,IAAI,CAC/B,CAEA;AACA,KAAM,CAAAsE,QAAQ,CAAG,KAAM,CAAApL,KAAK,CAACoJ,IAAI,CAAC,0CAA0C,CAAC,CAC7EvC,OAAO,CAAC6B,GAAG,CAAC,2BAA2B,CAAE0C,QAAQ,CAAC9B,IAAI,CAAC,CAEvD;AACAnF,eAAe,CAAC,KAAK,CAAC,CACtBE,aAAa,CAAC,IAAI,CAAC,CACnBE,cAAc,CAAC,KAAK,CAAC,CACrBE,YAAY,CAAC,KAAK,CAAC,CACnBtB,UAAU,CAAC,KAAK,CAAC,CAEjB;AACA,GAAIuB,WAAW,CAAC+D,MAAM,CAAG,CAAC,CAAE,CAC1BxF,iBAAiB,CAACyB,WAAW,CAACA,WAAW,CAAC+D,MAAM,CAAG,CAAC,CAAC,CAAC,CACxD,CAEA,MAAO,KAAI,CACb,CAAE,MAAOrF,KAAK,CAAE,CACdyD,OAAO,CAACzD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClDC,QAAQ,CAAC,6BAA6B,CAAGD,KAAK,CAACqG,OAAO,CAAC,CACvDtG,UAAU,CAAC,KAAK,CAAC,CACjB,MAAO,MAAK,CACd,CACF,CAAC,CAED;AACA,KAAM,CAAAqJ,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAItI,YAAY,CAAE,CAChB,KAAM,CAAAuI,OAAO,CAAG,KAAM,CAAAF,cAAc,CAAC,CAAC,CACtCpJ,UAAU,CAAC,KAAK,CAAC,CACjB,GAAIsJ,OAAO,CAAE,CACXpJ,QAAQ,CAAC,2CAA2C,CAAC,CACvD,CACF,CACF,CAAC,CAED,mBACE5B,KAAA,CAACxB,SAAS,EAACyM,KAAK,MAACC,SAAS,CAAC,MAAM,CAAA7G,QAAA,eAC/BrE,KAAA,CAACvB,GAAG,EAAA4F,QAAA,eACFvE,IAAA,CAACpB,GAAG,EAACyM,EAAE,CAAE,CAAE,CAAA9G,QAAA,cACTrE,KAAA,CAACrB,IAAI,EAACuM,SAAS,CAAC,gBAAgB,CAAA7G,QAAA,eAC9BvE,IAAA,CAACnB,IAAI,CAAC2F,MAAM,EAAC4G,SAAS,CAAC,4BAA4B,CAAA7G,QAAA,cACjDvE,IAAA,OAAIoL,SAAS,CAAC,MAAM,CAAA7G,QAAA,CAAC,oBAAkB,CAAI,CAAC,CACjC,CAAC,cACdrE,KAAA,CAACrB,IAAI,CAAC6F,IAAI,EAAC0G,SAAS,CAAC,MAAM,CAAA7G,QAAA,eACzBrE,KAAA,CAACnB,IAAI,CAACuM,KAAK,EAACF,SAAS,CAAC,MAAM,CAAA7G,QAAA,eAC1BvE,IAAA,CAACjB,IAAI,CAACwM,KAAK,EAACH,SAAS,CAAC,MAAM,CAAA7G,QAAA,CAAC,yCAAuC,CAAY,CAAC,cACjFvE,IAAA,CAACjB,IAAI,CAACyM,OAAO,EACX/G,EAAE,CAAC,QAAQ,CACXgH,QAAQ,MACRjG,KAAK,CAAEzE,eAAgB,CACvB2K,QAAQ,CAAGhG,CAAC,EAAK1E,kBAAkB,CAAC,CAAC,GAAG0E,CAAC,CAACE,MAAM,CAAC+F,eAAe,CAAC,CAACC,GAAG,CAACC,GAAG,EAAIA,GAAG,CAACrG,KAAK,CAAC,CAAE,CACzFnB,KAAK,CAAE,CAAEyH,MAAM,CAAE,OAAQ,CAAE,CAAAvH,QAAA,CAE1B,CAACV,UAAU,CAACqD,MAAM,CAAG,CAAC,CAAGrD,UAAU,CAAG,CACrC,oCAAoC,CACpC,wBAAwB,CACxB,mCAAmC,CACnC,oCAAoC,CACpC,sBAAsB,CACtB,6BAA6B,CAC7B,mCAAmC,CACnC,yBAAyB,CAC1B,EAAE+H,GAAG,CAAEG,GAAG,eACT/L,IAAA,WAAkBwF,KAAK,CAAEuG,GAAI,CAAAxH,QAAA,CAAEwH,GAAG,EAArBA,GAA8B,CAC5C,CAAC,CACU,CAAC,EACL,CAAC,cAEb7L,KAAA,CAACnB,IAAI,CAACuM,KAAK,EAACF,SAAS,CAAC,MAAM,CAAA7G,QAAA,eAC1BvE,IAAA,CAACjB,IAAI,CAACwM,KAAK,EAACH,SAAS,CAAC,MAAM,CAAA7G,QAAA,CAAC,cAAY,CAAY,CAAC,cACtDrE,KAAA,CAACnB,IAAI,CAACiN,MAAM,EACVxG,KAAK,CAAEnD,WAAY,CACnBqJ,QAAQ,CAAGhG,CAAC,EAAKpD,cAAc,CAACoD,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAE,CAAAjB,QAAA,eAEhDvE,IAAA,WAAQwF,KAAK,CAAC,OAAO,CAAAjB,QAAA,CAAC,OAAK,CAAQ,CAAC,cACpCvE,IAAA,WAAQwF,KAAK,CAAC,OAAO,CAAAjB,QAAA,CAAC,OAAK,CAAQ,CAAC,cACpCvE,IAAA,WAAQwF,KAAK,CAAC,QAAQ,CAAAjB,QAAA,CAAC,aAAW,CAAQ,CAAC,EAChC,CAAC,EACJ,CAAC,cAGbvE,IAAA,CAACZ,cAAc,EACb6M,OAAO,CAAC,OAAO,CACfC,SAAS,CAAC,OAAO,CACjBC,OAAO,CAAEhI,eAAgB,CACzBiI,SAAS,MAAA7H,QAAA,cAETvE,IAAA,QACEoL,SAAS,CAAC,uBAAuB,CACjC/G,KAAK,CAAE,CAAEgI,MAAM,CAAE,SAAS,CAAEC,OAAO,CAAE,cAAe,CAAE,CAAA/H,QAAA,cAEtDvE,IAAA,QACEuM,GAAG,CAAC,mBAAmB,CACvBC,GAAG,CAAC,yBAAyB,CAC7BnI,KAAK,CAAE,CAAEoI,KAAK,CAAE,MAAM,CAAEX,MAAM,CAAE,MAAO,CAAE,CAC1C,CAAC,CACC,CAAC,CACQ,CAAC,CAEhBzJ,WAAW,GAAK,OAAO,eACtBnC,KAAA,CAACnB,IAAI,CAACuM,KAAK,EAACF,SAAS,CAAC,MAAM,CAAA7G,QAAA,eAC1BvE,IAAA,CAACjB,IAAI,CAACwM,KAAK,EAAAhH,QAAA,CAAC,cAAY,CAAY,CAAC,cACrCvE,IAAA,CAACjB,IAAI,CAACyM,OAAO,EACXkB,IAAI,CAAC,MAAM,CACXC,MAAM,CAAC,SAAS,CAChBjB,QAAQ,CAAEjG,iBAAkB,CAC5BmH,GAAG,CAAE5I,YAAa,CACnB,CAAC,CACD7C,YAAY,eACXnB,IAAA,QAAKoL,SAAS,CAAC,MAAM,CAAA7G,QAAA,cACnBvE,IAAA,UACEuM,GAAG,CAAEpL,YAAa,CAClB0L,QAAQ,MACRxI,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEwI,SAAS,CAAE,OAAQ,CAAE,CACjD,CAAC,CACC,CACN,EACS,CACb,CAEAzK,WAAW,GAAK,OAAO,eACtBnC,KAAA,CAACnB,IAAI,CAACuM,KAAK,EAACF,SAAS,CAAC,MAAM,CAAA7G,QAAA,eAC1BvE,IAAA,CAACjB,IAAI,CAACwM,KAAK,EAAAhH,QAAA,CAAC,cAAY,CAAY,CAAC,cACrCvE,IAAA,CAACjB,IAAI,CAACyM,OAAO,EACXkB,IAAI,CAAC,MAAM,CACXC,MAAM,CAAC,SAAS,CAChBjB,QAAQ,CAAEvF,iBAAkB,CAC5ByG,GAAG,CAAE5I,YAAa,CACnB,CAAC,CACDzC,YAAY,eACXvB,IAAA,QAAKoL,SAAS,CAAC,MAAM,CAAA7G,QAAA,cACnBvE,IAAA,QACEuM,GAAG,CAAEhL,YAAa,CAClBiL,GAAG,CAAC,SAAS,CACbnI,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEwI,SAAS,CAAE,OAAQ,CAAE,CACjD,CAAC,CACC,CACN,EACS,CACb,CAEAzK,WAAW,GAAK,QAAQ,eACvBnC,KAAA,QAAKkL,SAAS,CAAC,kBAAkB,CAAA7G,QAAA,eAC/BvE,IAAA,CAAClB,MAAM,EACLiO,OAAO,CAAE9K,YAAY,CAAG,QAAQ,CAAG,MAAO,CAC1C+K,OAAO,CAAEpG,YAAa,CACtBwE,SAAS,CAAC,MAAM,CAAA7G,QAAA,CAEftC,YAAY,CAAG,aAAa,CAAG,cAAc,CACxC,CAAC,CAERA,YAAY,eACX/B,KAAA,CAAAE,SAAA,EAAAmE,QAAA,eACErE,KAAA,QAAKkL,SAAS,CAAC,uBAAuB,CAAA7G,QAAA,eACpCvE,IAAA,CAACL,MAAM,EACLsN,KAAK,CAAE,KAAM,CACbL,GAAG,CAAE7I,SAAU,CACfmJ,gBAAgB,CAAC,YAAY,CAC7BT,KAAK,CAAC,MAAM,CACZX,MAAM,CAAC,MAAM,CACbqB,gBAAgB,CAAE,CAChBV,KAAK,CAAE,GAAG,CACVX,MAAM,CAAE,GAAG,CACXsB,UAAU,CAAE3K,iBACd,CAAE,CACH,CAAC,CACDwB,QAAQ,eACPjE,IAAA,CAAClB,MAAM,EACLiO,OAAO,CAAC,mBAAmB,CAC3BC,OAAO,CAAEnG,uBAAwB,CACjCuE,SAAS,CAAC,WAAW,CACrBiC,IAAI,CAAC,IAAI,CAAA9I,QAAA,CACV,eAED,CAAQ,CACT,EACE,CAAC,cACNvE,IAAA,CAAClB,MAAM,EACLiO,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEvG,aAAc,CAAAlC,QAAA,CACxB,eAED,CAAQ,CAAC,EACT,CACH,CAEAhD,YAAY,eACXvB,IAAA,QAAKoL,SAAS,CAAC,MAAM,CAAA7G,QAAA,cACnBvE,IAAA,QACEuM,GAAG,CAAEhL,YAAa,CAClBiL,GAAG,CAAC,UAAU,CACdnI,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEwI,SAAS,CAAE,OAAQ,CAAE,CACjD,CAAC,CACC,CACN,EACE,CACN,CAEAjL,KAAK,eAAI7B,IAAA,CAAChB,KAAK,EAAC+N,OAAO,CAAC,QAAQ,CAAAxI,QAAA,CAAE1C,KAAK,CAAQ,CAAC,cACjD3B,KAAA,QAAKkL,SAAS,CAAC,mBAAmB,CAAA7G,QAAA,eAChCvE,IAAA,CAAClB,MAAM,EACLiO,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAE/F,YAAa,CACtBqG,QAAQ,CAAE3L,OAAO,EACf,CAACoF,oBAAoB,CAAC,CAAC,EACvBhG,eAAe,CAACmG,MAAM,GAAK,CAAC,EAC5BvE,YAAa,CAAA4B,QAAA,CAEd5C,OAAO,cACNzB,KAAA,CAAAE,SAAA,EAAAmE,QAAA,eACEvE,IAAA,CAACf,OAAO,EACNwF,EAAE,CAAC,MAAM,CACT8I,SAAS,CAAC,QAAQ,CAClBF,IAAI,CAAC,IAAI,CACTG,IAAI,CAAC,QAAQ,CACb,cAAY,MAAM,CACnB,CAAC,cACFxN,IAAA,SAAMoL,SAAS,CAAC,MAAM,CAAA7G,QAAA,CAAC,eAAa,CAAM,CAAC,EAC3C,CAAC,CAEH,uBACD,CACK,CAAC,cAETvE,IAAA,CAAClB,MAAM,EACLiO,OAAO,CAAC,WAAW,CACnBC,OAAO,CAAErK,YAAY,CAAGsI,oBAAoB,CAAGjE,WAAY,CAC3DsG,QAAQ,CAAE3L,OAAO,EAAI,CAACgB,YAAa,CAAA4B,QAAA,CAElC5B,YAAY,CAAG,iBAAiB,CAAG,OAAO,CACrC,CAAC,EACN,CAAC,EACG,CAAC,EACR,CAAC,CACJ,CAAC,cAEN3C,IAAA,CAACpB,GAAG,EAACyM,EAAE,CAAE,CAAE,CAAA9G,QAAA,CACR9C,cAAc,cACbvB,KAAA,CAACrB,IAAI,EAACuM,SAAS,CAAC,gBAAgB,CAAA7G,QAAA,eAC9BvE,IAAA,CAACnB,IAAI,CAAC2F,MAAM,EAAC4G,SAAS,CAAC,uBAAuB,CAAA7G,QAAA,cAC5CvE,IAAA,OAAIoL,SAAS,CAAC,MAAM,CAAA7G,QAAA,CAAC,mBAAiB,CAAI,CAAC,CAChC,CAAC,cACdrE,KAAA,CAACrB,IAAI,CAAC6F,IAAI,EAAAH,QAAA,eACRrE,KAAA,QAAKkL,SAAS,CAAC,gCAAgC,CAAA7G,QAAA,EAC5CxB,WAAW,eACV7C,KAAA,QAAKkL,SAAS,CAAC,oBAAoB,CAAA7G,QAAA,eACjCvE,IAAA,CAACf,OAAO,EAACsO,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,QAAQ,CAAAjJ,QAAA,cACvCvE,IAAA,SAAMoL,SAAS,CAAC,iBAAiB,CAAA7G,QAAA,CAAC,oBAAkB,CAAM,CAAC,CACpD,CAAC,cACVvE,IAAA,SAAMqE,KAAK,CAAE,CAAEoJ,KAAK,CAAE,OAAO,CAAEC,UAAU,CAAE,EAAG,CAAE,CAAAnJ,QAAA,CAAC,oBAAkB,CAAM,CAAC,EACvE,CACN,cACDvE,IAAA,QACEuM,GAAG,CAAE9K,cAAc,CAAIA,cAAc,CAACkM,UAAU,CAAC,OAAO,CAAC,CAAGlM,cAAc,CAAG,0BAA0BA,cAAc,EAAE,CAAI,EAAG,CAC9H+K,GAAG,CAAC,WAAW,CACfnI,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAC5BsJ,OAAO,CAAGlI,CAAC,EAAK,CAAEA,CAAC,CAACE,MAAM,CAAC8D,OAAO,CAAG,IAAI,CAAEhE,CAAC,CAACE,MAAM,CAAC2G,GAAG,CAAG,EAAE,CAAEzK,QAAQ,CAAC,oCAAoC,CAAC,CAAE,CAAE,CACjH,CAAC,CACDa,YAAY,EAAI,CAACI,WAAW,eAC3B/C,IAAA,QAAKoL,SAAS,CAAC,oBAAoB,CAAA7G,QAAA,cACjCvE,IAAA,CAACf,OAAO,EAACsO,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,QAAQ,CAAAjJ,QAAA,cACvCvE,IAAA,SAAMoL,SAAS,CAAC,iBAAiB,CAAA7G,QAAA,CAAC,eAAa,CAAM,CAAC,CAC/C,CAAC,CACP,CACN,CAEApB,WAAW,CAAC+D,MAAM,CAAG,CAAC,EAAI,CAACnE,WAAW,eACrC7C,KAAA,QAAKmE,KAAK,CAAE,CAAEiI,OAAO,CAAE,MAAM,CAAEuB,UAAU,CAAE,QAAQ,CAAEC,SAAS,CAAE,EAAE,CAAEC,GAAG,CAAE,EAAG,CAAE,CAAAxJ,QAAA,eAC5EvE,IAAA,WAAQgN,OAAO,CAAE7C,YAAa,CAACmD,QAAQ,CAAEjK,iBAAiB,GAAK,CAAE,CAAAkB,QAAA,CAAC,QAAC,CAAQ,CAAC,cAC5EvE,IAAA,WAAQgN,OAAO,CAAE/C,eAAgB,CAAA1F,QAAA,CAAEtB,SAAS,CAAG,UAAU,CAAG,SAAS,CAAS,CAAC,cAC/EjD,IAAA,WAAQgN,OAAO,CAAEzC,aAAc,CAAC+C,QAAQ,CAAEjK,iBAAiB,EAAIF,WAAW,CAAC+D,MAAM,CAAG,CAAE,CAAA3C,QAAA,CAAC,QAAC,CAAQ,CAAC,cACjGvE,IAAA,UACE0M,IAAI,CAAC,OAAO,CACZlC,GAAG,CAAE,CAAE,CACPF,GAAG,CAAEnH,WAAW,CAAC+D,MAAM,CAAG,CAAE,CAC5B1B,KAAK,CAAEnC,iBAAkB,CACzBqI,QAAQ,CAAEjB,kBAAmB,CAC7BpG,KAAK,CAAE,CAAE2J,IAAI,CAAE,CAAE,CAAE,CACpB,CAAC,cACF9N,KAAA,SAAMmE,KAAK,CAAE,CAAE4J,QAAQ,CAAE,EAAG,CAAE,CAAA1J,QAAA,EAAElB,iBAAiB,CAAG,CAAC,CAAC,KAAG,CAACF,WAAW,CAAC+D,MAAM,EAAO,CAAC,EACjF,CACN,EACE,CAAC,CAELnF,gBAAgB,EAAIA,gBAAgB,CAAC6G,UAAU,eAC9C1I,KAAA,CAAAE,SAAA,EAAAmE,QAAA,eACEvE,IAAA,OAAAuE,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAC1BvE,IAAA,QAAKoL,SAAS,CAAC,kBAAkB,CAAA7G,QAAA,cAC/BrE,KAAA,UAAOkL,SAAS,CAAC,qBAAqB,CAAA7G,QAAA,eACpCvE,IAAA,UAAAuE,QAAA,cACErE,KAAA,OAAAqE,QAAA,eACEvE,IAAA,OAAAuE,QAAA,CAAI,qBAAmB,CAAI,CAAC,cAC5BvE,IAAA,OAAAuE,QAAA,CAAI,OAAK,CAAI,CAAC,EACZ,CAAC,CACA,CAAC,cACRvE,IAAA,UAAAuE,QAAA,CACG2J,MAAM,CAACC,OAAO,CAACxD,cAAc,CAAC,CAAC,CAAC,CAACiB,GAAG,CAACwC,IAAA,MAAC,CAACrC,GAAG,CAAEsC,KAAK,CAAC,CAAAD,IAAA,oBACjDlO,KAAA,OAAAqE,QAAA,eACEvE,IAAA,OAAAuE,QAAA,CAAKwH,GAAG,CAAK,CAAC,cACd/L,IAAA,OAAAuE,QAAA,CAAK8J,KAAK,CAAK,CAAC,GAFTtC,GAGL,CAAC,EACN,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,CAELhK,gBAAgB,CAACiH,kBAAkB,EAAIkF,MAAM,CAACI,IAAI,CAACvM,gBAAgB,CAACiH,kBAAkB,CAAC,CAAC9B,MAAM,CAAG,CAAC,eACjGhH,KAAA,CAAAE,SAAA,EAAAmE,QAAA,eACEvE,IAAA,OAAAuE,QAAA,CAAI,yCAAuC,CAAI,CAAC,cAChDvE,IAAA,QAAKoL,SAAS,CAAC,kBAAkB,CAAA7G,QAAA,cAC/BrE,KAAA,UAAOkL,SAAS,CAAC,qBAAqB,CAAA7G,QAAA,eACpCvE,IAAA,UAAAuE,QAAA,cACErE,KAAA,OAAAqE,QAAA,eACEvE,IAAA,OAAAuE,QAAA,CAAI,qBAAmB,CAAI,CAAC,cAC5BvE,IAAA,OAAAuE,QAAA,CAAI,wBAAsB,CAAI,CAAC,EAC7B,CAAC,CACA,CAAC,cACRvE,IAAA,UAAAuE,QAAA,CACG2J,MAAM,CAACC,OAAO,CAACpM,gBAAgB,CAACiH,kBAAkB,CAAC,CAAC4C,GAAG,CAAC2C,KAAA,MAAC,CAACxC,GAAG,CAAE7E,MAAM,CAAC,CAAAqH,KAAA,oBACrErO,KAAA,OAAAqE,QAAA,eACEvE,IAAA,OAAAuE,QAAA,CAAKwH,GAAG,CAAK,CAAC,cACd/L,IAAA,OAAAuE,QAAA,CAAK2C,MAAM,CAAK,CAAC,GAFV6E,GAGL,CAAC,EACN,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,EACN,CACH,EACD,CACH,CAEAtI,iBAAiB,CAACyD,MAAM,CAAG,CAAC,eAC3BhH,KAAA,QAAKmE,KAAK,CAAE,CAAEyJ,SAAS,CAAE,EAAG,CAAE,CAAAvJ,QAAA,eAC5BvE,IAAA,OAAAuE,QAAA,CAAI,0BAAwB,CAAI,CAAC,cACjCvE,IAAA,QAAKoL,SAAS,CAAC,kBAAkB,CAAC/G,KAAK,CAAE,CAAEyI,SAAS,CAAE,GAAG,CAAE0B,SAAS,CAAE,MAAO,CAAE,CAAAjK,QAAA,cAC7ErE,KAAA,UAAOkL,SAAS,CAAC,8BAA8B,CAAA7G,QAAA,eAC7CvE,IAAA,UAAAuE,QAAA,cACErE,KAAA,OAAAqE,QAAA,eACEvE,IAAA,OAAAuE,QAAA,CAAI,IAAE,CAAI,CAAC,cACXvE,IAAA,OAAAuE,QAAA,CAAI,OAAK,CAAI,CAAC,cACdvE,IAAA,OAAAuE,QAAA,CAAI,KAAG,CAAI,CAAC,cACZvE,IAAA,OAAAuE,QAAA,CAAI,OAAK,CAAI,CAAC,cACdvE,IAAA,OAAAuE,QAAA,CAAI,QAAM,CAAI,CAAC,EACb,CAAC,CACA,CAAC,cACRvE,IAAA,UAAAuE,QAAA,CACGd,iBAAiB,CAACmI,GAAG,CAAC6C,GAAG,eACxBvO,KAAA,OAAAqE,QAAA,eACEvE,IAAA,OAAAuE,QAAA,CAAKkK,GAAG,CAACC,EAAE,CAAK,CAAC,cACjB1O,IAAA,OAAAuE,QAAA,CAAKkK,GAAG,CAACE,KAAK,CAAK,CAAC,cACpB3O,IAAA,OAAAuE,QAAA,CAAKkK,GAAG,CAACG,GAAG,CAAG,GAAGH,GAAG,CAACG,GAAG,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,KAAKJ,GAAG,CAACG,GAAG,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAG,GAAG,CAAK,CAAC,cAC/E7O,IAAA,OAAAuE,QAAA,CAAKkK,GAAG,CAACK,KAAK,CAAK,CAAC,cACpB9O,IAAA,OAAAuE,QAAA,CAAKkK,GAAG,CAACM,MAAM,CAAK,CAAC,GALdN,GAAG,CAACC,EAMT,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,EACH,CACN,CAEA/K,mBAAmB,CAACuD,MAAM,CAAG,CAAC,eAC7BhH,KAAA,QAAKmE,KAAK,CAAE,CAAEyJ,SAAS,CAAE,EAAG,CAAE,CAAAvJ,QAAA,eAC5BvE,IAAA,OAAAuE,QAAA,CAAI,mCAAiC,CAAI,CAAC,cAC1CvE,IAAA,QAAKoL,SAAS,CAAC,kBAAkB,CAAC/G,KAAK,CAAE,CAAEyI,SAAS,CAAE,GAAG,CAAE0B,SAAS,CAAE,MAAO,CAAE,CAAAjK,QAAA,cAC7ErE,KAAA,UAAOkL,SAAS,CAAC,8BAA8B,CAAA7G,QAAA,eAC7CvE,IAAA,UAAAuE,QAAA,cACErE,KAAA,OAAAqE,QAAA,eACEvE,IAAA,OAAAuE,QAAA,CAAI,OAAK,CAAI,CAAC,cACdvE,IAAA,OAAAuE,QAAA,CAAI,wBAAsB,CAAI,CAAC,EAC7B,CAAC,CACA,CAAC,cACRvE,IAAA,UAAAuE,QAAA,CACGZ,mBAAmB,CAACiI,GAAG,CAAC6C,GAAG,eAC1BvO,KAAA,OAAAqE,QAAA,eACEvE,IAAA,OAAAuE,QAAA,CAAKkK,GAAG,CAACE,KAAK,CAAK,CAAC,cACpB3O,IAAA,OAAAuE,QAAA,CAAKkK,GAAG,CAAC,wBAAwB,CAAC,CAAK,CAAC,GAFjCA,GAAG,CAACE,KAGT,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,EACH,CACN,EACQ,CAAC,EACR,CAAC,cAEPzO,KAAA,CAACrB,IAAI,EAACuM,SAAS,CAAC,gBAAgB,CAAA7G,QAAA,eAC9BvE,IAAA,CAACnB,IAAI,CAAC2F,MAAM,EAAC4G,SAAS,CAAC,oBAAoB,CAAA7G,QAAA,cACzCvE,IAAA,OAAIoL,SAAS,CAAC,MAAM,CAAA7G,QAAA,CAAC,cAAY,CAAI,CAAC,CAC3B,CAAC,cACdrE,KAAA,CAACrB,IAAI,CAAC6F,IAAI,EAAAH,QAAA,eACRrE,KAAA,OAAAqE,QAAA,eACEvE,IAAA,OAAAuE,QAAA,CAAI,mDAAiD,CAAI,CAAC,cAC1DvE,IAAA,OAAAuE,QAAA,CAAI,oDAAkD,CAAI,CAAC,cAC3DvE,IAAA,OAAAuE,QAAA,CAAI,iCAA+B,CAAI,CAAC,cACxCvE,IAAA,OAAAuE,QAAA,CAAI,4CAAwC,CAAI,CAAC,EAC/C,CAAC,cACLvE,IAAA,MAAAuE,QAAA,CAAG,6EAA2E,CAAG,CAAC,cAClFrE,KAAA,OAAAqE,QAAA,eACEvE,IAAA,OAAAuE,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAC1BvE,IAAA,OAAAuE,QAAA,CAAI,YAAU,CAAI,CAAC,cACnBvE,IAAA,OAAAuE,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxBvE,IAAA,OAAAuE,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxBvE,IAAA,OAAAuE,QAAA,CAAI,eAAa,CAAI,CAAC,EACpB,CAAC,EACI,CAAC,EACR,CACP,CACE,CAAC,EACH,CAAC,cAENrE,KAAA,CAACf,IAAI,EACH6P,SAAS,CAAEzM,SAAU,CACrB0M,QAAQ,CAAGC,CAAC,EAAK1M,YAAY,CAAC0M,CAAC,CAAE,CACjC9D,SAAS,CAAC,MAAM,CAAA7G,QAAA,eAEhBvE,IAAA,CAACd,GAAG,EAACiQ,QAAQ,CAAC,WAAW,CAACC,KAAK,CAAC,WAAW,CAAA7K,QAAA,cACzCvE,IAAA,CAACnB,IAAI,EAACuM,SAAS,CAAC,WAAW,CAAA7G,QAAA,cACzBrE,KAAA,CAACrB,IAAI,CAAC6F,IAAI,EAAAH,QAAA,eACRvE,IAAA,OAAAuE,QAAA,CAAI,oCAAkC,CAAI,CAAC,cAC3CvE,IAAA,MAAAuE,QAAA,CAAG,iNAIH,CAAG,CAAC,cAEJvE,IAAA,OAAAuE,QAAA,CAAI,iCAA+B,CAAI,CAAC,cACxCrE,KAAA,OAAAqE,QAAA,eACErE,KAAA,OAAAqE,QAAA,eAAIvE,IAAA,WAAAuE,QAAA,CAAQ,mCAAiC,CAAQ,CAAC,uEAAoE,EAAI,CAAC,cAC/HrE,KAAA,OAAAqE,QAAA,eAAIvE,IAAA,WAAAuE,QAAA,CAAQ,yBAAuB,CAAQ,CAAC,gEAA6D,EAAI,CAAC,cAC9GrE,KAAA,OAAAqE,QAAA,eAAIvE,IAAA,WAAAuE,QAAA,CAAQ,sBAAoB,CAAQ,CAAC,sDAAmD,EAAI,CAAC,cACjGrE,KAAA,OAAAqE,QAAA,eAAIvE,IAAA,WAAAuE,QAAA,CAAQ,uBAAqB,CAAQ,CAAC,mEAAgE,EAAI,CAAC,cAC/GrE,KAAA,OAAAqE,QAAA,eAAIvE,IAAA,WAAAuE,QAAA,CAAQ,wBAAsB,CAAQ,CAAC,yDAAsD,EAAI,CAAC,cACtGrE,KAAA,OAAAqE,QAAA,eAAIvE,IAAA,WAAAuE,QAAA,CAAQ,6BAA2B,CAAQ,CAAC,oDAAiD,EAAI,CAAC,cACtGrE,KAAA,OAAAqE,QAAA,eAAIvE,IAAA,WAAAuE,QAAA,CAAQ,eAAa,CAAQ,CAAC,4DAAyD,EAAI,CAAC,cAChGrE,KAAA,OAAAqE,QAAA,eAAIvE,IAAA,WAAAuE,QAAA,CAAQ,eAAa,CAAQ,CAAC,oDAAiD,EAAI,CAAC,EACtF,CAAC,cAELvE,IAAA,OAAAuE,QAAA,CAAI,sBAAoB,CAAI,CAAC,cAC7BvE,IAAA,MAAAuE,QAAA,CAAG,yJAGH,CAAG,CAAC,cACJrE,KAAA,OAAAqE,QAAA,eACEvE,IAAA,OAAAuE,QAAA,CAAI,oCAAkC,CAAI,CAAC,cAC3CvE,IAAA,OAAAuE,QAAA,CAAI,mCAAiC,CAAI,CAAC,cAC1CvE,IAAA,OAAAuE,QAAA,CAAI,wCAAsC,CAAI,CAAC,cAC/CvE,IAAA,OAAAuE,QAAA,CAAI,8CAA4C,CAAI,CAAC,EACnD,CAAC,cAELvE,IAAA,OAAAuE,QAAA,CAAI,WAAS,CAAI,CAAC,cAClBrE,KAAA,OAAAqE,QAAA,eACEvE,IAAA,OAAAuE,QAAA,CAAI,0CAAwC,CAAI,CAAC,cACjDvE,IAAA,OAAAuE,QAAA,CAAI,oCAAkC,CAAI,CAAC,cAC3CvE,IAAA,OAAAuE,QAAA,CAAI,gDAA8C,CAAI,CAAC,cACvDvE,IAAA,OAAAuE,QAAA,CAAI,4CAA0C,CAAI,CAAC,cACnDvE,IAAA,OAAAuE,QAAA,CAAI,qDAAmD,CAAI,CAAC,EAC1D,CAAC,EACI,CAAC,CACR,CAAC,CACJ,CAAC,cAENvE,IAAA,CAACd,GAAG,EAACiQ,QAAQ,CAAC,aAAa,CAACC,KAAK,CAAC,aAAa,CAAA7K,QAAA,cAC7CvE,IAAA,CAACnB,IAAI,EAACuM,SAAS,CAAC,WAAW,CAAA7G,QAAA,cACzBrE,KAAA,CAACrB,IAAI,CAAC6F,IAAI,EAAAH,QAAA,eACRvE,IAAA,OAAAuE,QAAA,CAAI,oCAAkC,CAAI,CAAC,cAC3CvE,IAAA,MAAAuE,QAAA,CAAG,iNAIH,CAAG,CAAC,cAEJvE,IAAA,OAAAuE,QAAA,CAAI,iCAA+B,CAAI,CAAC,cACxCrE,KAAA,OAAAqE,QAAA,eACErE,KAAA,OAAAqE,QAAA,eAAIvE,IAAA,WAAAuE,QAAA,CAAQ,mCAAiC,CAAQ,CAAC,uEAAoE,EAAI,CAAC,cAC/HrE,KAAA,OAAAqE,QAAA,eAAIvE,IAAA,WAAAuE,QAAA,CAAQ,yBAAuB,CAAQ,CAAC,gEAA6D,EAAI,CAAC,cAC9GrE,KAAA,OAAAqE,QAAA,eAAIvE,IAAA,WAAAuE,QAAA,CAAQ,sBAAoB,CAAQ,CAAC,sDAAmD,EAAI,CAAC,cACjGrE,KAAA,OAAAqE,QAAA,eAAIvE,IAAA,WAAAuE,QAAA,CAAQ,uBAAqB,CAAQ,CAAC,mEAAgE,EAAI,CAAC,cAC/GrE,KAAA,OAAAqE,QAAA,eAAIvE,IAAA,WAAAuE,QAAA,CAAQ,wBAAsB,CAAQ,CAAC,yDAAsD,EAAI,CAAC,cACtGrE,KAAA,OAAAqE,QAAA,eAAIvE,IAAA,WAAAuE,QAAA,CAAQ,6BAA2B,CAAQ,CAAC,oDAAiD,EAAI,CAAC,cACtGrE,KAAA,OAAAqE,QAAA,eAAIvE,IAAA,WAAAuE,QAAA,CAAQ,eAAa,CAAQ,CAAC,4DAAyD,EAAI,CAAC,cAChGrE,KAAA,OAAAqE,QAAA,eAAIvE,IAAA,WAAAuE,QAAA,CAAQ,eAAa,CAAQ,CAAC,oDAAiD,EAAI,CAAC,EACtF,CAAC,cAELvE,IAAA,OAAAuE,QAAA,CAAI,sBAAoB,CAAI,CAAC,cAC7BvE,IAAA,MAAAuE,QAAA,CAAG,yJAGH,CAAG,CAAC,cACJrE,KAAA,OAAAqE,QAAA,eACEvE,IAAA,OAAAuE,QAAA,CAAI,oCAAkC,CAAI,CAAC,cAC3CvE,IAAA,OAAAuE,QAAA,CAAI,mCAAiC,CAAI,CAAC,cAC1CvE,IAAA,OAAAuE,QAAA,CAAI,wCAAsC,CAAI,CAAC,cAC/CvE,IAAA,OAAAuE,QAAA,CAAI,8CAA4C,CAAI,CAAC,EACnD,CAAC,cAELvE,IAAA,OAAAuE,QAAA,CAAI,WAAS,CAAI,CAAC,cAClBrE,KAAA,OAAAqE,QAAA,eACEvE,IAAA,OAAAuE,QAAA,CAAI,0CAAwC,CAAI,CAAC,cACjDvE,IAAA,OAAAuE,QAAA,CAAI,oCAAkC,CAAI,CAAC,cAC3CvE,IAAA,OAAAuE,QAAA,CAAI,gDAA8C,CAAI,CAAC,cACvDvE,IAAA,OAAAuE,QAAA,CAAI,4CAA0C,CAAI,CAAC,cACnDvE,IAAA,OAAAuE,QAAA,CAAI,qDAAmD,CAAI,CAAC,EAC1D,CAAC,EACI,CAAC,CACR,CAAC,CACJ,CAAC,EACF,CAAC,EACE,CAAC,CAEhB,CAEA,cAAe,CAAAzD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}