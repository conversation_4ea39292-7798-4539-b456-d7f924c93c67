{"ast": null, "code": "import React,{useState,useRef,useEffect}from'react';import{Card,Button,Form,Alert,Spinner,Table,Row,Col}from'react-bootstrap';import axios from'axios';import Webcam from'react-webcam';import useResponsive from'../hooks/useResponsive';import'./VideoDefectDetection.css';import{validateUploadFile,showFileValidationError}from'../utils/fileValidation';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const VideoDefectDetection=()=>{const[selectedModel,setSelectedModel]=useState('All');const[videoFile,setVideoFile]=useState(null);const[videoPreview,setVideoPreview]=useState(null);const[processedVideo,setProcessedVideo]=useState(null);const[loading,setLoading]=useState(false);const[error,setError]=useState('');const[isProcessing,setIsProcessing]=useState(false);const[shouldStop,setShouldStop]=useState(false);const[coordinates,setCoordinates]=useState('Not Available');const[inputSource,setInputSource]=useState('video');const[cameraActive,setCameraActive]=useState(false);const[cameraOrientation,setCameraOrientation]=useState('environment');const[isRecording,setIsRecording]=useState(false);const[recordingTime,setRecordingTime]=useState(0);const[recordedChunks,setRecordedChunks]=useState([]);const recordedChunksRef=useRef([]);// <-- Add this line\n// Video processing states\nconst[frameBuffer,setFrameBuffer]=useState([]);const[currentFrameIndex,setCurrentFrameIndex]=useState(0);const[isBuffering,setIsBuffering]=useState(false);const[isPlaying,setIsPlaying]=useState(false);const[processingProgress,setProcessingProgress]=useState(0);const[allDetections,setAllDetections]=useState([]);const[videoResults,setVideoResults]=useState(null);// Add new state for frame-by-frame updates\nconst[currentDetections,setCurrentDetections]=useState([]);const[showResults,setShowResults]=useState(false);const streamRef=useRef(null);const webcamRef=useRef(null);const fileInputRef=useRef(null);const recordingTimerRef=useRef(null);const mediaRecorderRef=useRef(null);const{isMobile}=useResponsive();const BUFFER_SIZE=10;const PLAYBACK_FPS=15;const MAX_RECORDING_TIME=60;// 1 minute limit\nconst[totalFrames,setTotalFrames]=useState(null);const totalFramesValid=Number.isFinite(totalFrames)&&totalFrames>0;const[videoDuration,setVideoDuration]=useState(null);const[videoFPS,setVideoFPS]=useState(30);// Default FPS\n// Available models\nconst modelOptions=[{value:'All',label:'All (detect all types of defects)'},{value:'Potholes',label:'Potholes'},{value:'Alligator Cracks',label:'Alligator Cracks'},{value:'Kerbs',label:'Kerbs'}];// Get user location\nuseEffect(()=>{if(navigator.geolocation){navigator.geolocation.getCurrentPosition(position=>{const{latitude,longitude}=position.coords;setCoordinates(`${latitude.toFixed(6)}, ${longitude.toFixed(6)}`);},err=>{console.error(\"Error getting location:\",err);setCoordinates('Location unavailable');});}},[]);// Recording timer\nuseEffect(()=>{if(isRecording){recordingTimerRef.current=setInterval(()=>{setRecordingTime(prev=>{if(prev>=MAX_RECORDING_TIME){handleStopRecording();return MAX_RECORDING_TIME;}return prev+1;});},1000);}else{if(recordingTimerRef.current){clearInterval(recordingTimerRef.current);}}return()=>{if(recordingTimerRef.current){clearInterval(recordingTimerRef.current);}};},[isRecording]);// Video playback effect\nuseEffect(()=>{let playbackInterval;if(isPlaying&&frameBuffer.length>0){playbackInterval=setInterval(()=>{setCurrentFrameIndex(prev=>{if(prev<frameBuffer.length-1){return prev+1;}else{setIsPlaying(false);return prev;}});},1000/PLAYBACK_FPS);}return()=>{if(playbackInterval)clearInterval(playbackInterval);};},[isPlaying,frameBuffer]);// Update processed video when frame changes\nuseEffect(()=>{if(frameBuffer.length>0&&currentFrameIndex<frameBuffer.length){setProcessedVideo(frameBuffer[currentFrameIndex]);}},[currentFrameIndex,frameBuffer]);// When videoPreview is set, extract duration and FPS\nuseEffect(()=>{if(videoPreview){const video=document.createElement('video');video.src=videoPreview;video.preload='metadata';video.onloadedmetadata=()=>{setVideoDuration(video.duration);// Try to get FPS from video tracks if available\nif(video.webkitVideoDecodedByteCount!==undefined){// Not standard, but some browsers may expose frameRate\ntry{const tracks=video.videoTracks||video.captureStream&&video.captureStream().getVideoTracks();if(tracks&&tracks.length>0&&tracks[0].getSettings){const settings=tracks[0].getSettings();if(settings.frameRate){setVideoFPS(settings.frameRate);}}}catch(e){}}};}},[videoPreview]);// Helper to estimate total frames if backend total_frames is invalid\nconst estimatedTotalFrames=videoDuration&&videoFPS?Math.round(videoDuration*videoFPS):null;// Add cleanup effect for SSE stream\nuseEffect(()=>{return()=>{if(streamRef.current){streamRef.current.abort();}};},[]);const[warning,setWarning]=useState('');// Handle video file selection\nconst handleVideoChange=e=>{const file=e.target.files[0];if(file){// Validate video file first\nconst validation=validateUploadFile(file,'video','video_defect_detection');if(!validation.isValid){showFileValidationError(validation.errorMessage,setError);// Clear the file input\nif(e.target){e.target.value='';}return;}// Clear any previous errors\nsetError('');const video=document.createElement('video');video.preload='metadata';video.onloadedmetadata=()=>{if(video.duration>60){setWarning('Video upload is restricted to 1 minute. Please select a shorter video.');setVideoFile(null);setVideoPreview(null);setProcessedVideo(null);setVideoResults(null);setAllDetections([]);setError('');if(fileInputRef.current)fileInputRef.current.value='';}else{setWarning('');setVideoFile(file);setVideoPreview(URL.createObjectURL(file));setProcessedVideo(null);setVideoResults(null);setAllDetections([]);setError('');}};video.src=URL.createObjectURL(file);}};// Handle camera activation\nconst toggleCamera=()=>{setCameraActive(!cameraActive);if(!cameraActive){setVideoFile(null);setVideoPreview(null);setProcessedVideo(null);setVideoResults(null);setAllDetections([]);setError('');}};// Start recording\nconst handleStartRecording=async()=>{console.log('handleStartRecording called');if(!webcamRef.current||!webcamRef.current.stream){setError('Camera not available');return;}try{setRecordedChunks([]);recordedChunksRef.current=[];setRecordingTime(0);setIsRecording(true);setError('');const mediaRecorder=new MediaRecorder(webcamRef.current.stream,{mimeType:'video/webm'});mediaRecorderRef.current=mediaRecorder;mediaRecorder.ondataavailable=event=>{console.log('ondataavailable fired, size:',event.data.size);if(event.data.size>0){setRecordedChunks(prev=>{const updated=[...prev,event.data];recordedChunksRef.current=updated;// <-- Keep ref in sync\nreturn updated;});}};mediaRecorder.onstop=()=>{console.log('mediaRecorder.onstop fired');// Use the ref to get the latest chunks\nconst chunks=recordedChunksRef.current;// Debug logs\nconsole.log('onstop: recordedChunks length:',chunks.length);let totalSize=0;chunks.forEach((c,i)=>{console.log(`Chunk ${i} size:`,c.size);totalSize+=c.size;});console.log('Total recorded size:',totalSize);const blob=new Blob(chunks,{type:'video/webm'});const file=new File([blob],`recorded_video_${Date.now()}.webm`,{type:'video/webm'});setVideoFile(file);setVideoPreview(URL.createObjectURL(blob));setIsRecording(false);setRecordingTime(0);// Reset the ref and state for next recording\nrecordedChunksRef.current=[];setRecordedChunks([]);};mediaRecorder.onstart=()=>{console.log('mediaRecorder.onstart fired');};mediaRecorder.onerror=e=>{console.error('mediaRecorder.onerror',e);};console.log('Calling mediaRecorder.start(1000)');mediaRecorder.start(1000);// timeslice: 1000ms\n}catch(error){setError('Failed to start recording: '+error.message);setIsRecording(false);}};// Stop recording\nconst handleStopRecording=()=>{console.log('handleStopRecording called');if(mediaRecorderRef.current&&isRecording){mediaRecorderRef.current.stop();setIsRecording(false);setRecordingTime(0);}};// Toggle camera orientation\nconst toggleCameraOrientation=()=>{setCameraOrientation(prev=>prev==='environment'?'user':'environment');};// Check if ready for processing\nconst isReadyForProcessing=()=>{return inputSource==='video'&&videoFile||inputSource==='camera'&&videoFile;};// Handle video processing\nconst handleProcess=async()=>{if(!isReadyForProcessing()){setError('Please provide a video file first');return;}// Reset states\nsetLoading(true);setError('');setIsProcessing(true);setShouldStop(false);setIsBuffering(true);setIsPlaying(false);setFrameBuffer([]);setCurrentFrameIndex(0);setProcessingProgress(0);setAllDetections([]);setCurrentDetections([]);setProcessedVideo(null);setVideoResults(null);setShowResults(false);// Create abort controller for cleanup\nstreamRef.current=new AbortController();try{const formData=new FormData();formData.append('video',videoFile);formData.append('selectedModel',selectedModel);formData.append('coordinates',coordinates);const userString=sessionStorage.getItem('user');const user=userString?JSON.parse(userString):null;formData.append('username',(user===null||user===void 0?void 0:user.username)||'Unknown');formData.append('role',(user===null||user===void 0?void 0:user.role)||'Unknown');console.log('Starting video processing with model:',selectedModel);const sseUrl='/api/pavement/detect-video';const response=await fetch(sseUrl,{method:'POST',body:formData,signal:streamRef.current.signal});if(!response.ok){throw new Error(`HTTP error! status: ${response.status}`);}const reader=response.body.getReader();const decoder=new TextDecoder();let buffer='';// Helper to accumulate detections\nconst appendDetections=detections=>{if(detections&&Array.isArray(detections)&&detections.length>0){setAllDetections(prev=>[...prev,...detections]);}};const processStream=async()=>{try{while(true){const{done,value}=await reader.read();if(done){console.log('Stream ended naturally');setIsProcessing(false);setLoading(false);setIsBuffering(false);break;}buffer+=decoder.decode(value,{stream:true});let lines=buffer.split('\\n');// Keep the last line in buffer if it's incomplete\nbuffer=lines.pop();for(const line of lines){if(line.startsWith('data: ')){try{const data=JSON.parse(line.substring(6));// Debug log for every SSE message\nconsole.log('SSE data:',data);// Handle error case\nif(data.success===false){setError(data.message||'Video processing failed');setIsProcessing(false);setLoading(false);setIsBuffering(false);return;}// Update progress immediately\nif(data.progress!==undefined&&totalFramesValid){setProcessingProgress(data.progress);if(!showResults&&data.progress>0){setShowResults(true);}}else if(data.frame_count!==undefined&&totalFramesValid){// Calculate progress if not provided but backend totalFrames is valid\nconst progress=data.frame_count/totalFrames*100;setProcessingProgress(progress);if(!showResults&&progress>0){setShowResults(true);}}else if(data.frame_count!==undefined&&estimatedTotalFrames){// Fallback: use estimated total frames from duration and FPS\nconst progress=data.frame_count/estimatedTotalFrames*100;setProcessingProgress(progress);if(!showResults&&progress>0){setShowResults(true);}}// Update frame display immediately\nif(data.frame&&typeof data.frame==='string'&&data.frame.length>1000){setFrameBuffer(prev=>{const newBuffer=[...prev,data.frame];return newBuffer;});setProcessedVideo(data.frame);setCurrentFrameIndex(prev=>prev+1);if(isBuffering){setIsBuffering(false);}}// Accumulate detections per frame\nif(data.detections&&data.detections.length>0){setCurrentDetections(data.detections);appendDetections(data.detections);}// Handle final results\nif(data.all_detections){setVideoResults(data);setAllDetections(data.all_detections);setIsProcessing(false);setLoading(false);setIsBuffering(false);setProcessingProgress(100);setCurrentFrameIndex(0);setIsPlaying(false);console.log('Video processing completed');return;}// Handle end signal\nif(data.end){console.log('Received end signal');setIsProcessing(false);setLoading(false);setIsBuffering(false);return;}// Update totalFrames when receiving SSE data:\nif(data.total_frames!==undefined){setTotalFrames(data.total_frames);}}catch(parseError){console.warn('Error parsing SSE data:',parseError);}}}}}catch(streamError){if(streamError.name==='AbortError'){console.log('Stream aborted by user');}else{console.error('Stream processing error:',streamError);setError('Error processing video stream');}setIsProcessing(false);setLoading(false);setIsBuffering(false);}finally{if(reader){try{reader.releaseLock();}catch(e){console.warn('Error releasing reader lock:',e);}}}};await processStream();}catch(error){console.error('Video processing error:',error);setError(error.message||'Video processing failed');setLoading(false);setIsProcessing(false);}};// Stop processing\nconst handleStopProcessing=async()=>{try{await axios.post('/api/pavement/stop-video-processing');setIsProcessing(false);setShouldStop(true);setIsBuffering(false);setIsPlaying(false);setLoading(false);setError('Video processing stopped');}catch(error){console.error('Error stopping processing:',error);setError('Failed to stop processing');}};// Reset all\nconst handleReset=()=>{setVideoFile(null);setVideoPreview(null);setProcessedVideo(null);setVideoResults(null);setAllDetections([]);setCurrentDetections([]);setFrameBuffer([]);setCurrentFrameIndex(0);setIsProcessing(false);setShouldStop(false);setIsBuffering(false);setIsPlaying(false);setProcessingProgress(0);setError('');setSelectedModel('All');setShowResults(false);// <-- Ensure table is hidden after reset\nif(fileInputRef.current){fileInputRef.current.value='';}};// Playback controls\nconst handlePlayPause=()=>setIsPlaying(!isPlaying);const handleRewind=()=>setCurrentFrameIndex(Math.max(currentFrameIndex-5,0));const handleForward=()=>setCurrentFrameIndex(Math.min(currentFrameIndex+5,frameBuffer.length-1));// Group detections by type\nconst getDetectionSummary=()=>{const summary={};allDetections.forEach(det=>{summary[det.type]=(summary[det.type]||0)+1;});return summary;};// Get tracking statistics\nconst getTrackingStats=()=>{if(videoResults){return{uniqueDetections:videoResults.total_unique_detections||allDetections.length,frameDetections:videoResults.total_frame_detections||allDetections.length,duplicatesRemoved:(videoResults.total_frame_detections||allDetections.length)-(videoResults.total_unique_detections||allDetections.length)};}return{uniqueDetections:allDetections.length,frameDetections:allDetections.length,duplicatesRemoved:0};};// Format time\nconst formatTime=seconds=>{const mins=Math.floor(seconds/60);const secs=seconds%60;return`${mins}:${secs.toString().padStart(2,'0')}`;};return/*#__PURE__*/_jsx(\"div\",{className:\"video-defect-detection\",children:/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Card,{className:\"mb-4\",children:[/*#__PURE__*/_jsx(Card.Header,{className:\"bg-primary text-white\",children:/*#__PURE__*/_jsx(\"h5\",{className:\"mb-0\",children:\"Video Defect Detection\"})}),/*#__PURE__*/_jsxs(Card.Body,{children:[error&&/*#__PURE__*/_jsx(Alert,{variant:\"danger\",className:\"mb-3\",children:error}),warning&&/*#__PURE__*/_jsx(Alert,{variant:\"warning\",className:\"mb-3\",children:warning}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Detection Model\"}),/*#__PURE__*/_jsx(Form.Select,{value:selectedModel,onChange:e=>setSelectedModel(e.target.value),disabled:isProcessing,children:modelOptions.map(option=>/*#__PURE__*/_jsx(\"option\",{value:option.value,children:option.label},option.value))})]}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Input Source\"}),/*#__PURE__*/_jsxs(Form.Select,{value:inputSource,onChange:e=>setInputSource(e.target.value),disabled:isProcessing,children:[/*#__PURE__*/_jsx(\"option\",{value:\"video\",children:\"Video Upload\"}),/*#__PURE__*/_jsx(\"option\",{value:\"camera\",children:\"Live Camera Recording\"})]})]}),inputSource==='video'&&/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Upload Video\"}),/*#__PURE__*/_jsx(Form.Control,{type:\"file\",accept:\"video/*\",onChange:handleVideoChange,ref:fileInputRef,disabled:isProcessing}),videoPreview&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-3\",children:/*#__PURE__*/_jsx(\"video\",{src:videoPreview,controls:true,className:\"video-preview\",style:{maxHeight:'200px'}})})]}),inputSource==='camera'&&/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex gap-2 mb-2\",children:[/*#__PURE__*/_jsx(Button,{variant:cameraActive?\"danger\":\"info\",onClick:toggleCamera,disabled:isProcessing,children:cameraActive?'Stop Camera':'Start Camera'}),isMobile&&cameraActive&&/*#__PURE__*/_jsx(Button,{variant:\"outline-secondary\",onClick:toggleCameraOrientation,size:\"sm\",children:\"Rotate Camera\"})]}),cameraActive&&/*#__PURE__*/_jsxs(\"div\",{className:\"webcam-container\",children:[/*#__PURE__*/_jsx(Webcam,{audio:false,ref:webcamRef,screenshotFormat:\"image/jpeg\",width:\"100%\",height:\"auto\",videoConstraints:{width:640,height:480,facingMode:cameraOrientation}}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-2\",children:!isRecording?/*#__PURE__*/_jsx(Button,{variant:\"success\",onClick:handleStartRecording,disabled:isProcessing,children:\"Start Recording\"}):/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center gap-2\",children:[/*#__PURE__*/_jsx(Button,{variant:\"danger\",onClick:handleStopRecording,children:\"Stop Recording\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-danger\",children:[\"Recording: \",formatTime(recordingTime),\" / \",formatTime(MAX_RECORDING_TIME)]})]})})]}),videoPreview&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-3\",children:/*#__PURE__*/_jsx(\"video\",{src:videoPreview,controls:true,className:\"video-preview\",style:{maxHeight:'200px'}})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"action-buttons\",children:[/*#__PURE__*/_jsx(Button,{variant:\"primary\",onClick:handleProcess,disabled:!isReadyForProcessing()||isProcessing,children:loading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Spinner,{size:\"sm\",className:\"me-2\"}),\"Processing...\"]}):'Process Video'}),isProcessing&&/*#__PURE__*/_jsx(Button,{variant:\"warning\",onClick:handleStopProcessing,children:\"Stop Processing\"}),/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:handleReset,disabled:isProcessing,children:\"Reset\"})]}),isProcessing&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-3\",children:(totalFramesValid||estimatedTotalFrames)&&processingProgress>0&&Number.isFinite(processingProgress)?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Processing Progress:\"}),/*#__PURE__*/_jsxs(\"span\",{children:[Math.max(0,Math.min(100,processingProgress)).toFixed(1),\"%\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"progress mt-1\",children:/*#__PURE__*/_jsx(\"div\",{className:\"progress-bar progress-bar-striped progress-bar-animated\",role:\"progressbar\",style:{width:`${Math.max(0,Math.min(100,processingProgress))}%`},\"aria-valuenow\":Math.max(0,Math.min(100,processingProgress)),\"aria-valuemin\":\"0\",\"aria-valuemax\":\"100\"})})]}):/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center mt-3\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Processing...\"}),/*#__PURE__*/_jsx(\"div\",{className:\"progress flex-grow-1 ms-2\",style:{height:'20px'},children:/*#__PURE__*/_jsx(\"div\",{className:\"progress-bar progress-bar-striped progress-bar-animated\",role:\"progressbar\",style:{width:`100%`,backgroundColor:'#e0e0e0'},\"aria-valuenow\":0,\"aria-valuemin\":\"0\",\"aria-valuemax\":\"100\"})})]})})]})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:(showResults||allDetections.length>0||!isProcessing&&videoResults&&allDetections.length>0)&&/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(Card.Header,{className:\"bg-info text-white\",children:[/*#__PURE__*/_jsx(\"h5\",{className:\"mb-0\",children:\"Detection Results\"}),isProcessing&&/*#__PURE__*/_jsx(\"small\",{className:\"text-white-50\",children:\"Results update in real-time as processing continues...\"}),!isProcessing&&videoResults&&/*#__PURE__*/_jsxs(\"small\",{className:\"text-success\",children:[/*#__PURE__*/_jsx(\"b\",{children:\"Processing Complete.\"}),\" Final results are shown below.\"]})]}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"detection-summary mb-3\",children:[/*#__PURE__*/_jsx(\"h6\",{children:\"Detection Summary:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"mb-2\",children:Object.entries(getDetectionSummary()).map(_ref=>{let[type,count]=_ref;return/*#__PURE__*/_jsxs(\"span\",{className:\"badge bg-secondary me-1\",children:[type,\": \",count]},type);})}),/*#__PURE__*/_jsx(\"div\",{className:\"tracking-stats\",children:/*#__PURE__*/_jsxs(\"small\",{className:\"text-muted\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Tracking Stats:\"}),\" \",' ',/*#__PURE__*/_jsxs(\"span\",{className:\"badge bg-success me-1\",children:[\"Unique: \",getTrackingStats().uniqueDetections]}),/*#__PURE__*/_jsxs(\"span\",{className:\"badge bg-info me-1\",children:[\"Total Frames: \",getTrackingStats().frameDetections]}),/*#__PURE__*/_jsxs(\"span\",{className:\"badge bg-warning\",children:[\"Duplicates Removed: \",getTrackingStats().duplicatesRemoved]})]})})]}),(()=>{const potholeDetections=allDetections.filter(d=>d.type==='Pothole');const crackDetections=allDetections.filter(d=>d.type.includes('Crack'));const kerbDetections=allDetections.filter(d=>d.type.includes('Kerb'));return/*#__PURE__*/_jsxs(\"div\",{children:[(selectedModel==='All'||selectedModel==='Potholes')&&/*#__PURE__*/_jsxs(\"div\",{className:\"defect-section potholes mb-4\",children:[/*#__PURE__*/_jsxs(\"h6\",{className:\"text-danger\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"emoji\",children:\"\\uD83D\\uDD73\\uFE0F\"}),\"Potholes Detected: \",potholeDetections.length]}),potholeDetections.length>0?/*#__PURE__*/_jsx(\"div\",{className:\"detection-table-container\",children:/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,size:\"sm\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Area (cm\\xB2)\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Depth (cm)\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Volume (cm\\xB3)\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Volume Range\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:potholeDetections.map((detection,index)=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:detection.track_id||index+1}),/*#__PURE__*/_jsx(\"td\",{children:detection.area_cm2?detection.area_cm2.toFixed(2):'N/A'}),/*#__PURE__*/_jsx(\"td\",{children:detection.depth_cm?detection.depth_cm.toFixed(2):'N/A'}),/*#__PURE__*/_jsx(\"td\",{children:detection.volume?detection.volume.toFixed(2):'N/A'}),/*#__PURE__*/_jsx(\"td\",{children:detection.volume_range||'N/A'})]},index))})]})}):/*#__PURE__*/_jsx(\"div\",{className:\"no-defects-message\",children:\"No potholes detected\"})]}),(selectedModel==='All'||selectedModel==='Alligator Cracks')&&/*#__PURE__*/_jsxs(\"div\",{className:\"defect-section cracks mb-4\",children:[/*#__PURE__*/_jsxs(\"h6\",{className:\"text-success\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"emoji\",children:\"\\uD83E\\uDEA8\"}),\"Cracks Detected: \",crackDetections.length]}),crackDetections.length>0?/*#__PURE__*/_jsx(\"div\",{className:\"detection-table-container\",children:/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,size:\"sm\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Type\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Area (cm\\xB2)\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Area Range\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:crackDetections.map((detection,index)=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:detection.track_id||index+1}),/*#__PURE__*/_jsx(\"td\",{children:detection.type}),/*#__PURE__*/_jsx(\"td\",{children:detection.area_cm2?detection.area_cm2.toFixed(2):'N/A'}),/*#__PURE__*/_jsx(\"td\",{children:detection.area_range||'N/A'})]},index))})]})}):/*#__PURE__*/_jsx(\"div\",{className:\"no-defects-message\",children:\"No cracks detected\"})]}),(selectedModel==='All'||selectedModel==='Kerbs')&&/*#__PURE__*/_jsxs(\"div\",{className:\"defect-section kerbs mb-4\",children:[/*#__PURE__*/_jsxs(\"h6\",{className:\"text-primary\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"emoji\",children:\"\\uD83D\\uDEA7\"}),\"Kerbs Detected: \",kerbDetections.length]}),kerbDetections.length>0?/*#__PURE__*/_jsx(\"div\",{className:\"detection-table-container\",children:/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,size:\"sm\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Type\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Condition\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Length\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:kerbDetections.map((detection,index)=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:detection.track_id||index+1}),/*#__PURE__*/_jsx(\"td\",{children:detection.kerb_type||'Concrete Kerb'}),/*#__PURE__*/_jsx(\"td\",{children:detection.condition||detection.type}),/*#__PURE__*/_jsx(\"td\",{children:detection.length_m?detection.length_m.toFixed(2):'N/A'})]},index))})]})}):/*#__PURE__*/_jsx(\"div\",{className:\"no-defects-message\",children:\"No kerbs detected\"})]})]});})()]})]})})]})});};export default VideoDefectDetection;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Card", "<PERSON><PERSON>", "Form", "<PERSON><PERSON>", "Spinner", "Table", "Row", "Col", "axios", "Webcam", "useResponsive", "validateUploadFile", "showFileValidationError", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "VideoDefectDetection", "selected<PERSON><PERSON>l", "setSelectedModel", "videoFile", "setVideoFile", "videoPreview", "setVideoPreview", "processedVideo", "setProcessedVideo", "loading", "setLoading", "error", "setError", "isProcessing", "setIsProcessing", "shouldStop", "setShouldStop", "coordinates", "setCoordinates", "inputSource", "setInputSource", "cameraActive", "setCameraActive", "cameraOrientation", "setCameraOrientation", "isRecording", "setIsRecording", "recordingTime", "setRecordingTime", "recordedChunks", "setRecordedChunks", "recordedChunksRef", "frameBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentFrameIndex", "setCurrentFrameIndex", "isBuffering", "setIsBuffering", "isPlaying", "setIsPlaying", "processingProgress", "setProcessingProgress", "allDetections", "setAllDetections", "videoResults", "setVideoResults", "currentDetections", "setCurrentDetections", "showResults", "setShowResults", "streamRef", "webcamRef", "fileInputRef", "recordingTimerRef", "mediaRecorderRef", "isMobile", "BUFFER_SIZE", "PLAYBACK_FPS", "MAX_RECORDING_TIME", "totalFrames", "setTotalFrames", "totalFramesValid", "Number", "isFinite", "videoDuration", "setVideoDuration", "videoFPS", "setVideoFPS", "modelOptions", "value", "label", "navigator", "geolocation", "getCurrentPosition", "position", "latitude", "longitude", "coords", "toFixed", "err", "console", "current", "setInterval", "prev", "handleStopRecording", "clearInterval", "playbackInterval", "length", "video", "document", "createElement", "src", "preload", "onloadedmetadata", "duration", "webkitVideoDecodedByteCount", "undefined", "tracks", "videoTracks", "captureStream", "getVideoTracks", "getSettings", "settings", "frameRate", "e", "estimatedTotalFrames", "Math", "round", "abort", "warning", "setWarning", "handleVideoChange", "file", "target", "files", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errorMessage", "URL", "createObjectURL", "toggleCamera", "handleStartRecording", "log", "stream", "mediaRecorder", "MediaRecorder", "mimeType", "ondataavailable", "event", "data", "size", "updated", "onstop", "chunks", "totalSize", "for<PERSON>ach", "c", "i", "blob", "Blob", "type", "File", "Date", "now", "onstart", "onerror", "start", "message", "stop", "toggleCameraOrientation", "isReadyForProcessing", "handleProcess", "AbortController", "formData", "FormData", "append", "userString", "sessionStorage", "getItem", "user", "JSON", "parse", "username", "role", "sseUrl", "response", "fetch", "method", "body", "signal", "ok", "Error", "status", "reader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "buffer", "appendDetections", "detections", "Array", "isArray", "processStream", "done", "read", "decode", "lines", "split", "pop", "line", "startsWith", "substring", "success", "progress", "frame_count", "frame", "new<PERSON>uffer", "all_detections", "end", "total_frames", "parseError", "warn", "streamError", "name", "releaseLock", "handleStopProcessing", "post", "handleReset", "handlePlayPause", "handleRewind", "max", "handleForward", "min", "getDetectionSummary", "summary", "det", "getTrackingStats", "uniqueDetections", "total_unique_detections", "frameDetections", "total_frame_detections", "duplicates<PERSON><PERSON>oved", "formatTime", "seconds", "mins", "floor", "secs", "toString", "padStart", "className", "children", "md", "Header", "Body", "variant", "Group", "Label", "Select", "onChange", "disabled", "map", "option", "Control", "accept", "ref", "controls", "style", "maxHeight", "onClick", "audio", "screenshotFormat", "width", "height", "videoConstraints", "facingMode", "backgroundColor", "Object", "entries", "_ref", "count", "potholeDetections", "filter", "d", "crackDetections", "includes", "kerbDetections", "striped", "bordered", "hover", "detection", "index", "track_id", "area_cm2", "depth_cm", "volume", "volume_range", "area_range", "kerb_type", "condition", "length_m"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/components/VideoDefectDetection.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport { Card, Button, Form, Alert, Spinner, Table, Row, Col } from 'react-bootstrap';\r\nimport axios from 'axios';\r\nimport Webcam from 'react-webcam';\r\nimport useResponsive from '../hooks/useResponsive';\r\nimport './VideoDefectDetection.css';\r\nimport { validateUploadFile, showFileValidationError } from '../utils/fileValidation';\r\n\r\nconst VideoDefectDetection = () => {\r\n  const [selectedModel, setSelectedModel] = useState('All');\r\n  const [videoFile, setVideoFile] = useState(null);\r\n  const [videoPreview, setVideoPreview] = useState(null);\r\n  const [processedVideo, setProcessedVideo] = useState(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n  const [shouldStop, setShouldStop] = useState(false);\r\n  const [coordinates, setCoordinates] = useState('Not Available');\r\n  const [inputSource, setInputSource] = useState('video');\r\n  const [cameraActive, setCameraActive] = useState(false);\r\n  const [cameraOrientation, setCameraOrientation] = useState('environment');\r\n  const [isRecording, setIsRecording] = useState(false);\r\n  const [recordingTime, setRecordingTime] = useState(0);\r\n  const [recordedChunks, setRecordedChunks] = useState([]);\r\n  const recordedChunksRef = useRef([]); // <-- Add this line\r\n  \r\n  // Video processing states\r\n  const [frameBuffer, setFrameBuffer] = useState([]);\r\n  const [currentFrameIndex, setCurrentFrameIndex] = useState(0);\r\n  const [isBuffering, setIsBuffering] = useState(false);\r\n  const [isPlaying, setIsPlaying] = useState(false);\r\n  const [processingProgress, setProcessingProgress] = useState(0);\r\n  const [allDetections, setAllDetections] = useState([]);\r\n  const [videoResults, setVideoResults] = useState(null);\r\n  \r\n  // Add new state for frame-by-frame updates\r\n  const [currentDetections, setCurrentDetections] = useState([]);\r\n  const [showResults, setShowResults] = useState(false);\r\n  const streamRef = useRef(null);\r\n\r\n  const webcamRef = useRef(null);\r\n  const fileInputRef = useRef(null);\r\n  const recordingTimerRef = useRef(null);\r\n  const mediaRecorderRef = useRef(null);\r\n  const { isMobile } = useResponsive();\r\n  \r\n  const BUFFER_SIZE = 10;\r\n  const PLAYBACK_FPS = 15;\r\n  const MAX_RECORDING_TIME = 60; // 1 minute limit\r\n\r\n  const [totalFrames, setTotalFrames] = useState(null);\r\n  const totalFramesValid = Number.isFinite(totalFrames) && totalFrames > 0;\r\n\r\n  const [videoDuration, setVideoDuration] = useState(null);\r\n  const [videoFPS, setVideoFPS] = useState(30); // Default FPS\r\n\r\n  // Available models\r\n  const modelOptions = [\r\n    { value: 'All', label: 'All (detect all types of defects)' },\r\n    { value: 'Potholes', label: 'Potholes' },\r\n    { value: 'Alligator Cracks', label: 'Alligator Cracks' },\r\n    { value: 'Kerbs', label: 'Kerbs' }\r\n  ];\r\n\r\n  // Get user location\r\n  useEffect(() => {\r\n    if (navigator.geolocation) {\r\n      navigator.geolocation.getCurrentPosition(\r\n        (position) => {\r\n          const { latitude, longitude } = position.coords;\r\n          setCoordinates(`${latitude.toFixed(6)}, ${longitude.toFixed(6)}`);\r\n        },\r\n        (err) => {\r\n          console.error(\"Error getting location:\", err);\r\n          setCoordinates('Location unavailable');\r\n        }\r\n      );\r\n    }\r\n  }, []);\r\n\r\n  // Recording timer\r\n  useEffect(() => {\r\n    if (isRecording) {\r\n      recordingTimerRef.current = setInterval(() => {\r\n        setRecordingTime(prev => {\r\n          if (prev >= MAX_RECORDING_TIME) {\r\n            handleStopRecording();\r\n            return MAX_RECORDING_TIME;\r\n          }\r\n          return prev + 1;\r\n        });\r\n      }, 1000);\r\n    } else {\r\n      if (recordingTimerRef.current) {\r\n        clearInterval(recordingTimerRef.current);\r\n      }\r\n    }\r\n    \r\n    return () => {\r\n      if (recordingTimerRef.current) {\r\n        clearInterval(recordingTimerRef.current);\r\n      }\r\n    };\r\n  }, [isRecording]);\r\n\r\n  // Video playback effect\r\n  useEffect(() => {\r\n    let playbackInterval;\r\n    if (isPlaying && frameBuffer.length > 0) {\r\n      playbackInterval = setInterval(() => {\r\n        setCurrentFrameIndex(prev => {\r\n          if (prev < frameBuffer.length - 1) {\r\n            return prev + 1;\r\n          } else {\r\n            setIsPlaying(false);\r\n            return prev;\r\n          }\r\n        });\r\n      }, 1000 / PLAYBACK_FPS);\r\n    }\r\n    return () => {\r\n      if (playbackInterval) clearInterval(playbackInterval);\r\n    };\r\n  }, [isPlaying, frameBuffer]);\r\n\r\n  // Update processed video when frame changes\r\n  useEffect(() => {\r\n    if (frameBuffer.length > 0 && currentFrameIndex < frameBuffer.length) {\r\n      setProcessedVideo(frameBuffer[currentFrameIndex]);\r\n    }\r\n  }, [currentFrameIndex, frameBuffer]);\r\n\r\n  // When videoPreview is set, extract duration and FPS\r\n  useEffect(() => {\r\n    if (videoPreview) {\r\n      const video = document.createElement('video');\r\n      video.src = videoPreview;\r\n      video.preload = 'metadata';\r\n      video.onloadedmetadata = () => {\r\n        setVideoDuration(video.duration);\r\n        // Try to get FPS from video tracks if available\r\n        if (video.webkitVideoDecodedByteCount !== undefined) {\r\n          // Not standard, but some browsers may expose frameRate\r\n          try {\r\n            const tracks = video.videoTracks || (video.captureStream && video.captureStream().getVideoTracks());\r\n            if (tracks && tracks.length > 0 && tracks[0].getSettings) {\r\n              const settings = tracks[0].getSettings();\r\n              if (settings.frameRate) {\r\n                setVideoFPS(settings.frameRate);\r\n              }\r\n            }\r\n          } catch (e) {}\r\n        }\r\n      };\r\n    }\r\n  }, [videoPreview]);\r\n\r\n  // Helper to estimate total frames if backend total_frames is invalid\r\n  const estimatedTotalFrames = videoDuration && videoFPS ? Math.round(videoDuration * videoFPS) : null;\r\n\r\n  // Add cleanup effect for SSE stream\r\n  useEffect(() => {\r\n    return () => {\r\n      if (streamRef.current) {\r\n        streamRef.current.abort();\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  const [warning, setWarning] = useState('');\r\n\r\n  // Handle video file selection\r\n  const handleVideoChange = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      // Validate video file first\r\n      const validation = validateUploadFile(file, 'video', 'video_defect_detection');\r\n      if (!validation.isValid) {\r\n        showFileValidationError(validation.errorMessage, setError);\r\n        // Clear the file input\r\n        if (e.target) {\r\n          e.target.value = '';\r\n        }\r\n        return;\r\n      }\r\n\r\n      // Clear any previous errors\r\n      setError('');\r\n\r\n      const video = document.createElement('video');\r\n      video.preload = 'metadata';\r\n      video.onloadedmetadata = () => {\r\n        if (video.duration > 60) {\r\n          setWarning('Video upload is restricted to 1 minute. Please select a shorter video.');\r\n          setVideoFile(null);\r\n          setVideoPreview(null);\r\n          setProcessedVideo(null);\r\n          setVideoResults(null);\r\n          setAllDetections([]);\r\n          setError('');\r\n          if (fileInputRef.current) fileInputRef.current.value = '';\r\n        } else {\r\n          setWarning('');\r\n          setVideoFile(file);\r\n          setVideoPreview(URL.createObjectURL(file));\r\n          setProcessedVideo(null);\r\n          setVideoResults(null);\r\n          setAllDetections([]);\r\n          setError('');\r\n        }\r\n      };\r\n      video.src = URL.createObjectURL(file);\r\n    }\r\n  };\r\n\r\n  // Handle camera activation\r\n  const toggleCamera = () => {\r\n    setCameraActive(!cameraActive);\r\n    if (!cameraActive) {\r\n      setVideoFile(null);\r\n      setVideoPreview(null);\r\n      setProcessedVideo(null);\r\n      setVideoResults(null);\r\n      setAllDetections([]);\r\n      setError('');\r\n    }\r\n  };\r\n\r\n  // Start recording\r\n  const handleStartRecording = async () => {\r\n    console.log('handleStartRecording called');\r\n    if (!webcamRef.current || !webcamRef.current.stream) {\r\n      setError('Camera not available');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setRecordedChunks([]);\r\n      recordedChunksRef.current = [];\r\n      setRecordingTime(0);\r\n      setIsRecording(true);\r\n      setError('');\r\n\r\n      const mediaRecorder = new MediaRecorder(webcamRef.current.stream, {\r\n        mimeType: 'video/webm'\r\n      });\r\n\r\n      mediaRecorderRef.current = mediaRecorder;\r\n\r\n      mediaRecorder.ondataavailable = (event) => {\r\n        console.log('ondataavailable fired, size:', event.data.size);\r\n        if (event.data.size > 0) {\r\n          setRecordedChunks(prev => {\r\n            const updated = [...prev, event.data];\r\n            recordedChunksRef.current = updated; // <-- Keep ref in sync\r\n            return updated;\r\n          });\r\n        }\r\n      };\r\n\r\n      mediaRecorder.onstop = () => {\r\n        console.log('mediaRecorder.onstop fired');\r\n        // Use the ref to get the latest chunks\r\n        const chunks = recordedChunksRef.current;\r\n        // Debug logs\r\n        console.log('onstop: recordedChunks length:', chunks.length);\r\n        let totalSize = 0;\r\n        chunks.forEach((c, i) => {\r\n          console.log(`Chunk ${i} size:`, c.size);\r\n          totalSize += c.size;\r\n        });\r\n        console.log('Total recorded size:', totalSize);\r\n        const blob = new Blob(chunks, { type: 'video/webm' });\r\n        const file = new File([blob], `recorded_video_${Date.now()}.webm`, { type: 'video/webm' });\r\n        setVideoFile(file);\r\n        setVideoPreview(URL.createObjectURL(blob));\r\n        setIsRecording(false);\r\n        setRecordingTime(0);\r\n        // Reset the ref and state for next recording\r\n        recordedChunksRef.current = [];\r\n        setRecordedChunks([]);\r\n      };\r\n\r\n      mediaRecorder.onstart = () => {\r\n        console.log('mediaRecorder.onstart fired');\r\n      };\r\n      mediaRecorder.onerror = (e) => {\r\n        console.error('mediaRecorder.onerror', e);\r\n      };\r\n\r\n      console.log('Calling mediaRecorder.start(1000)');\r\n      mediaRecorder.start(1000); // timeslice: 1000ms\r\n    } catch (error) {\r\n      setError('Failed to start recording: ' + error.message);\r\n      setIsRecording(false);\r\n    }\r\n  };\r\n\r\n  // Stop recording\r\n  const handleStopRecording = () => {\r\n    console.log('handleStopRecording called');\r\n    if (mediaRecorderRef.current && isRecording) {\r\n      mediaRecorderRef.current.stop();\r\n      setIsRecording(false);\r\n      setRecordingTime(0);\r\n    }\r\n  };\r\n\r\n  // Toggle camera orientation\r\n  const toggleCameraOrientation = () => {\r\n    setCameraOrientation(prev => prev === 'environment' ? 'user' : 'environment');\r\n  };\r\n\r\n  // Check if ready for processing\r\n  const isReadyForProcessing = () => {\r\n    return (inputSource === 'video' && videoFile) || \r\n           (inputSource === 'camera' && videoFile);\r\n  };\r\n\r\n  // Handle video processing\r\n  const handleProcess = async () => {\r\n    if (!isReadyForProcessing()) {\r\n      setError('Please provide a video file first');\r\n      return;\r\n    }\r\n\r\n    // Reset states\r\n    setLoading(true);\r\n    setError('');\r\n    setIsProcessing(true);\r\n    setShouldStop(false);\r\n    setIsBuffering(true);\r\n    setIsPlaying(false);\r\n    setFrameBuffer([]);\r\n    setCurrentFrameIndex(0);\r\n    setProcessingProgress(0);\r\n    setAllDetections([]);\r\n    setCurrentDetections([]);\r\n    setProcessedVideo(null);\r\n    setVideoResults(null);\r\n    setShowResults(false);\r\n\r\n    // Create abort controller for cleanup\r\n    streamRef.current = new AbortController();\r\n\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('video', videoFile);\r\n      formData.append('selectedModel', selectedModel);\r\n      formData.append('coordinates', coordinates);\r\n      const userString = sessionStorage.getItem('user');\r\n      const user = userString ? JSON.parse(userString) : null;\r\n      formData.append('username', user?.username || 'Unknown');\r\n      formData.append('role', user?.role || 'Unknown');\r\n\r\n      console.log('Starting video processing with model:', selectedModel);\r\n\r\n      const sseUrl = '/api/pavement/detect-video';\r\n      \r\n      const response = await fetch(sseUrl, {\r\n        method: 'POST',\r\n        body: formData,\r\n        signal: streamRef.current.signal\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n      }\r\n\r\n      const reader = response.body.getReader();\r\n      const decoder = new TextDecoder();\r\n      let buffer = '';\r\n\r\n      // Helper to accumulate detections\r\n      const appendDetections = (detections) => {\r\n        if (detections && Array.isArray(detections) && detections.length > 0) {\r\n          setAllDetections(prev => [...prev, ...detections]);\r\n        }\r\n      };\r\n\r\n      const processStream = async () => {\r\n        try {\r\n          while (true) {\r\n            const { done, value } = await reader.read();\r\n            if (done) {\r\n              console.log('Stream ended naturally');\r\n              setIsProcessing(false);\r\n              setLoading(false);\r\n              setIsBuffering(false);\r\n              break;\r\n            }\r\n\r\n            buffer += decoder.decode(value, { stream: true });\r\n            let lines = buffer.split('\\n');\r\n            // Keep the last line in buffer if it's incomplete\r\n            buffer = lines.pop();\r\n\r\n            for (const line of lines) {\r\n              if (line.startsWith('data: ')) {\r\n                try {\r\n                  const data = JSON.parse(line.substring(6));\r\n                  // Debug log for every SSE message\r\n                  console.log('SSE data:', data);\r\n\r\n                  // Handle error case\r\n                  if (data.success === false) {\r\n                    setError(data.message || 'Video processing failed');\r\n                    setIsProcessing(false);\r\n                    setLoading(false);\r\n                    setIsBuffering(false);\r\n                    return;\r\n                  }\r\n\r\n                  // Update progress immediately\r\n                  if (data.progress !== undefined && totalFramesValid) {\r\n                    setProcessingProgress(data.progress);\r\n                    if (!showResults && data.progress > 0) {\r\n                      setShowResults(true);\r\n                    }\r\n                  } else if (data.frame_count !== undefined && totalFramesValid) {\r\n                    // Calculate progress if not provided but backend totalFrames is valid\r\n                    const progress = (data.frame_count / totalFrames) * 100;\r\n                    setProcessingProgress(progress);\r\n                    if (!showResults && progress > 0) {\r\n                      setShowResults(true);\r\n                    }\r\n                  } else if (data.frame_count !== undefined && estimatedTotalFrames) {\r\n                    // Fallback: use estimated total frames from duration and FPS\r\n                    const progress = (data.frame_count / estimatedTotalFrames) * 100;\r\n                    setProcessingProgress(progress);\r\n                    if (!showResults && progress > 0) {\r\n                      setShowResults(true);\r\n                    }\r\n                  }\r\n\r\n                  // Update frame display immediately\r\n                  if (data.frame && typeof data.frame === 'string' && data.frame.length > 1000) {\r\n                    setFrameBuffer(prev => {\r\n                      const newBuffer = [...prev, data.frame];\r\n                      return newBuffer;\r\n                    });\r\n                    setProcessedVideo(data.frame);\r\n                    setCurrentFrameIndex(prev => prev + 1);\r\n                    if (isBuffering) {\r\n                      setIsBuffering(false);\r\n                    }\r\n                  }\r\n\r\n                  // Accumulate detections per frame\r\n                  if (data.detections && data.detections.length > 0) {\r\n                    setCurrentDetections(data.detections);\r\n                    appendDetections(data.detections);\r\n                  }\r\n\r\n                  // Handle final results\r\n                  if (data.all_detections) {\r\n                    setVideoResults(data);\r\n                    setAllDetections(data.all_detections);\r\n                    setIsProcessing(false);\r\n                    setLoading(false);\r\n                    setIsBuffering(false);\r\n                    setProcessingProgress(100);\r\n                    setCurrentFrameIndex(0);\r\n                    setIsPlaying(false);\r\n                    console.log('Video processing completed');\r\n                    return;\r\n                  }\r\n\r\n                  // Handle end signal\r\n                  if (data.end) {\r\n                    console.log('Received end signal');\r\n                    setIsProcessing(false);\r\n                    setLoading(false);\r\n                    setIsBuffering(false);\r\n                    return;\r\n                  }\r\n\r\n                  // Update totalFrames when receiving SSE data:\r\n                  if (data.total_frames !== undefined) {\r\n                    setTotalFrames(data.total_frames);\r\n                  }\r\n                } catch (parseError) {\r\n                  console.warn('Error parsing SSE data:', parseError);\r\n                }\r\n              }\r\n            }\r\n          }\r\n        } catch (streamError) {\r\n          if (streamError.name === 'AbortError') {\r\n            console.log('Stream aborted by user');\r\n          } else {\r\n            console.error('Stream processing error:', streamError);\r\n            setError('Error processing video stream');\r\n          }\r\n          setIsProcessing(false);\r\n          setLoading(false);\r\n          setIsBuffering(false);\r\n        } finally {\r\n          if (reader) {\r\n            try {\r\n              reader.releaseLock();\r\n            } catch (e) {\r\n              console.warn('Error releasing reader lock:', e);\r\n            }\r\n          }\r\n        }\r\n      };\r\n\r\n      await processStream();\r\n    } catch (error) {\r\n      console.error('Video processing error:', error);\r\n      setError(error.message || 'Video processing failed');\r\n      setLoading(false);\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  // Stop processing\r\n  const handleStopProcessing = async () => {\r\n    try {\r\n      await axios.post('/api/pavement/stop-video-processing');\r\n      \r\n      setIsProcessing(false);\r\n      setShouldStop(true);\r\n      setIsBuffering(false);\r\n      setIsPlaying(false);\r\n      setLoading(false);\r\n      setError('Video processing stopped');\r\n    } catch (error) {\r\n      console.error('Error stopping processing:', error);\r\n      setError('Failed to stop processing');\r\n    }\r\n  };\r\n\r\n  // Reset all\r\n  const handleReset = () => {\r\n    setVideoFile(null);\r\n    setVideoPreview(null);\r\n    setProcessedVideo(null);\r\n    setVideoResults(null);\r\n    setAllDetections([]);\r\n    setCurrentDetections([]);\r\n    setFrameBuffer([]);\r\n    setCurrentFrameIndex(0);\r\n    setIsProcessing(false);\r\n    setShouldStop(false);\r\n    setIsBuffering(false);\r\n    setIsPlaying(false);\r\n    setProcessingProgress(0);\r\n    setError('');\r\n    setSelectedModel('All');\r\n    setShowResults(false); // <-- Ensure table is hidden after reset\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  // Playback controls\r\n  const handlePlayPause = () => setIsPlaying(!isPlaying);\r\n  const handleRewind = () => setCurrentFrameIndex(Math.max(currentFrameIndex - 5, 0));\r\n  const handleForward = () => setCurrentFrameIndex(Math.min(currentFrameIndex + 5, frameBuffer.length - 1));\r\n\r\n  // Group detections by type\r\n  const getDetectionSummary = () => {\r\n    const summary = {};\r\n    allDetections.forEach(det => {\r\n      summary[det.type] = (summary[det.type] || 0) + 1;\r\n    });\r\n    return summary;\r\n  };\r\n\r\n  // Get tracking statistics\r\n  const getTrackingStats = () => {\r\n    if (videoResults) {\r\n      return {\r\n        uniqueDetections: videoResults.total_unique_detections || allDetections.length,\r\n        frameDetections: videoResults.total_frame_detections || allDetections.length,\r\n        duplicatesRemoved: (videoResults.total_frame_detections || allDetections.length) - (videoResults.total_unique_detections || allDetections.length)\r\n      };\r\n    }\r\n    return {\r\n      uniqueDetections: allDetections.length,\r\n      frameDetections: allDetections.length,\r\n      duplicatesRemoved: 0\r\n    };\r\n  };\r\n\r\n  // Format time\r\n  const formatTime = (seconds) => {\r\n    const mins = Math.floor(seconds / 60);\r\n    const secs = seconds % 60;\r\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  return (\r\n    <div className=\"video-defect-detection\">\r\n      <Row>\r\n        <Col md={6}>\r\n          <Card className=\"mb-4\">\r\n            <Card.Header className=\"bg-primary text-white\">\r\n              <h5 className=\"mb-0\">Video Defect Detection</h5>\r\n            </Card.Header>\r\n            <Card.Body>\r\n              {error && (\r\n                <Alert variant=\"danger\" className=\"mb-3\">\r\n                  {error}\r\n                </Alert>\r\n              )}\r\n              {warning && (\r\n                <Alert variant=\"warning\" className=\"mb-3\">\r\n                  {warning}\r\n                </Alert>\r\n              )}\r\n\r\n              {/* Model Selection */}\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Detection Model</Form.Label>\r\n                <Form.Select\r\n                  value={selectedModel}\r\n                  onChange={(e) => setSelectedModel(e.target.value)}\r\n                  disabled={isProcessing}\r\n                >\r\n                  {modelOptions.map(option => (\r\n                    <option key={option.value} value={option.value}>\r\n                      {option.label}\r\n                    </option>\r\n                  ))}\r\n                </Form.Select>\r\n              </Form.Group>\r\n\r\n              {/* Input Source Selection */}\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Input Source</Form.Label>\r\n                <Form.Select\r\n                  value={inputSource}\r\n                  onChange={(e) => setInputSource(e.target.value)}\r\n                  disabled={isProcessing}\r\n                >\r\n                  <option value=\"video\">Video Upload</option>\r\n                  <option value=\"camera\">Live Camera Recording</option>\r\n                </Form.Select>\r\n              </Form.Group>\r\n\r\n              {/* Video Upload */}\r\n              {inputSource === 'video' && (\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Upload Video</Form.Label>\r\n                  <Form.Control\r\n                    type=\"file\"\r\n                    accept=\"video/*\"\r\n                    onChange={handleVideoChange}\r\n                    ref={fileInputRef}\r\n                    disabled={isProcessing}\r\n                  />\r\n                  {videoPreview && (\r\n                    <div className=\"mt-3\">\r\n                      <video\r\n                        src={videoPreview}\r\n                        controls\r\n                        className=\"video-preview\"\r\n                        style={{ maxHeight: '200px' }}\r\n                      />\r\n                    </div>\r\n                  )}\r\n                </Form.Group>\r\n              )}\r\n\r\n              {/* Camera Recording */}\r\n              {inputSource === 'camera' && (\r\n                <div className=\"mb-3\">\r\n                  <div className=\"d-flex gap-2 mb-2\">\r\n                    <Button\r\n                      variant={cameraActive ? \"danger\" : \"info\"}\r\n                      onClick={toggleCamera}\r\n                      disabled={isProcessing}\r\n                    >\r\n                      {cameraActive ? 'Stop Camera' : 'Start Camera'}\r\n                    </Button>\r\n                    {isMobile && cameraActive && (\r\n                      <Button\r\n                        variant=\"outline-secondary\"\r\n                        onClick={toggleCameraOrientation}\r\n                        size=\"sm\"\r\n                      >\r\n                        Rotate Camera\r\n                      </Button>\r\n                    )}\r\n                  </div>\r\n\r\n                  {cameraActive && (\r\n                    <div className=\"webcam-container\">\r\n                      <Webcam\r\n                        audio={false}\r\n                        ref={webcamRef}\r\n                        screenshotFormat=\"image/jpeg\"\r\n                        width=\"100%\"\r\n                        height=\"auto\"\r\n                        videoConstraints={{\r\n                          width: 640,\r\n                          height: 480,\r\n                          facingMode: cameraOrientation\r\n                        }}\r\n                      />\r\n                      \r\n                      <div className=\"mt-2\">\r\n                        {!isRecording ? (\r\n                          <Button\r\n                            variant=\"success\"\r\n                            onClick={handleStartRecording}\r\n                            disabled={isProcessing}\r\n                          >\r\n                            Start Recording\r\n                          </Button>\r\n                        ) : (\r\n                          <div className=\"d-flex align-items-center gap-2\">\r\n                            <Button\r\n                              variant=\"danger\"\r\n                              onClick={handleStopRecording}\r\n                            >\r\n                              Stop Recording\r\n                            </Button>\r\n                            <span className=\"text-danger\">\r\n                              Recording: {formatTime(recordingTime)} / {formatTime(MAX_RECORDING_TIME)}\r\n                            </span>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {videoPreview && (\r\n                    <div className=\"mt-3\">\r\n                      <video\r\n                        src={videoPreview}\r\n                        controls\r\n                        className=\"video-preview\"\r\n                        style={{ maxHeight: '200px' }}\r\n                      />\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {/* Action Buttons */}\r\n              <div className=\"action-buttons\">\r\n                <Button\r\n                  variant=\"primary\"\r\n                  onClick={handleProcess}\r\n                  disabled={!isReadyForProcessing() || isProcessing}\r\n                >\r\n                  {loading ? (\r\n                    <>\r\n                      <Spinner size=\"sm\" className=\"me-2\" />\r\n                      Processing...\r\n                    </>\r\n                  ) : (\r\n                    'Process Video'\r\n                  )}\r\n                </Button>\r\n                \r\n                {isProcessing && (\r\n                  <Button\r\n                    variant=\"warning\"\r\n                    onClick={handleStopProcessing}\r\n                  >\r\n                    Stop Processing\r\n                  </Button>\r\n                )}\r\n                \r\n                <Button\r\n                  variant=\"secondary\"\r\n                  onClick={handleReset}\r\n                  disabled={isProcessing}\r\n                >\r\n                  Reset\r\n                </Button>\r\n              </div>\r\n\r\n              {/* Always show progress during processing */}\r\n              {isProcessing && (\r\n                <div className=\"mt-3\">\r\n                  {(totalFramesValid || estimatedTotalFrames) && processingProgress > 0 && Number.isFinite(processingProgress) ? (\r\n                    <>\r\n                      <div className=\"d-flex justify-content-between\">\r\n                        <span>Processing Progress:</span>\r\n                        <span>{Math.max(0, Math.min(100, processingProgress)).toFixed(1)}%</span>\r\n                      </div>\r\n                      <div className=\"progress mt-1\">\r\n                        <div\r\n                          className=\"progress-bar progress-bar-striped progress-bar-animated\"\r\n                          role=\"progressbar\"\r\n                          style={{ width: `${Math.max(0, Math.min(100, processingProgress))}%` }}\r\n                          aria-valuenow={Math.max(0, Math.min(100, processingProgress))}\r\n                          aria-valuemin=\"0\"\r\n                          aria-valuemax=\"100\"\r\n                        ></div>\r\n                      </div>\r\n                    </>\r\n                  ) : (\r\n                    <div className=\"d-flex align-items-center mt-3\">\r\n                      <span>Processing...</span>\r\n                      <div className=\"progress flex-grow-1 ms-2\" style={{ height: '20px' }}>\r\n                        <div\r\n                          className=\"progress-bar progress-bar-striped progress-bar-animated\"\r\n                          role=\"progressbar\"\r\n                          style={{ width: `100%`, backgroundColor: '#e0e0e0' }}\r\n                          aria-valuenow={0}\r\n                          aria-valuemin=\"0\"\r\n                          aria-valuemax=\"100\"\r\n                        ></div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n\r\n        <Col md={6}>\r\n          {/* Show detection results as soon as we have any, and always after processing is complete if results exist */}\r\n          {((showResults || allDetections.length > 0 || (!isProcessing && videoResults && allDetections.length > 0))) && (\r\n            <Card>\r\n              <Card.Header className=\"bg-info text-white\">\r\n                <h5 className=\"mb-0\">Detection Results</h5>\r\n                {isProcessing && (\r\n                  <small className=\"text-white-50\">\r\n                    Results update in real-time as processing continues...\r\n                  </small>\r\n                )}\r\n                {!isProcessing && videoResults && (\r\n                  <small className=\"text-success\">\r\n                    <b>Processing Complete.</b> Final results are shown below.\r\n                  </small>\r\n                )}\r\n              </Card.Header>\r\n              <Card.Body>\r\n                {/* Summary */}\r\n                <div className=\"detection-summary mb-3\">\r\n                  <h6>Detection Summary:</h6>\r\n                  <div className=\"mb-2\">\r\n                    {Object.entries(getDetectionSummary()).map(([type, count]) => (\r\n                      <span key={type} className=\"badge bg-secondary me-1\">\r\n                        {type}: {count}\r\n                      </span>\r\n                    ))}\r\n                  </div>\r\n                  \r\n                  {/* Tracking Statistics */}\r\n                  <div className=\"tracking-stats\">\r\n                    <small className=\"text-muted\">\r\n                      <strong>Tracking Stats:</strong> {' '}\r\n                      <span className=\"badge bg-success me-1\">\r\n                        Unique: {getTrackingStats().uniqueDetections}\r\n                      </span>\r\n                      <span className=\"badge bg-info me-1\">\r\n                        Total Frames: {getTrackingStats().frameDetections}\r\n                      </span>\r\n                      <span className=\"badge bg-warning\">\r\n                        Duplicates Removed: {getTrackingStats().duplicatesRemoved}\r\n                      </span>\r\n                    </small>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Separate Tables for Each Defect Type */}\r\n                {(() => {\r\n                  const potholeDetections = allDetections.filter(d => d.type === 'Pothole');\r\n                  const crackDetections = allDetections.filter(d => d.type.includes('Crack'));\r\n                  const kerbDetections = allDetections.filter(d => d.type.includes('Kerb'));\r\n\r\n                  return (\r\n                    <div>\r\n                      {/* Pothole Table - Show only if \"All\" or \"Potholes\" is selected */}\r\n                      {(selectedModel === 'All' || selectedModel === 'Potholes') && (\r\n                        <div className=\"defect-section potholes mb-4\">\r\n                          <h6 className=\"text-danger\">\r\n                            <span className=\"emoji\">🕳️</span>\r\n                            Potholes Detected: {potholeDetections.length}\r\n                          </h6>\r\n                          {potholeDetections.length > 0 ? (\r\n                            <div className=\"detection-table-container\">\r\n                              <Table striped bordered hover size=\"sm\">\r\n                                <thead>\r\n                                  <tr>\r\n                                    <th>ID</th>\r\n                                    <th>Area (cm²)</th>\r\n                                    <th>Depth (cm)</th>\r\n                                    <th>Volume (cm³)</th>\r\n                                    <th>Volume Range</th>\r\n                                  </tr>\r\n                                </thead>\r\n                                <tbody>\r\n                                  {potholeDetections.map((detection, index) => (\r\n                                    <tr key={index}>\r\n                                      <td>{detection.track_id || index + 1}</td>\r\n                                      <td>{detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'}</td>\r\n                                      <td>{detection.depth_cm ? detection.depth_cm.toFixed(2) : 'N/A'}</td>\r\n                                      <td>{detection.volume ? detection.volume.toFixed(2) : 'N/A'}</td>\r\n                                      <td>{detection.volume_range || 'N/A'}</td>\r\n                                    </tr>\r\n                                  ))}\r\n                                </tbody>\r\n                              </Table>\r\n                            </div>\r\n                          ) : (\r\n                            <div className=\"no-defects-message\">No potholes detected</div>\r\n                          )}\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Cracks Table - Show only if \"All\" or \"Alligator Cracks\" is selected */}\r\n                      {(selectedModel === 'All' || selectedModel === 'Alligator Cracks') && (\r\n                        <div className=\"defect-section cracks mb-4\">\r\n                          <h6 className=\"text-success\">\r\n                            <span className=\"emoji\">🪨</span>\r\n                            Cracks Detected: {crackDetections.length}\r\n                          </h6>\r\n                          {crackDetections.length > 0 ? (\r\n                            <div className=\"detection-table-container\">\r\n                              <Table striped bordered hover size=\"sm\">\r\n                                <thead>\r\n                                  <tr>\r\n                                    <th>ID</th>\r\n                                    <th>Type</th>\r\n                                    <th>Area (cm²)</th>\r\n                                    <th>Area Range</th>\r\n                                  </tr>\r\n                                </thead>\r\n                                <tbody>\r\n                                  {crackDetections.map((detection, index) => (\r\n                                    <tr key={index}>\r\n                                      <td>{detection.track_id || index + 1}</td>\r\n                                      <td>{detection.type}</td>\r\n                                      <td>{detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'}</td>\r\n                                      <td>{detection.area_range || 'N/A'}</td>\r\n                                    </tr>\r\n                                  ))}\r\n                                </tbody>\r\n                              </Table>\r\n                            </div>\r\n                          ) : (\r\n                            <div className=\"no-defects-message\">No cracks detected</div>\r\n                          )}\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Kerbs Table - Show only if \"All\" or \"Kerbs\" is selected */}\r\n                      {(selectedModel === 'All' || selectedModel === 'Kerbs') && (\r\n                        <div className=\"defect-section kerbs mb-4\">\r\n                          <h6 className=\"text-primary\">\r\n                            <span className=\"emoji\">🚧</span>\r\n                            Kerbs Detected: {kerbDetections.length}\r\n                          </h6>\r\n                          {kerbDetections.length > 0 ? (\r\n                            <div className=\"detection-table-container\">\r\n                              <Table striped bordered hover size=\"sm\">\r\n                                <thead>\r\n                                  <tr>\r\n                                    <th>ID</th>\r\n                                    <th>Type</th>\r\n                                    <th>Condition</th>\r\n                                    <th>Length</th>\r\n                                  </tr>\r\n                                </thead>\r\n                                <tbody>\r\n                                  {kerbDetections.map((detection, index) => (\r\n                                    <tr key={index}>\r\n                                      <td>{detection.track_id || index + 1}</td>\r\n                                      <td>{detection.kerb_type || 'Concrete Kerb'}</td>\r\n                                      <td>{detection.condition || detection.type}</td>\r\n                                      <td>{detection.length_m ? detection.length_m.toFixed(2) : 'N/A'}</td>\r\n                                    </tr>\r\n                                  ))}\r\n                                </tbody>\r\n                              </Table>\r\n                            </div>\r\n                          ) : (\r\n                            <div className=\"no-defects-message\">No kerbs detected</div>\r\n                          )}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  );\r\n                })()}\r\n              </Card.Body>\r\n            </Card>\r\n          )}\r\n        </Col>\r\n      </Row>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default VideoDefectDetection; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,OAASC,IAAI,CAAEC,MAAM,CAAEC,IAAI,CAAEC,KAAK,CAAEC,OAAO,CAAEC,KAAK,CAAEC,GAAG,CAAEC,GAAG,KAAQ,iBAAiB,CACrF,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,cAAc,CACjC,MAAO,CAAAC,aAAa,KAAM,wBAAwB,CAClD,MAAO,4BAA4B,CACnC,OAASC,kBAAkB,CAAEC,uBAAuB,KAAQ,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEtF,KAAM,CAAAC,oBAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAGxB,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACyB,SAAS,CAAEC,YAAY,CAAC,CAAG1B,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAAC2B,YAAY,CAAEC,eAAe,CAAC,CAAG5B,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAAC6B,cAAc,CAAEC,iBAAiB,CAAC,CAAG9B,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAAC+B,OAAO,CAAEC,UAAU,CAAC,CAAGhC,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACiC,KAAK,CAAEC,QAAQ,CAAC,CAAGlC,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACmC,YAAY,CAAEC,eAAe,CAAC,CAAGpC,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACqC,UAAU,CAAEC,aAAa,CAAC,CAAGtC,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACuC,WAAW,CAAEC,cAAc,CAAC,CAAGxC,QAAQ,CAAC,eAAe,CAAC,CAC/D,KAAM,CAACyC,WAAW,CAAEC,cAAc,CAAC,CAAG1C,QAAQ,CAAC,OAAO,CAAC,CACvD,KAAM,CAAC2C,YAAY,CAAEC,eAAe,CAAC,CAAG5C,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC6C,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG9C,QAAQ,CAAC,aAAa,CAAC,CACzE,KAAM,CAAC+C,WAAW,CAAEC,cAAc,CAAC,CAAGhD,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACiD,aAAa,CAAEC,gBAAgB,CAAC,CAAGlD,QAAQ,CAAC,CAAC,CAAC,CACrD,KAAM,CAACmD,cAAc,CAAEC,iBAAiB,CAAC,CAAGpD,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAAqD,iBAAiB,CAAGpD,MAAM,CAAC,EAAE,CAAC,CAAE;AAEtC;AACA,KAAM,CAACqD,WAAW,CAAEC,cAAc,CAAC,CAAGvD,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACwD,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGzD,QAAQ,CAAC,CAAC,CAAC,CAC7D,KAAM,CAAC0D,WAAW,CAAEC,cAAc,CAAC,CAAG3D,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAAC4D,SAAS,CAAEC,YAAY,CAAC,CAAG7D,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC8D,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG/D,QAAQ,CAAC,CAAC,CAAC,CAC/D,KAAM,CAACgE,aAAa,CAAEC,gBAAgB,CAAC,CAAGjE,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACkE,YAAY,CAAEC,eAAe,CAAC,CAAGnE,QAAQ,CAAC,IAAI,CAAC,CAEtD;AACA,KAAM,CAACoE,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGrE,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACsE,WAAW,CAAEC,cAAc,CAAC,CAAGvE,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAAAwE,SAAS,CAAGvE,MAAM,CAAC,IAAI,CAAC,CAE9B,KAAM,CAAAwE,SAAS,CAAGxE,MAAM,CAAC,IAAI,CAAC,CAC9B,KAAM,CAAAyE,YAAY,CAAGzE,MAAM,CAAC,IAAI,CAAC,CACjC,KAAM,CAAA0E,iBAAiB,CAAG1E,MAAM,CAAC,IAAI,CAAC,CACtC,KAAM,CAAA2E,gBAAgB,CAAG3E,MAAM,CAAC,IAAI,CAAC,CACrC,KAAM,CAAE4E,QAAS,CAAC,CAAGhE,aAAa,CAAC,CAAC,CAEpC,KAAM,CAAAiE,WAAW,CAAG,EAAE,CACtB,KAAM,CAAAC,YAAY,CAAG,EAAE,CACvB,KAAM,CAAAC,kBAAkB,CAAG,EAAE,CAAE;AAE/B,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGlF,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAAAmF,gBAAgB,CAAGC,MAAM,CAACC,QAAQ,CAACJ,WAAW,CAAC,EAAIA,WAAW,CAAG,CAAC,CAExE,KAAM,CAACK,aAAa,CAAEC,gBAAgB,CAAC,CAAGvF,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAACwF,QAAQ,CAAEC,WAAW,CAAC,CAAGzF,QAAQ,CAAC,EAAE,CAAC,CAAE;AAE9C;AACA,KAAM,CAAA0F,YAAY,CAAG,CACnB,CAAEC,KAAK,CAAE,KAAK,CAAEC,KAAK,CAAE,mCAAoC,CAAC,CAC5D,CAAED,KAAK,CAAE,UAAU,CAAEC,KAAK,CAAE,UAAW,CAAC,CACxC,CAAED,KAAK,CAAE,kBAAkB,CAAEC,KAAK,CAAE,kBAAmB,CAAC,CACxD,CAAED,KAAK,CAAE,OAAO,CAAEC,KAAK,CAAE,OAAQ,CAAC,CACnC,CAED;AACA1F,SAAS,CAAC,IAAM,CACd,GAAI2F,SAAS,CAACC,WAAW,CAAE,CACzBD,SAAS,CAACC,WAAW,CAACC,kBAAkB,CACrCC,QAAQ,EAAK,CACZ,KAAM,CAAEC,QAAQ,CAAEC,SAAU,CAAC,CAAGF,QAAQ,CAACG,MAAM,CAC/C3D,cAAc,CAAC,GAAGyD,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,KAAKF,SAAS,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CACnE,CAAC,CACAC,GAAG,EAAK,CACPC,OAAO,CAACrE,KAAK,CAAC,yBAAyB,CAAEoE,GAAG,CAAC,CAC7C7D,cAAc,CAAC,sBAAsB,CAAC,CACxC,CACF,CAAC,CACH,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACAtC,SAAS,CAAC,IAAM,CACd,GAAI6C,WAAW,CAAE,CACf4B,iBAAiB,CAAC4B,OAAO,CAAGC,WAAW,CAAC,IAAM,CAC5CtD,gBAAgB,CAACuD,IAAI,EAAI,CACvB,GAAIA,IAAI,EAAIzB,kBAAkB,CAAE,CAC9B0B,mBAAmB,CAAC,CAAC,CACrB,MAAO,CAAA1B,kBAAkB,CAC3B,CACA,MAAO,CAAAyB,IAAI,CAAG,CAAC,CACjB,CAAC,CAAC,CACJ,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,IAAM,CACL,GAAI9B,iBAAiB,CAAC4B,OAAO,CAAE,CAC7BI,aAAa,CAAChC,iBAAiB,CAAC4B,OAAO,CAAC,CAC1C,CACF,CAEA,MAAO,IAAM,CACX,GAAI5B,iBAAiB,CAAC4B,OAAO,CAAE,CAC7BI,aAAa,CAAChC,iBAAiB,CAAC4B,OAAO,CAAC,CAC1C,CACF,CAAC,CACH,CAAC,CAAE,CAACxD,WAAW,CAAC,CAAC,CAEjB;AACA7C,SAAS,CAAC,IAAM,CACd,GAAI,CAAA0G,gBAAgB,CACpB,GAAIhD,SAAS,EAAIN,WAAW,CAACuD,MAAM,CAAG,CAAC,CAAE,CACvCD,gBAAgB,CAAGJ,WAAW,CAAC,IAAM,CACnC/C,oBAAoB,CAACgD,IAAI,EAAI,CAC3B,GAAIA,IAAI,CAAGnD,WAAW,CAACuD,MAAM,CAAG,CAAC,CAAE,CACjC,MAAO,CAAAJ,IAAI,CAAG,CAAC,CACjB,CAAC,IAAM,CACL5C,YAAY,CAAC,KAAK,CAAC,CACnB,MAAO,CAAA4C,IAAI,CACb,CACF,CAAC,CAAC,CACJ,CAAC,CAAE,IAAI,CAAG1B,YAAY,CAAC,CACzB,CACA,MAAO,IAAM,CACX,GAAI6B,gBAAgB,CAAED,aAAa,CAACC,gBAAgB,CAAC,CACvD,CAAC,CACH,CAAC,CAAE,CAAChD,SAAS,CAAEN,WAAW,CAAC,CAAC,CAE5B;AACApD,SAAS,CAAC,IAAM,CACd,GAAIoD,WAAW,CAACuD,MAAM,CAAG,CAAC,EAAIrD,iBAAiB,CAAGF,WAAW,CAACuD,MAAM,CAAE,CACpE/E,iBAAiB,CAACwB,WAAW,CAACE,iBAAiB,CAAC,CAAC,CACnD,CACF,CAAC,CAAE,CAACA,iBAAiB,CAAEF,WAAW,CAAC,CAAC,CAEpC;AACApD,SAAS,CAAC,IAAM,CACd,GAAIyB,YAAY,CAAE,CAChB,KAAM,CAAAmF,KAAK,CAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC,CAC7CF,KAAK,CAACG,GAAG,CAAGtF,YAAY,CACxBmF,KAAK,CAACI,OAAO,CAAG,UAAU,CAC1BJ,KAAK,CAACK,gBAAgB,CAAG,IAAM,CAC7B5B,gBAAgB,CAACuB,KAAK,CAACM,QAAQ,CAAC,CAChC;AACA,GAAIN,KAAK,CAACO,2BAA2B,GAAKC,SAAS,CAAE,CACnD;AACA,GAAI,CACF,KAAM,CAAAC,MAAM,CAAGT,KAAK,CAACU,WAAW,EAAKV,KAAK,CAACW,aAAa,EAAIX,KAAK,CAACW,aAAa,CAAC,CAAC,CAACC,cAAc,CAAC,CAAE,CACnG,GAAIH,MAAM,EAAIA,MAAM,CAACV,MAAM,CAAG,CAAC,EAAIU,MAAM,CAAC,CAAC,CAAC,CAACI,WAAW,CAAE,CACxD,KAAM,CAAAC,QAAQ,CAAGL,MAAM,CAAC,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CACxC,GAAIC,QAAQ,CAACC,SAAS,CAAE,CACtBpC,WAAW,CAACmC,QAAQ,CAACC,SAAS,CAAC,CACjC,CACF,CACF,CAAE,MAAOC,CAAC,CAAE,CAAC,CACf,CACF,CAAC,CACH,CACF,CAAC,CAAE,CAACnG,YAAY,CAAC,CAAC,CAElB;AACA,KAAM,CAAAoG,oBAAoB,CAAGzC,aAAa,EAAIE,QAAQ,CAAGwC,IAAI,CAACC,KAAK,CAAC3C,aAAa,CAAGE,QAAQ,CAAC,CAAG,IAAI,CAEpG;AACAtF,SAAS,CAAC,IAAM,CACd,MAAO,IAAM,CACX,GAAIsE,SAAS,CAAC+B,OAAO,CAAE,CACrB/B,SAAS,CAAC+B,OAAO,CAAC2B,KAAK,CAAC,CAAC,CAC3B,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGpI,QAAQ,CAAC,EAAE,CAAC,CAE1C;AACA,KAAM,CAAAqI,iBAAiB,CAAIP,CAAC,EAAK,CAC/B,KAAM,CAAAQ,IAAI,CAAGR,CAAC,CAACS,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAC9B,GAAIF,IAAI,CAAE,CACR;AACA,KAAM,CAAAG,UAAU,CAAG3H,kBAAkB,CAACwH,IAAI,CAAE,OAAO,CAAE,wBAAwB,CAAC,CAC9E,GAAI,CAACG,UAAU,CAACC,OAAO,CAAE,CACvB3H,uBAAuB,CAAC0H,UAAU,CAACE,YAAY,CAAEzG,QAAQ,CAAC,CAC1D;AACA,GAAI4F,CAAC,CAACS,MAAM,CAAE,CACZT,CAAC,CAACS,MAAM,CAAC5C,KAAK,CAAG,EAAE,CACrB,CACA,OACF,CAEA;AACAzD,QAAQ,CAAC,EAAE,CAAC,CAEZ,KAAM,CAAA4E,KAAK,CAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC,CAC7CF,KAAK,CAACI,OAAO,CAAG,UAAU,CAC1BJ,KAAK,CAACK,gBAAgB,CAAG,IAAM,CAC7B,GAAIL,KAAK,CAACM,QAAQ,CAAG,EAAE,CAAE,CACvBgB,UAAU,CAAC,wEAAwE,CAAC,CACpF1G,YAAY,CAAC,IAAI,CAAC,CAClBE,eAAe,CAAC,IAAI,CAAC,CACrBE,iBAAiB,CAAC,IAAI,CAAC,CACvBqC,eAAe,CAAC,IAAI,CAAC,CACrBF,gBAAgB,CAAC,EAAE,CAAC,CACpB/B,QAAQ,CAAC,EAAE,CAAC,CACZ,GAAIwC,YAAY,CAAC6B,OAAO,CAAE7B,YAAY,CAAC6B,OAAO,CAACZ,KAAK,CAAG,EAAE,CAC3D,CAAC,IAAM,CACLyC,UAAU,CAAC,EAAE,CAAC,CACd1G,YAAY,CAAC4G,IAAI,CAAC,CAClB1G,eAAe,CAACgH,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC,CAAC,CAC1CxG,iBAAiB,CAAC,IAAI,CAAC,CACvBqC,eAAe,CAAC,IAAI,CAAC,CACrBF,gBAAgB,CAAC,EAAE,CAAC,CACpB/B,QAAQ,CAAC,EAAE,CAAC,CACd,CACF,CAAC,CACD4E,KAAK,CAACG,GAAG,CAAG2B,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC,CACvC,CACF,CAAC,CAED;AACA,KAAM,CAAAQ,YAAY,CAAGA,CAAA,GAAM,CACzBlG,eAAe,CAAC,CAACD,YAAY,CAAC,CAC9B,GAAI,CAACA,YAAY,CAAE,CACjBjB,YAAY,CAAC,IAAI,CAAC,CAClBE,eAAe,CAAC,IAAI,CAAC,CACrBE,iBAAiB,CAAC,IAAI,CAAC,CACvBqC,eAAe,CAAC,IAAI,CAAC,CACrBF,gBAAgB,CAAC,EAAE,CAAC,CACpB/B,QAAQ,CAAC,EAAE,CAAC,CACd,CACF,CAAC,CAED;AACA,KAAM,CAAA6G,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvCzC,OAAO,CAAC0C,GAAG,CAAC,6BAA6B,CAAC,CAC1C,GAAI,CAACvE,SAAS,CAAC8B,OAAO,EAAI,CAAC9B,SAAS,CAAC8B,OAAO,CAAC0C,MAAM,CAAE,CACnD/G,QAAQ,CAAC,sBAAsB,CAAC,CAChC,OACF,CAEA,GAAI,CACFkB,iBAAiB,CAAC,EAAE,CAAC,CACrBC,iBAAiB,CAACkD,OAAO,CAAG,EAAE,CAC9BrD,gBAAgB,CAAC,CAAC,CAAC,CACnBF,cAAc,CAAC,IAAI,CAAC,CACpBd,QAAQ,CAAC,EAAE,CAAC,CAEZ,KAAM,CAAAgH,aAAa,CAAG,GAAI,CAAAC,aAAa,CAAC1E,SAAS,CAAC8B,OAAO,CAAC0C,MAAM,CAAE,CAChEG,QAAQ,CAAE,YACZ,CAAC,CAAC,CAEFxE,gBAAgB,CAAC2B,OAAO,CAAG2C,aAAa,CAExCA,aAAa,CAACG,eAAe,CAAIC,KAAK,EAAK,CACzChD,OAAO,CAAC0C,GAAG,CAAC,8BAA8B,CAAEM,KAAK,CAACC,IAAI,CAACC,IAAI,CAAC,CAC5D,GAAIF,KAAK,CAACC,IAAI,CAACC,IAAI,CAAG,CAAC,CAAE,CACvBpG,iBAAiB,CAACqD,IAAI,EAAI,CACxB,KAAM,CAAAgD,OAAO,CAAG,CAAC,GAAGhD,IAAI,CAAE6C,KAAK,CAACC,IAAI,CAAC,CACrClG,iBAAiB,CAACkD,OAAO,CAAGkD,OAAO,CAAE;AACrC,MAAO,CAAAA,OAAO,CAChB,CAAC,CAAC,CACJ,CACF,CAAC,CAEDP,aAAa,CAACQ,MAAM,CAAG,IAAM,CAC3BpD,OAAO,CAAC0C,GAAG,CAAC,4BAA4B,CAAC,CACzC;AACA,KAAM,CAAAW,MAAM,CAAGtG,iBAAiB,CAACkD,OAAO,CACxC;AACAD,OAAO,CAAC0C,GAAG,CAAC,gCAAgC,CAAEW,MAAM,CAAC9C,MAAM,CAAC,CAC5D,GAAI,CAAA+C,SAAS,CAAG,CAAC,CACjBD,MAAM,CAACE,OAAO,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CACvBzD,OAAO,CAAC0C,GAAG,CAAC,SAASe,CAAC,QAAQ,CAAED,CAAC,CAACN,IAAI,CAAC,CACvCI,SAAS,EAAIE,CAAC,CAACN,IAAI,CACrB,CAAC,CAAC,CACFlD,OAAO,CAAC0C,GAAG,CAAC,sBAAsB,CAAEY,SAAS,CAAC,CAC9C,KAAM,CAAAI,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACN,MAAM,CAAE,CAAEO,IAAI,CAAE,YAAa,CAAC,CAAC,CACrD,KAAM,CAAA5B,IAAI,CAAG,GAAI,CAAA6B,IAAI,CAAC,CAACH,IAAI,CAAC,CAAE,kBAAkBI,IAAI,CAACC,GAAG,CAAC,CAAC,OAAO,CAAE,CAAEH,IAAI,CAAE,YAAa,CAAC,CAAC,CAC1FxI,YAAY,CAAC4G,IAAI,CAAC,CAClB1G,eAAe,CAACgH,GAAG,CAACC,eAAe,CAACmB,IAAI,CAAC,CAAC,CAC1ChH,cAAc,CAAC,KAAK,CAAC,CACrBE,gBAAgB,CAAC,CAAC,CAAC,CACnB;AACAG,iBAAiB,CAACkD,OAAO,CAAG,EAAE,CAC9BnD,iBAAiB,CAAC,EAAE,CAAC,CACvB,CAAC,CAED8F,aAAa,CAACoB,OAAO,CAAG,IAAM,CAC5BhE,OAAO,CAAC0C,GAAG,CAAC,6BAA6B,CAAC,CAC5C,CAAC,CACDE,aAAa,CAACqB,OAAO,CAAIzC,CAAC,EAAK,CAC7BxB,OAAO,CAACrE,KAAK,CAAC,uBAAuB,CAAE6F,CAAC,CAAC,CAC3C,CAAC,CAEDxB,OAAO,CAAC0C,GAAG,CAAC,mCAAmC,CAAC,CAChDE,aAAa,CAACsB,KAAK,CAAC,IAAI,CAAC,CAAE;AAC7B,CAAE,MAAOvI,KAAK,CAAE,CACdC,QAAQ,CAAC,6BAA6B,CAAGD,KAAK,CAACwI,OAAO,CAAC,CACvDzH,cAAc,CAAC,KAAK,CAAC,CACvB,CACF,CAAC,CAED;AACA,KAAM,CAAA0D,mBAAmB,CAAGA,CAAA,GAAM,CAChCJ,OAAO,CAAC0C,GAAG,CAAC,4BAA4B,CAAC,CACzC,GAAIpE,gBAAgB,CAAC2B,OAAO,EAAIxD,WAAW,CAAE,CAC3C6B,gBAAgB,CAAC2B,OAAO,CAACmE,IAAI,CAAC,CAAC,CAC/B1H,cAAc,CAAC,KAAK,CAAC,CACrBE,gBAAgB,CAAC,CAAC,CAAC,CACrB,CACF,CAAC,CAED;AACA,KAAM,CAAAyH,uBAAuB,CAAGA,CAAA,GAAM,CACpC7H,oBAAoB,CAAC2D,IAAI,EAAIA,IAAI,GAAK,aAAa,CAAG,MAAM,CAAG,aAAa,CAAC,CAC/E,CAAC,CAED;AACA,KAAM,CAAAmE,oBAAoB,CAAGA,CAAA,GAAM,CACjC,MAAQ,CAAAnI,WAAW,GAAK,OAAO,EAAIhB,SAAS,EACpCgB,WAAW,GAAK,QAAQ,EAAIhB,SAAU,CAChD,CAAC,CAED;AACA,KAAM,CAAAoJ,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CAACD,oBAAoB,CAAC,CAAC,CAAE,CAC3B1I,QAAQ,CAAC,mCAAmC,CAAC,CAC7C,OACF,CAEA;AACAF,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CACZE,eAAe,CAAC,IAAI,CAAC,CACrBE,aAAa,CAAC,KAAK,CAAC,CACpBqB,cAAc,CAAC,IAAI,CAAC,CACpBE,YAAY,CAAC,KAAK,CAAC,CACnBN,cAAc,CAAC,EAAE,CAAC,CAClBE,oBAAoB,CAAC,CAAC,CAAC,CACvBM,qBAAqB,CAAC,CAAC,CAAC,CACxBE,gBAAgB,CAAC,EAAE,CAAC,CACpBI,oBAAoB,CAAC,EAAE,CAAC,CACxBvC,iBAAiB,CAAC,IAAI,CAAC,CACvBqC,eAAe,CAAC,IAAI,CAAC,CACrBI,cAAc,CAAC,KAAK,CAAC,CAErB;AACAC,SAAS,CAAC+B,OAAO,CAAG,GAAI,CAAAuE,eAAe,CAAC,CAAC,CAEzC,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,CAAExJ,SAAS,CAAC,CACnCsJ,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAE1J,aAAa,CAAC,CAC/CwJ,QAAQ,CAACE,MAAM,CAAC,aAAa,CAAE1I,WAAW,CAAC,CAC3C,KAAM,CAAA2I,UAAU,CAAGC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CACjD,KAAM,CAAAC,IAAI,CAAGH,UAAU,CAAGI,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC,CAAG,IAAI,CACvDH,QAAQ,CAACE,MAAM,CAAC,UAAU,CAAE,CAAAI,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEG,QAAQ,GAAI,SAAS,CAAC,CACxDT,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAE,CAAAI,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEI,IAAI,GAAI,SAAS,CAAC,CAEhDnF,OAAO,CAAC0C,GAAG,CAAC,uCAAuC,CAAEzH,aAAa,CAAC,CAEnE,KAAM,CAAAmK,MAAM,CAAG,4BAA4B,CAE3C,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAACF,MAAM,CAAE,CACnCG,MAAM,CAAE,MAAM,CACdC,IAAI,CAAEf,QAAQ,CACdgB,MAAM,CAAEvH,SAAS,CAAC+B,OAAO,CAACwF,MAC5B,CAAC,CAAC,CAEF,GAAI,CAACJ,QAAQ,CAACK,EAAE,CAAE,CAChB,KAAM,IAAI,CAAAC,KAAK,CAAC,uBAAuBN,QAAQ,CAACO,MAAM,EAAE,CAAC,CAC3D,CAEA,KAAM,CAAAC,MAAM,CAAGR,QAAQ,CAACG,IAAI,CAACM,SAAS,CAAC,CAAC,CACxC,KAAM,CAAAC,OAAO,CAAG,GAAI,CAAAC,WAAW,CAAC,CAAC,CACjC,GAAI,CAAAC,MAAM,CAAG,EAAE,CAEf;AACA,KAAM,CAAAC,gBAAgB,CAAIC,UAAU,EAAK,CACvC,GAAIA,UAAU,EAAIC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,EAAIA,UAAU,CAAC5F,MAAM,CAAG,CAAC,CAAE,CACpE5C,gBAAgB,CAACwC,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAE,GAAGgG,UAAU,CAAC,CAAC,CACpD,CACF,CAAC,CAED,KAAM,CAAAG,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF,MAAO,IAAI,CAAE,CACX,KAAM,CAAEC,IAAI,CAAElH,KAAM,CAAC,CAAG,KAAM,CAAAwG,MAAM,CAACW,IAAI,CAAC,CAAC,CAC3C,GAAID,IAAI,CAAE,CACRvG,OAAO,CAAC0C,GAAG,CAAC,wBAAwB,CAAC,CACrC5G,eAAe,CAAC,KAAK,CAAC,CACtBJ,UAAU,CAAC,KAAK,CAAC,CACjB2B,cAAc,CAAC,KAAK,CAAC,CACrB,MACF,CAEA4I,MAAM,EAAIF,OAAO,CAACU,MAAM,CAACpH,KAAK,CAAE,CAAEsD,MAAM,CAAE,IAAK,CAAC,CAAC,CACjD,GAAI,CAAA+D,KAAK,CAAGT,MAAM,CAACU,KAAK,CAAC,IAAI,CAAC,CAC9B;AACAV,MAAM,CAAGS,KAAK,CAACE,GAAG,CAAC,CAAC,CAEpB,IAAK,KAAM,CAAAC,IAAI,GAAI,CAAAH,KAAK,CAAE,CACxB,GAAIG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAE,CAC7B,GAAI,CACF,KAAM,CAAA7D,IAAI,CAAG+B,IAAI,CAACC,KAAK,CAAC4B,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC,CAC1C;AACA/G,OAAO,CAAC0C,GAAG,CAAC,WAAW,CAAEO,IAAI,CAAC,CAE9B;AACA,GAAIA,IAAI,CAAC+D,OAAO,GAAK,KAAK,CAAE,CAC1BpL,QAAQ,CAACqH,IAAI,CAACkB,OAAO,EAAI,yBAAyB,CAAC,CACnDrI,eAAe,CAAC,KAAK,CAAC,CACtBJ,UAAU,CAAC,KAAK,CAAC,CACjB2B,cAAc,CAAC,KAAK,CAAC,CACrB,OACF,CAEA;AACA,GAAI4F,IAAI,CAACgE,QAAQ,GAAKjG,SAAS,EAAInC,gBAAgB,CAAE,CACnDpB,qBAAqB,CAACwF,IAAI,CAACgE,QAAQ,CAAC,CACpC,GAAI,CAACjJ,WAAW,EAAIiF,IAAI,CAACgE,QAAQ,CAAG,CAAC,CAAE,CACrChJ,cAAc,CAAC,IAAI,CAAC,CACtB,CACF,CAAC,IAAM,IAAIgF,IAAI,CAACiE,WAAW,GAAKlG,SAAS,EAAInC,gBAAgB,CAAE,CAC7D;AACA,KAAM,CAAAoI,QAAQ,CAAIhE,IAAI,CAACiE,WAAW,CAAGvI,WAAW,CAAI,GAAG,CACvDlB,qBAAqB,CAACwJ,QAAQ,CAAC,CAC/B,GAAI,CAACjJ,WAAW,EAAIiJ,QAAQ,CAAG,CAAC,CAAE,CAChChJ,cAAc,CAAC,IAAI,CAAC,CACtB,CACF,CAAC,IAAM,IAAIgF,IAAI,CAACiE,WAAW,GAAKlG,SAAS,EAAIS,oBAAoB,CAAE,CACjE;AACA,KAAM,CAAAwF,QAAQ,CAAIhE,IAAI,CAACiE,WAAW,CAAGzF,oBAAoB,CAAI,GAAG,CAChEhE,qBAAqB,CAACwJ,QAAQ,CAAC,CAC/B,GAAI,CAACjJ,WAAW,EAAIiJ,QAAQ,CAAG,CAAC,CAAE,CAChChJ,cAAc,CAAC,IAAI,CAAC,CACtB,CACF,CAEA;AACA,GAAIgF,IAAI,CAACkE,KAAK,EAAI,MAAO,CAAAlE,IAAI,CAACkE,KAAK,GAAK,QAAQ,EAAIlE,IAAI,CAACkE,KAAK,CAAC5G,MAAM,CAAG,IAAI,CAAE,CAC5EtD,cAAc,CAACkD,IAAI,EAAI,CACrB,KAAM,CAAAiH,SAAS,CAAG,CAAC,GAAGjH,IAAI,CAAE8C,IAAI,CAACkE,KAAK,CAAC,CACvC,MAAO,CAAAC,SAAS,CAClB,CAAC,CAAC,CACF5L,iBAAiB,CAACyH,IAAI,CAACkE,KAAK,CAAC,CAC7BhK,oBAAoB,CAACgD,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CACtC,GAAI/C,WAAW,CAAE,CACfC,cAAc,CAAC,KAAK,CAAC,CACvB,CACF,CAEA;AACA,GAAI4F,IAAI,CAACkD,UAAU,EAAIlD,IAAI,CAACkD,UAAU,CAAC5F,MAAM,CAAG,CAAC,CAAE,CACjDxC,oBAAoB,CAACkF,IAAI,CAACkD,UAAU,CAAC,CACrCD,gBAAgB,CAACjD,IAAI,CAACkD,UAAU,CAAC,CACnC,CAEA;AACA,GAAIlD,IAAI,CAACoE,cAAc,CAAE,CACvBxJ,eAAe,CAACoF,IAAI,CAAC,CACrBtF,gBAAgB,CAACsF,IAAI,CAACoE,cAAc,CAAC,CACrCvL,eAAe,CAAC,KAAK,CAAC,CACtBJ,UAAU,CAAC,KAAK,CAAC,CACjB2B,cAAc,CAAC,KAAK,CAAC,CACrBI,qBAAqB,CAAC,GAAG,CAAC,CAC1BN,oBAAoB,CAAC,CAAC,CAAC,CACvBI,YAAY,CAAC,KAAK,CAAC,CACnByC,OAAO,CAAC0C,GAAG,CAAC,4BAA4B,CAAC,CACzC,OACF,CAEA;AACA,GAAIO,IAAI,CAACqE,GAAG,CAAE,CACZtH,OAAO,CAAC0C,GAAG,CAAC,qBAAqB,CAAC,CAClC5G,eAAe,CAAC,KAAK,CAAC,CACtBJ,UAAU,CAAC,KAAK,CAAC,CACjB2B,cAAc,CAAC,KAAK,CAAC,CACrB,OACF,CAEA;AACA,GAAI4F,IAAI,CAACsE,YAAY,GAAKvG,SAAS,CAAE,CACnCpC,cAAc,CAACqE,IAAI,CAACsE,YAAY,CAAC,CACnC,CACF,CAAE,MAAOC,UAAU,CAAE,CACnBxH,OAAO,CAACyH,IAAI,CAAC,yBAAyB,CAAED,UAAU,CAAC,CACrD,CACF,CACF,CACF,CACF,CAAE,MAAOE,WAAW,CAAE,CACpB,GAAIA,WAAW,CAACC,IAAI,GAAK,YAAY,CAAE,CACrC3H,OAAO,CAAC0C,GAAG,CAAC,wBAAwB,CAAC,CACvC,CAAC,IAAM,CACL1C,OAAO,CAACrE,KAAK,CAAC,0BAA0B,CAAE+L,WAAW,CAAC,CACtD9L,QAAQ,CAAC,+BAA+B,CAAC,CAC3C,CACAE,eAAe,CAAC,KAAK,CAAC,CACtBJ,UAAU,CAAC,KAAK,CAAC,CACjB2B,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC,OAAS,CACR,GAAIwI,MAAM,CAAE,CACV,GAAI,CACFA,MAAM,CAAC+B,WAAW,CAAC,CAAC,CACtB,CAAE,MAAOpG,CAAC,CAAE,CACVxB,OAAO,CAACyH,IAAI,CAAC,8BAA8B,CAAEjG,CAAC,CAAC,CACjD,CACF,CACF,CACF,CAAC,CAED,KAAM,CAAA8E,aAAa,CAAC,CAAC,CACvB,CAAE,MAAO3K,KAAK,CAAE,CACdqE,OAAO,CAACrE,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/CC,QAAQ,CAACD,KAAK,CAACwI,OAAO,EAAI,yBAAyB,CAAC,CACpDzI,UAAU,CAAC,KAAK,CAAC,CACjBI,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED;AACA,KAAM,CAAA+L,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAI,CACF,KAAM,CAAAxN,KAAK,CAACyN,IAAI,CAAC,qCAAqC,CAAC,CAEvDhM,eAAe,CAAC,KAAK,CAAC,CACtBE,aAAa,CAAC,IAAI,CAAC,CACnBqB,cAAc,CAAC,KAAK,CAAC,CACrBE,YAAY,CAAC,KAAK,CAAC,CACnB7B,UAAU,CAAC,KAAK,CAAC,CACjBE,QAAQ,CAAC,0BAA0B,CAAC,CACtC,CAAE,MAAOD,KAAK,CAAE,CACdqE,OAAO,CAACrE,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClDC,QAAQ,CAAC,2BAA2B,CAAC,CACvC,CACF,CAAC,CAED;AACA,KAAM,CAAAmM,WAAW,CAAGA,CAAA,GAAM,CACxB3M,YAAY,CAAC,IAAI,CAAC,CAClBE,eAAe,CAAC,IAAI,CAAC,CACrBE,iBAAiB,CAAC,IAAI,CAAC,CACvBqC,eAAe,CAAC,IAAI,CAAC,CACrBF,gBAAgB,CAAC,EAAE,CAAC,CACpBI,oBAAoB,CAAC,EAAE,CAAC,CACxBd,cAAc,CAAC,EAAE,CAAC,CAClBE,oBAAoB,CAAC,CAAC,CAAC,CACvBrB,eAAe,CAAC,KAAK,CAAC,CACtBE,aAAa,CAAC,KAAK,CAAC,CACpBqB,cAAc,CAAC,KAAK,CAAC,CACrBE,YAAY,CAAC,KAAK,CAAC,CACnBE,qBAAqB,CAAC,CAAC,CAAC,CACxB7B,QAAQ,CAAC,EAAE,CAAC,CACZV,gBAAgB,CAAC,KAAK,CAAC,CACvB+C,cAAc,CAAC,KAAK,CAAC,CAAE;AACvB,GAAIG,YAAY,CAAC6B,OAAO,CAAE,CACxB7B,YAAY,CAAC6B,OAAO,CAACZ,KAAK,CAAG,EAAE,CACjC,CACF,CAAC,CAED;AACA,KAAM,CAAA2I,eAAe,CAAGA,CAAA,GAAMzK,YAAY,CAAC,CAACD,SAAS,CAAC,CACtD,KAAM,CAAA2K,YAAY,CAAGA,CAAA,GAAM9K,oBAAoB,CAACuE,IAAI,CAACwG,GAAG,CAAChL,iBAAiB,CAAG,CAAC,CAAE,CAAC,CAAC,CAAC,CACnF,KAAM,CAAAiL,aAAa,CAAGA,CAAA,GAAMhL,oBAAoB,CAACuE,IAAI,CAAC0G,GAAG,CAAClL,iBAAiB,CAAG,CAAC,CAAEF,WAAW,CAACuD,MAAM,CAAG,CAAC,CAAC,CAAC,CAEzG;AACA,KAAM,CAAA8H,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,OAAO,CAAG,CAAC,CAAC,CAClB5K,aAAa,CAAC6F,OAAO,CAACgF,GAAG,EAAI,CAC3BD,OAAO,CAACC,GAAG,CAAC3E,IAAI,CAAC,CAAG,CAAC0E,OAAO,CAACC,GAAG,CAAC3E,IAAI,CAAC,EAAI,CAAC,EAAI,CAAC,CAClD,CAAC,CAAC,CACF,MAAO,CAAA0E,OAAO,CAChB,CAAC,CAED;AACA,KAAM,CAAAE,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,GAAI5K,YAAY,CAAE,CAChB,MAAO,CACL6K,gBAAgB,CAAE7K,YAAY,CAAC8K,uBAAuB,EAAIhL,aAAa,CAAC6C,MAAM,CAC9EoI,eAAe,CAAE/K,YAAY,CAACgL,sBAAsB,EAAIlL,aAAa,CAAC6C,MAAM,CAC5EsI,iBAAiB,CAAE,CAACjL,YAAY,CAACgL,sBAAsB,EAAIlL,aAAa,CAAC6C,MAAM,GAAK3C,YAAY,CAAC8K,uBAAuB,EAAIhL,aAAa,CAAC6C,MAAM,CAClJ,CAAC,CACH,CACA,MAAO,CACLkI,gBAAgB,CAAE/K,aAAa,CAAC6C,MAAM,CACtCoI,eAAe,CAAEjL,aAAa,CAAC6C,MAAM,CACrCsI,iBAAiB,CAAE,CACrB,CAAC,CACH,CAAC,CAED;AACA,KAAM,CAAAC,UAAU,CAAIC,OAAO,EAAK,CAC9B,KAAM,CAAAC,IAAI,CAAGtH,IAAI,CAACuH,KAAK,CAACF,OAAO,CAAG,EAAE,CAAC,CACrC,KAAM,CAAAG,IAAI,CAAGH,OAAO,CAAG,EAAE,CACzB,MAAO,GAAGC,IAAI,IAAIE,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CACtD,CAAC,CAED,mBACEzO,IAAA,QAAK0O,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrCzO,KAAA,CAACV,GAAG,EAAAmP,QAAA,eACF3O,IAAA,CAACP,GAAG,EAACmP,EAAE,CAAE,CAAE,CAAAD,QAAA,cACTzO,KAAA,CAAChB,IAAI,EAACwP,SAAS,CAAC,MAAM,CAAAC,QAAA,eACpB3O,IAAA,CAACd,IAAI,CAAC2P,MAAM,EAACH,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cAC5C3O,IAAA,OAAI0O,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,wBAAsB,CAAI,CAAC,CACrC,CAAC,cACdzO,KAAA,CAAChB,IAAI,CAAC4P,IAAI,EAAAH,QAAA,EACP3N,KAAK,eACJhB,IAAA,CAACX,KAAK,EAAC0P,OAAO,CAAC,QAAQ,CAACL,SAAS,CAAC,MAAM,CAAAC,QAAA,CACrC3N,KAAK,CACD,CACR,CACAkG,OAAO,eACNlH,IAAA,CAACX,KAAK,EAAC0P,OAAO,CAAC,SAAS,CAACL,SAAS,CAAC,MAAM,CAAAC,QAAA,CACtCzH,OAAO,CACH,CACR,cAGDhH,KAAA,CAACd,IAAI,CAAC4P,KAAK,EAACN,SAAS,CAAC,MAAM,CAAAC,QAAA,eAC1B3O,IAAA,CAACZ,IAAI,CAAC6P,KAAK,EAAAN,QAAA,CAAC,iBAAe,CAAY,CAAC,cACxC3O,IAAA,CAACZ,IAAI,CAAC8P,MAAM,EACVxK,KAAK,CAAEpE,aAAc,CACrB6O,QAAQ,CAAGtI,CAAC,EAAKtG,gBAAgB,CAACsG,CAAC,CAACS,MAAM,CAAC5C,KAAK,CAAE,CAClD0K,QAAQ,CAAElO,YAAa,CAAAyN,QAAA,CAEtBlK,YAAY,CAAC4K,GAAG,CAACC,MAAM,eACtBtP,IAAA,WAA2B0E,KAAK,CAAE4K,MAAM,CAAC5K,KAAM,CAAAiK,QAAA,CAC5CW,MAAM,CAAC3K,KAAK,EADF2K,MAAM,CAAC5K,KAEZ,CACT,CAAC,CACS,CAAC,EACJ,CAAC,cAGbxE,KAAA,CAACd,IAAI,CAAC4P,KAAK,EAACN,SAAS,CAAC,MAAM,CAAAC,QAAA,eAC1B3O,IAAA,CAACZ,IAAI,CAAC6P,KAAK,EAAAN,QAAA,CAAC,cAAY,CAAY,CAAC,cACrCzO,KAAA,CAACd,IAAI,CAAC8P,MAAM,EACVxK,KAAK,CAAElD,WAAY,CACnB2N,QAAQ,CAAGtI,CAAC,EAAKpF,cAAc,CAACoF,CAAC,CAACS,MAAM,CAAC5C,KAAK,CAAE,CAChD0K,QAAQ,CAAElO,YAAa,CAAAyN,QAAA,eAEvB3O,IAAA,WAAQ0E,KAAK,CAAC,OAAO,CAAAiK,QAAA,CAAC,cAAY,CAAQ,CAAC,cAC3C3O,IAAA,WAAQ0E,KAAK,CAAC,QAAQ,CAAAiK,QAAA,CAAC,uBAAqB,CAAQ,CAAC,EAC1C,CAAC,EACJ,CAAC,CAGZnN,WAAW,GAAK,OAAO,eACtBtB,KAAA,CAACd,IAAI,CAAC4P,KAAK,EAACN,SAAS,CAAC,MAAM,CAAAC,QAAA,eAC1B3O,IAAA,CAACZ,IAAI,CAAC6P,KAAK,EAAAN,QAAA,CAAC,cAAY,CAAY,CAAC,cACrC3O,IAAA,CAACZ,IAAI,CAACmQ,OAAO,EACXtG,IAAI,CAAC,MAAM,CACXuG,MAAM,CAAC,SAAS,CAChBL,QAAQ,CAAE/H,iBAAkB,CAC5BqI,GAAG,CAAEhM,YAAa,CAClB2L,QAAQ,CAAElO,YAAa,CACxB,CAAC,CACDR,YAAY,eACXV,IAAA,QAAK0O,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnB3O,IAAA,UACEgG,GAAG,CAAEtF,YAAa,CAClBgP,QAAQ,MACRhB,SAAS,CAAC,eAAe,CACzBiB,KAAK,CAAE,CAAEC,SAAS,CAAE,OAAQ,CAAE,CAC/B,CAAC,CACC,CACN,EACS,CACb,CAGApO,WAAW,GAAK,QAAQ,eACvBtB,KAAA,QAAKwO,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBzO,KAAA,QAAKwO,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC3O,IAAA,CAACb,MAAM,EACL4P,OAAO,CAAErN,YAAY,CAAG,QAAQ,CAAG,MAAO,CAC1CmO,OAAO,CAAEhI,YAAa,CACtBuH,QAAQ,CAAElO,YAAa,CAAAyN,QAAA,CAEtBjN,YAAY,CAAG,aAAa,CAAG,cAAc,CACxC,CAAC,CACRkC,QAAQ,EAAIlC,YAAY,eACvB1B,IAAA,CAACb,MAAM,EACL4P,OAAO,CAAC,mBAAmB,CAC3Bc,OAAO,CAAEnG,uBAAwB,CACjCnB,IAAI,CAAC,IAAI,CAAAoG,QAAA,CACV,eAED,CAAQ,CACT,EACE,CAAC,CAELjN,YAAY,eACXxB,KAAA,QAAKwO,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B3O,IAAA,CAACL,MAAM,EACLmQ,KAAK,CAAE,KAAM,CACbL,GAAG,CAAEjM,SAAU,CACfuM,gBAAgB,CAAC,YAAY,CAC7BC,KAAK,CAAC,MAAM,CACZC,MAAM,CAAC,MAAM,CACbC,gBAAgB,CAAE,CAChBF,KAAK,CAAE,GAAG,CACVC,MAAM,CAAE,GAAG,CACXE,UAAU,CAAEvO,iBACd,CAAE,CACH,CAAC,cAEF5B,IAAA,QAAK0O,SAAS,CAAC,MAAM,CAAAC,QAAA,CAClB,CAAC7M,WAAW,cACX9B,IAAA,CAACb,MAAM,EACL4P,OAAO,CAAC,SAAS,CACjBc,OAAO,CAAE/H,oBAAqB,CAC9BsH,QAAQ,CAAElO,YAAa,CAAAyN,QAAA,CACxB,iBAED,CAAQ,CAAC,cAETzO,KAAA,QAAKwO,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9C3O,IAAA,CAACb,MAAM,EACL4P,OAAO,CAAC,QAAQ,CAChBc,OAAO,CAAEpK,mBAAoB,CAAAkJ,QAAA,CAC9B,gBAED,CAAQ,CAAC,cACTzO,KAAA,SAAMwO,SAAS,CAAC,aAAa,CAAAC,QAAA,EAAC,aACjB,CAACR,UAAU,CAACnM,aAAa,CAAC,CAAC,KAAG,CAACmM,UAAU,CAACpK,kBAAkB,CAAC,EACpE,CAAC,EACJ,CACN,CACE,CAAC,EACH,CACN,CAEArD,YAAY,eACXV,IAAA,QAAK0O,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnB3O,IAAA,UACEgG,GAAG,CAAEtF,YAAa,CAClBgP,QAAQ,MACRhB,SAAS,CAAC,eAAe,CACzBiB,KAAK,CAAE,CAAEC,SAAS,CAAE,OAAQ,CAAE,CAC/B,CAAC,CACC,CACN,EACE,CACN,cAGD1P,KAAA,QAAKwO,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B3O,IAAA,CAACb,MAAM,EACL4P,OAAO,CAAC,SAAS,CACjBc,OAAO,CAAEjG,aAAc,CACvBwF,QAAQ,CAAE,CAACzF,oBAAoB,CAAC,CAAC,EAAIzI,YAAa,CAAAyN,QAAA,CAEjD7N,OAAO,cACNZ,KAAA,CAAAE,SAAA,EAAAuO,QAAA,eACE3O,IAAA,CAACV,OAAO,EAACiJ,IAAI,CAAC,IAAI,CAACmG,SAAS,CAAC,MAAM,CAAE,CAAC,gBAExC,EAAE,CAAC,CAEH,eACD,CACK,CAAC,CAERxN,YAAY,eACXlB,IAAA,CAACb,MAAM,EACL4P,OAAO,CAAC,SAAS,CACjBc,OAAO,CAAE3C,oBAAqB,CAAAyB,QAAA,CAC/B,iBAED,CAAQ,CACT,cAED3O,IAAA,CAACb,MAAM,EACL4P,OAAO,CAAC,WAAW,CACnBc,OAAO,CAAEzC,WAAY,CACrBgC,QAAQ,CAAElO,YAAa,CAAAyN,QAAA,CACxB,OAED,CAAQ,CAAC,EACN,CAAC,CAGLzN,YAAY,eACXlB,IAAA,QAAK0O,SAAS,CAAC,MAAM,CAAAC,QAAA,CAClB,CAACzK,gBAAgB,EAAI4C,oBAAoB,GAAKjE,kBAAkB,CAAG,CAAC,EAAIsB,MAAM,CAACC,QAAQ,CAACvB,kBAAkB,CAAC,cAC1G3C,KAAA,CAAAE,SAAA,EAAAuO,QAAA,eACEzO,KAAA,QAAKwO,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7C3O,IAAA,SAAA2O,QAAA,CAAM,sBAAoB,CAAM,CAAC,cACjCzO,KAAA,SAAAyO,QAAA,EAAO5H,IAAI,CAACwG,GAAG,CAAC,CAAC,CAAExG,IAAI,CAAC0G,GAAG,CAAC,GAAG,CAAE5K,kBAAkB,CAAC,CAAC,CAACsC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,EAAM,CAAC,EACtE,CAAC,cACNnF,IAAA,QAAK0O,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B3O,IAAA,QACE0O,SAAS,CAAC,yDAAyD,CACnElE,IAAI,CAAC,aAAa,CAClBmF,KAAK,CAAE,CAAEK,KAAK,CAAE,GAAGjJ,IAAI,CAACwG,GAAG,CAAC,CAAC,CAAExG,IAAI,CAAC0G,GAAG,CAAC,GAAG,CAAE5K,kBAAkB,CAAC,CAAC,GAAI,CAAE,CACvE,gBAAekE,IAAI,CAACwG,GAAG,CAAC,CAAC,CAAExG,IAAI,CAAC0G,GAAG,CAAC,GAAG,CAAE5K,kBAAkB,CAAC,CAAE,CAC9D,gBAAc,GAAG,CACjB,gBAAc,KAAK,CACf,CAAC,CACJ,CAAC,EACN,CAAC,cAEH3C,KAAA,QAAKwO,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7C3O,IAAA,SAAA2O,QAAA,CAAM,eAAa,CAAM,CAAC,cAC1B3O,IAAA,QAAK0O,SAAS,CAAC,2BAA2B,CAACiB,KAAK,CAAE,CAAEM,MAAM,CAAE,MAAO,CAAE,CAAAtB,QAAA,cACnE3O,IAAA,QACE0O,SAAS,CAAC,yDAAyD,CACnElE,IAAI,CAAC,aAAa,CAClBmF,KAAK,CAAE,CAAEK,KAAK,CAAE,MAAM,CAAEI,eAAe,CAAE,SAAU,CAAE,CACrD,gBAAe,CAAE,CACjB,gBAAc,GAAG,CACjB,gBAAc,KAAK,CACf,CAAC,CACJ,CAAC,EACH,CACN,CACE,CACN,EACQ,CAAC,EACR,CAAC,CACJ,CAAC,cAENpQ,IAAA,CAACP,GAAG,EAACmP,EAAE,CAAE,CAAE,CAAAD,QAAA,CAER,CAAEtL,WAAW,EAAIN,aAAa,CAAC6C,MAAM,CAAG,CAAC,EAAK,CAAC1E,YAAY,EAAI+B,YAAY,EAAIF,aAAa,CAAC6C,MAAM,CAAG,CAAE,gBACvG1F,KAAA,CAAChB,IAAI,EAAAyP,QAAA,eACHzO,KAAA,CAAChB,IAAI,CAAC2P,MAAM,EAACH,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACzC3O,IAAA,OAAI0O,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,CAC1CzN,YAAY,eACXlB,IAAA,UAAO0O,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,wDAEjC,CAAO,CACR,CACA,CAACzN,YAAY,EAAI+B,YAAY,eAC5B/C,KAAA,UAAOwO,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC7B3O,IAAA,MAAA2O,QAAA,CAAG,sBAAoB,CAAG,CAAC,kCAC7B,EAAO,CACR,EACU,CAAC,cACdzO,KAAA,CAAChB,IAAI,CAAC4P,IAAI,EAAAH,QAAA,eAERzO,KAAA,QAAKwO,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC3O,IAAA,OAAA2O,QAAA,CAAI,oBAAkB,CAAI,CAAC,cAC3B3O,IAAA,QAAK0O,SAAS,CAAC,MAAM,CAAAC,QAAA,CAClB0B,MAAM,CAACC,OAAO,CAAC5C,mBAAmB,CAAC,CAAC,CAAC,CAAC2B,GAAG,CAACkB,IAAA,MAAC,CAACtH,IAAI,CAAEuH,KAAK,CAAC,CAAAD,IAAA,oBACvDrQ,KAAA,SAAiBwO,SAAS,CAAC,yBAAyB,CAAAC,QAAA,EACjD1F,IAAI,CAAC,IAAE,CAACuH,KAAK,GADLvH,IAEL,CAAC,EACR,CAAC,CACC,CAAC,cAGNjJ,IAAA,QAAK0O,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BzO,KAAA,UAAOwO,SAAS,CAAC,YAAY,CAAAC,QAAA,eAC3B3O,IAAA,WAAA2O,QAAA,CAAQ,iBAAe,CAAQ,CAAC,IAAC,CAAC,GAAG,cACrCzO,KAAA,SAAMwO,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,UAC9B,CAACd,gBAAgB,CAAC,CAAC,CAACC,gBAAgB,EACxC,CAAC,cACP5N,KAAA,SAAMwO,SAAS,CAAC,oBAAoB,CAAAC,QAAA,EAAC,gBACrB,CAACd,gBAAgB,CAAC,CAAC,CAACG,eAAe,EAC7C,CAAC,cACP9N,KAAA,SAAMwO,SAAS,CAAC,kBAAkB,CAAAC,QAAA,EAAC,sBACb,CAACd,gBAAgB,CAAC,CAAC,CAACK,iBAAiB,EACrD,CAAC,EACF,CAAC,CACL,CAAC,EACH,CAAC,CAGL,CAAC,IAAM,CACN,KAAM,CAAAuC,iBAAiB,CAAG1N,aAAa,CAAC2N,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC1H,IAAI,GAAK,SAAS,CAAC,CACzE,KAAM,CAAA2H,eAAe,CAAG7N,aAAa,CAAC2N,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC1H,IAAI,CAAC4H,QAAQ,CAAC,OAAO,CAAC,CAAC,CAC3E,KAAM,CAAAC,cAAc,CAAG/N,aAAa,CAAC2N,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC1H,IAAI,CAAC4H,QAAQ,CAAC,MAAM,CAAC,CAAC,CAEzE,mBACE3Q,KAAA,QAAAyO,QAAA,EAEG,CAACrO,aAAa,GAAK,KAAK,EAAIA,aAAa,GAAK,UAAU,gBACvDJ,KAAA,QAAKwO,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3CzO,KAAA,OAAIwO,SAAS,CAAC,aAAa,CAAAC,QAAA,eACzB3O,IAAA,SAAM0O,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAC,oBAAG,CAAM,CAAC,sBACf,CAAC8B,iBAAiB,CAAC7K,MAAM,EAC1C,CAAC,CACJ6K,iBAAiB,CAAC7K,MAAM,CAAG,CAAC,cAC3B5F,IAAA,QAAK0O,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACxCzO,KAAA,CAACX,KAAK,EAACwR,OAAO,MAACC,QAAQ,MAACC,KAAK,MAAC1I,IAAI,CAAC,IAAI,CAAAoG,QAAA,eACrC3O,IAAA,UAAA2O,QAAA,cACEzO,KAAA,OAAAyO,QAAA,eACE3O,IAAA,OAAA2O,QAAA,CAAI,IAAE,CAAI,CAAC,cACX3O,IAAA,OAAA2O,QAAA,CAAI,eAAU,CAAI,CAAC,cACnB3O,IAAA,OAAA2O,QAAA,CAAI,YAAU,CAAI,CAAC,cACnB3O,IAAA,OAAA2O,QAAA,CAAI,iBAAY,CAAI,CAAC,cACrB3O,IAAA,OAAA2O,QAAA,CAAI,cAAY,CAAI,CAAC,EACnB,CAAC,CACA,CAAC,cACR3O,IAAA,UAAA2O,QAAA,CACG8B,iBAAiB,CAACpB,GAAG,CAAC,CAAC6B,SAAS,CAAEC,KAAK,gBACtCjR,KAAA,OAAAyO,QAAA,eACE3O,IAAA,OAAA2O,QAAA,CAAKuC,SAAS,CAACE,QAAQ,EAAID,KAAK,CAAG,CAAC,CAAK,CAAC,cAC1CnR,IAAA,OAAA2O,QAAA,CAAKuC,SAAS,CAACG,QAAQ,CAAGH,SAAS,CAACG,QAAQ,CAAClM,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAAK,CAAC,cACrEnF,IAAA,OAAA2O,QAAA,CAAKuC,SAAS,CAACI,QAAQ,CAAGJ,SAAS,CAACI,QAAQ,CAACnM,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAAK,CAAC,cACrEnF,IAAA,OAAA2O,QAAA,CAAKuC,SAAS,CAACK,MAAM,CAAGL,SAAS,CAACK,MAAM,CAACpM,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAAK,CAAC,cACjEnF,IAAA,OAAA2O,QAAA,CAAKuC,SAAS,CAACM,YAAY,EAAI,KAAK,CAAK,CAAC,GALnCL,KAML,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,cAENnR,IAAA,QAAK0O,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,sBAAoB,CAAK,CAC9D,EACE,CACN,CAGA,CAACrO,aAAa,GAAK,KAAK,EAAIA,aAAa,GAAK,kBAAkB,gBAC/DJ,KAAA,QAAKwO,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCzO,KAAA,OAAIwO,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC1B3O,IAAA,SAAM0O,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,oBAChB,CAACiC,eAAe,CAAChL,MAAM,EACtC,CAAC,CACJgL,eAAe,CAAChL,MAAM,CAAG,CAAC,cACzB5F,IAAA,QAAK0O,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACxCzO,KAAA,CAACX,KAAK,EAACwR,OAAO,MAACC,QAAQ,MAACC,KAAK,MAAC1I,IAAI,CAAC,IAAI,CAAAoG,QAAA,eACrC3O,IAAA,UAAA2O,QAAA,cACEzO,KAAA,OAAAyO,QAAA,eACE3O,IAAA,OAAA2O,QAAA,CAAI,IAAE,CAAI,CAAC,cACX3O,IAAA,OAAA2O,QAAA,CAAI,MAAI,CAAI,CAAC,cACb3O,IAAA,OAAA2O,QAAA,CAAI,eAAU,CAAI,CAAC,cACnB3O,IAAA,OAAA2O,QAAA,CAAI,YAAU,CAAI,CAAC,EACjB,CAAC,CACA,CAAC,cACR3O,IAAA,UAAA2O,QAAA,CACGiC,eAAe,CAACvB,GAAG,CAAC,CAAC6B,SAAS,CAAEC,KAAK,gBACpCjR,KAAA,OAAAyO,QAAA,eACE3O,IAAA,OAAA2O,QAAA,CAAKuC,SAAS,CAACE,QAAQ,EAAID,KAAK,CAAG,CAAC,CAAK,CAAC,cAC1CnR,IAAA,OAAA2O,QAAA,CAAKuC,SAAS,CAACjI,IAAI,CAAK,CAAC,cACzBjJ,IAAA,OAAA2O,QAAA,CAAKuC,SAAS,CAACG,QAAQ,CAAGH,SAAS,CAACG,QAAQ,CAAClM,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAAK,CAAC,cACrEnF,IAAA,OAAA2O,QAAA,CAAKuC,SAAS,CAACO,UAAU,EAAI,KAAK,CAAK,CAAC,GAJjCN,KAKL,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,cAENnR,IAAA,QAAK0O,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,oBAAkB,CAAK,CAC5D,EACE,CACN,CAGA,CAACrO,aAAa,GAAK,KAAK,EAAIA,aAAa,GAAK,OAAO,gBACpDJ,KAAA,QAAKwO,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxCzO,KAAA,OAAIwO,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC1B3O,IAAA,SAAM0O,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,mBACjB,CAACmC,cAAc,CAAClL,MAAM,EACpC,CAAC,CACJkL,cAAc,CAAClL,MAAM,CAAG,CAAC,cACxB5F,IAAA,QAAK0O,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACxCzO,KAAA,CAACX,KAAK,EAACwR,OAAO,MAACC,QAAQ,MAACC,KAAK,MAAC1I,IAAI,CAAC,IAAI,CAAAoG,QAAA,eACrC3O,IAAA,UAAA2O,QAAA,cACEzO,KAAA,OAAAyO,QAAA,eACE3O,IAAA,OAAA2O,QAAA,CAAI,IAAE,CAAI,CAAC,cACX3O,IAAA,OAAA2O,QAAA,CAAI,MAAI,CAAI,CAAC,cACb3O,IAAA,OAAA2O,QAAA,CAAI,WAAS,CAAI,CAAC,cAClB3O,IAAA,OAAA2O,QAAA,CAAI,QAAM,CAAI,CAAC,EACb,CAAC,CACA,CAAC,cACR3O,IAAA,UAAA2O,QAAA,CACGmC,cAAc,CAACzB,GAAG,CAAC,CAAC6B,SAAS,CAAEC,KAAK,gBACnCjR,KAAA,OAAAyO,QAAA,eACE3O,IAAA,OAAA2O,QAAA,CAAKuC,SAAS,CAACE,QAAQ,EAAID,KAAK,CAAG,CAAC,CAAK,CAAC,cAC1CnR,IAAA,OAAA2O,QAAA,CAAKuC,SAAS,CAACQ,SAAS,EAAI,eAAe,CAAK,CAAC,cACjD1R,IAAA,OAAA2O,QAAA,CAAKuC,SAAS,CAACS,SAAS,EAAIT,SAAS,CAACjI,IAAI,CAAK,CAAC,cAChDjJ,IAAA,OAAA2O,QAAA,CAAKuC,SAAS,CAACU,QAAQ,CAAGV,SAAS,CAACU,QAAQ,CAACzM,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAAK,CAAC,GAJ9DgM,KAKL,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,cAENnR,IAAA,QAAK0O,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,mBAAiB,CAAK,CAC3D,EACE,CACN,EACE,CAAC,CAEV,CAAC,EAAE,CAAC,EACK,CAAC,EACR,CACP,CACE,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtO,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}